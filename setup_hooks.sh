#!/bin/bash
# RemoCode Hook Setup Script
# Automatically configures Claude Code hooks with correct absolute paths

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CLAUDE_DIR="$HOME/.claude"
SETTINGS_FILE="$CLAUDE_DIR/settings.local.json"
TEMPLATE_FILE="$SCRIPT_DIR/.claude/settings.local.json.template"

echo "🚀 RemoCode Hook Setup"
echo "======================"

# Create .claude directory if it doesn't exist
if [ ! -d "$CLAUDE_DIR" ]; then
    echo "📁 Creating Claude Code configuration directory..."
    mkdir -p "$CLAUDE_DIR"
fi

# Check if template exists
if [ ! -f "$TEMPLATE_FILE" ]; then
    echo "❌ Template file not found: $TEMPLATE_FILE"
    exit 1
fi

# Create settings file with correct paths
echo "⚙️  Configuring hooks with absolute paths..."
sed "s|/absolute/path/to/your/remocode|$SCRIPT_DIR|g" "$TEMPLATE_FILE" > "$SETTINGS_FILE"

echo "✅ Claude Code hooks configured successfully!"
echo ""
echo "📍 Settings file: $SETTINGS_FILE"
echo "📍 Hook files:"
echo "   - $SCRIPT_DIR/src/remocode/hooks/pretool_guard.py"
echo "   - $SCRIPT_DIR/src/remocode/hooks/notification_handler.py"
echo "   - $SCRIPT_DIR/src/remocode/hooks/menu_detector.py"
echo ""
echo "🔧 To use RemoCode, simply run:"
echo "   uv run remocode transparent"
echo ""
echo "💻 To use Claude Code without RemoCode, run:"
echo "   claude"
echo ""
echo "📱 Don't forget to configure your Telegram credentials in .env file!"

# Check if .env file exists
if [ ! -f "$SCRIPT_DIR/.env" ]; then
    echo ""
    echo "⚠️  No .env file found. To complete setup:"
    echo "   1. Copy .env.example to .env: cp .env.example .env"
    echo "   2. Edit .env with your Telegram bot token and chat ID"
fi
