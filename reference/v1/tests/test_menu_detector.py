"""Tests for menu detection functionality."""

import pytest
from src.menu_detector import MenuDetector, MenuType


@pytest.mark.parametrize(
    "text,expected_type,expected_count",
    [
        ("1) Accept\n2) Reject\n", MenuType.NUMBERED_OPTIONS, 2),
        ("❯ 1. Yes\n  2. No\n", MenuType.NUMBERED_OPTIONS, 2),
        ("Would you like to proceed? (y/n)", MenuType.YES_NO_PROMPT, 2),
        ("Here is <PERSON>'s plan:", MenuType.INTERACTIVE_PROMPT, 3),
        ("Ready to code?", MenuType.INTERACTIVE_PROMPT, 3),
    ],
)
def test_detect_menus(text, expected_type, expected_count):
    """Test menu detection for various formats."""
    md = MenuDetector()
    result = md.add_output_line(text)
    assert result is not None
    assert result.menu_type == expected_type
    assert result.option_count == expected_count


def test_no_menu_detected():
    """Test that regular output doesn't trigger menu detection."""
    md = MenuDetector()
    result = md.add_output_line("This is just regular output")
    assert result is None


def test_menu_buffer_overflow():
    """Test that menu detector handles buffer overflow properly."""
    md = MenuDetector()

    # Fill buffer beyond capacity
    for i in range(60):
        md.add_output_line(f"Line {i}")

    # Should still work
    result = md.add_output_line("❯ 1. Option A")
    assert result is not None
    assert result.menu_type == MenuType.NUMBERED_OPTIONS


def test_clear_current_menu():
    """Test menu clearing functionality."""
    md = MenuDetector()

    # Detect a menu
    result = md.add_output_line("❯ 1. Yes\n  2. No")
    assert result is not None

    # Clear it
    md.clear_current_menu()
    assert md.current_menu is None
