#!/usr/bin/env python3
"""
Quick test script to verify Claude CLI behavior.
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent))

from src.claude_wrapper import Claude<PERSON><PERSON><PERSON>
from src.state_manager import StateManager
from src.config import EnvironmentConfig

# Setup debug logging
logging.basicConfig(
    level=logging.DEBUG, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)


async def test_claude_cli():
    """Test Claude CLI wrapper with debug logging."""
    print("🔍 Testing Claude CLI wrapper...")

    # Create state manager
    state_manager = StateManager(Path("test_state.json"))
    state_manager.create_new_session("test", None)

    # Output callback
    def on_output(line: str):
        print(f"[CALLBACK] {line}")

    # Menu callback
    def on_menu(menu):
        print(
            f"[MENU] Detected {menu.menu_type.value} with {menu.option_count} options"
        )

    # Create wrapper
    wrapper = <PERSON>Wrapper(
        claude_cmd="npx @anthropic-ai/claude-code",
        state_manager=state_manager,
        on_output=on_output,
        on_menu_detected=on_menu,
    )

    try:
        print("Starting Claude wrapper...")
        success = await wrapper.start()
        if not success:
            print("❌ Failed to start wrapper")
            return

        print("✅ Wrapper started, waiting for output...")

        # Wait a bit to see if any output appears
        await asyncio.sleep(5)

        # Send a test message
        print("Sending test message...")
        await wrapper.send_input("What files are in the current directory?", "test")

        # Wait for response
        await asyncio.sleep(10)

    except KeyboardInterrupt:
        print("\n👋 Stopping test...")
    finally:
        await wrapper.stop()
        print("Test completed")


if __name__ == "__main__":
    asyncio.run(test_claude_cli())
