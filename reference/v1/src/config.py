"""
Configuration management for RemoCode.

Handles loading and validation of configuration from JSON files,
environment variables, and runtime settings.
"""

import os
import json
from pathlib import Path
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from pydantic import BaseModel, Field, validator


class TelegramSettings(BaseModel):
    """Telegram-specific configuration settings."""

    enhanced_ui: bool = True
    show_notifications: bool = True
    silent_notifications: bool = True
    timeout_seconds: int = 300
    fallback_to_allow: bool = True


class SessionSettings(BaseModel):
    """Session management configuration."""

    session_ttl_hours: int = 2
    persistent_ttl_days: int = 30
    notification_mode: str = "silent"


class InteractivePromptDetection(BaseModel):
    """Configuration for detecting <PERSON>'s interactive prompts."""

    enabled: bool = True
    keywords: List[str] = [
        "Would you like to proceed?",
        "Ready to code?",
        "Here is <PERSON>'s plan:",
        "❯ 1.",
        "Choose an option:",
        "What would you like to do?",
        "Select:",
    ]


class RemoCodeConfig(BaseModel):
    """Main configuration model for RemoCode."""

    version: str = "2.0"
    description: str = "Clean RemoCode Configuration"

    # Operation settings
    autonomous_mode_enabled: bool = True
    autonomous_tools: List[str] = [
        "Read",
        "Write",
        "Edit",
        "MultiEdit",
        "str-replace-editor",
        "save-file",
        "remove-files",
        "codebase-retrieval",
        "git-commit-retrieval",
        "grep_search",
        "codebase_search",
        "file_search",
        "list_dir",
        "Glob",
        "view",
        "view-range-untruncated",
        "search-untruncated",
        "view_tasklist",
        "add_tasks",
        "update_tasks",
        "reorganize_tasklist",
        "diagnostics",
        "render-mermaid",
        "remember",
        "web-search",
        "web-fetch",
    ]

    autonomous_bash_patterns: List[str] = [
        r"^npm run test.*",
        r"^npm run build.*",
        r"^npm run lint.*",
        r"^npm run typecheck.*",
        r"^npm install.*",
        r"^npx tsc.*",
        r"^npx eslint.*",
        r"^ls.*",
        r"^find.*",
        r"^grep.*",
        r"^rg.*",
        r"^git status.*",
        r"^git diff.*",
        r"^git log.*",
        r"^git show.*",
    ]

    blocked_operations: List[str] = [
        "git_push",
        "git_merge",
        "git_rebase",
        "database_write",
        "database_delete",
        "system_shutdown",
        "deploy",
        "publish",
        "exit_plan_mode",
    ]

    blocked_bash_patterns: List[str] = [
        r"^git push.*",
        r"^git merge.*",
        r"^git rebase.*",
        r"^rm -rf.*",
        r"^sudo.*",
        r"^chmod 777.*",
        r"^npm publish.*",
        r"^vercel --prod.*",
        r"^docker.*",
    ]

    # Component settings
    telegram: TelegramSettings = Field(default_factory=TelegramSettings)
    session: SessionSettings = Field(default_factory=SessionSettings)
    interactive_prompt_detection: InteractivePromptDetection = Field(
        default_factory=InteractivePromptDetection
    )

    @validator("autonomous_tools")
    def validate_autonomous_tools(cls, v):
        """Ensure autonomous tools list is not empty."""
        if not v:
            raise ValueError("Autonomous tools list cannot be empty")
        return v


@dataclass
class EnvironmentConfig:
    """Environment-based configuration from .env files."""

    tg_token: Optional[str] = None
    tg_chat: Optional[str] = None
    claude_cmd: str = "npx @anthropic-ai/claude-code"
    log_level: str = "INFO"

    @classmethod
    def from_env(cls) -> "EnvironmentConfig":
        """Load configuration from environment variables."""
        return cls(
            tg_token=os.getenv("TG_TOKEN"),
            tg_chat=os.getenv("TG_CHAT"),
            claude_cmd=os.getenv("CLAUDE_CMD", "npx @anthropic-ai/claude-code"),
            log_level=os.getenv("LOG_LEVEL", "INFO"),
        )

    @property
    def has_telegram(self) -> bool:
        """Check if Telegram configuration is available."""
        return bool(self.tg_token and self.tg_chat)


class ConfigManager:
    """Manages loading and access to configuration from multiple sources."""

    def __init__(self, config_path: Optional[Path] = None):
        """Initialize configuration manager with optional config file path."""
        self.config_path = config_path or Path("config.json")
        self._config: Optional[RemoCodeConfig] = None
        self._env_config: Optional[EnvironmentConfig] = None

    def load(self) -> None:
        """Load configuration from all sources."""
        # Load environment configuration
        self._env_config = EnvironmentConfig.from_env()

        # Load JSON configuration
        if self.config_path.exists():
            try:
                with open(self.config_path, "r") as f:
                    config_data = json.load(f)
                self._config = RemoCodeConfig(**config_data)
            except (json.JSONDecodeError, ValueError) as e:
                raise RuntimeError(
                    f"Failed to load config from {self.config_path}: {e}"
                )
        else:
            # Use default configuration
            self._config = RemoCodeConfig()

    @property
    def config(self) -> RemoCodeConfig:
        """Get the main configuration object."""
        if self._config is None:
            self.load()
        return self._config

    @property
    def env(self) -> EnvironmentConfig:
        """Get the environment configuration object."""
        if self._env_config is None:
            self.load()
        return self._env_config

    def is_tool_autonomous(self, tool_name: str) -> bool:
        """Check if a tool is configured for autonomous operation."""
        return tool_name in self.config.autonomous_tools

    def is_operation_blocked(self, operation: str) -> bool:
        """Check if an operation is blocked and requires approval."""
        return operation in self.config.blocked_operations

    def save_default_config(self) -> None:
        """Save a default configuration file."""
        default_config = RemoCodeConfig()
        with open(self.config_path, "w") as f:
            json.dump(default_config.dict(), f, indent=2)


# Global configuration instance
config_manager = ConfigManager()
