"""
Handy commands functionality for RemoCode.

Provides common development tasks that can be executed
through the /handy command palette.
"""

import asyncio
import logging
import subprocess
from typing import Dict, List, Optional, Callable
from dataclasses import dataclass


logger = logging.getLogger(__name__)


@dataclass
class HandyCommand:
    """Represents a handy command that can be executed."""

    id: str
    name: str
    description: str
    command: str
    icon: str = "🔧"
    working_dir: Optional[str] = None


class HandyCommandExecutor:
    """Executes handy commands and streams output."""

    def __init__(self, on_output: Optional[Callable[[str], None]] = None):
        """
        Initialize handy command executor.

        Args:
            on_output: Optional callback for command output lines
        """
        self.on_output = on_output
        self.available_commands = self._build_command_catalog()

    def _build_command_catalog(self) -> Dict[str, HandyCommand]:
        """Build catalog of available handy commands."""
        commands = {
            "test": HandyCommand(
                id="test",
                name="Run Tests",
                description="Run the project test suite",
                command="npm test",
                icon="🧪",
            ),
            "build": HandyCommand(
                id="build",
                name="Run Build",
                description="Build the project",
                command="npm run build",
                icon="🏗️",
            ),
            "lint": HandyCommand(
                id="lint",
                name="Run Lint",
                description="Run linting checks",
                command="npm run lint",
                icon="📝",
            ),
            "typecheck": HandyCommand(
                id="typecheck",
                name="Run Typecheck",
                description="Run TypeScript type checking",
                command="npm run typecheck",
                icon="🔍",
            ),
            "git_status": HandyCommand(
                id="git_status",
                name="Git Status",
                description="Show git status",
                command="git status",
                icon="📊",
            ),
            "git_diff": HandyCommand(
                id="git_diff",
                name="Git Diff",
                description="Show git diff",
                command="git diff",
                icon="📋",
            ),
            "install": HandyCommand(
                id="install",
                name="Install Dependencies",
                description="Install npm dependencies",
                command="npm install",
                icon="📦",
            ),
        }

        return commands

    def get_available_commands(self) -> List[HandyCommand]:
        """Get list of available commands."""
        return list(self.available_commands.values())

    def get_command(self, command_id: str) -> Optional[HandyCommand]:
        """Get command by ID."""
        return self.available_commands.get(command_id)

    async def execute_command(self, command_id: str) -> bool:
        """
        Execute a handy command.

        Args:
            command_id: ID of the command to execute

        Returns:
            True if command executed successfully, False otherwise
        """
        command = self.get_command(command_id)
        if not command:
            logger.error(f"Unknown handy command: {command_id}")
            return False

        try:
            logger.info(f"Executing handy command: {command.name}")

            if self.on_output:
                self.on_output(f"🚀 Executing: {command.name}")
                self.on_output(f"💻 Command: {command.command}")
                self.on_output("")

            # Execute command with real-time output streaming
            process = await asyncio.create_subprocess_shell(
                command.command,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.STDOUT,
                cwd=command.working_dir,
            )

            # Stream output line by line
            if process.stdout:
                async for line_bytes in process.stdout:
                    line = line_bytes.decode("utf-8", errors="replace").rstrip()
                    if line:  # Skip empty lines
                        print(f"  {line}")  # Print to terminal
                        if self.on_output:
                            self.on_output(line)

            # Wait for completion
            return_code = await process.wait()

            # Report completion
            if return_code == 0:
                success_msg = f"✅ {command.name} completed successfully"
                logger.info(success_msg)
                if self.on_output:
                    self.on_output("")
                    self.on_output(success_msg)
            else:
                error_msg = f"❌ {command.name} failed with exit code {return_code}"
                logger.error(error_msg)
                if self.on_output:
                    self.on_output("")
                    self.on_output(error_msg)
                return False

            return True

        except Exception as e:
            error_msg = f"❌ Error executing {command.name}: {e}"
            logger.error(error_msg)
            if self.on_output:
                self.on_output(error_msg)
            return False

    async def execute_custom_command(
        self, command_text: str, description: str = "Custom Command"
    ) -> bool:
        """
        Execute a custom command.

        Args:
            command_text: Raw command to execute
            description: Description for logging

        Returns:
            True if successful, False otherwise
        """
        try:
            logger.info(f"Executing custom command: {description}")

            if self.on_output:
                self.on_output(f"🚀 Executing: {description}")
                self.on_output(f"💻 Command: {command_text}")
                self.on_output("")

            process = await asyncio.create_subprocess_shell(
                command_text,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.STDOUT,
            )

            if process.stdout:
                async for line_bytes in process.stdout:
                    line = line_bytes.decode("utf-8", errors="replace").rstrip()
                    if line:
                        print(f"  {line}")
                        if self.on_output:
                            self.on_output(line)

            return_code = await process.wait()

            if return_code == 0:
                success_msg = f"✅ {description} completed successfully"
                if self.on_output:
                    self.on_output("")
                    self.on_output(success_msg)
                return True
            else:
                error_msg = f"❌ {description} failed with exit code {return_code}"
                if self.on_output:
                    self.on_output("")
                    self.on_output(error_msg)
                return False

        except Exception as e:
            error_msg = f"❌ Error executing {description}: {e}"
            logger.error(error_msg)
            if self.on_output:
                self.on_output(error_msg)
            return False
