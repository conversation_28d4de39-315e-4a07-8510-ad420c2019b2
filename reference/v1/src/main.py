#!/usr/bin/env python3
"""
RemoCode - Remote Claude Code Control via Telegram

Main entry point for the clean, production-ready implementation.
Provides dual-system control through both terminal and Telegram interfaces.
"""

import asyncio
import logging
import signal
import sys
import time
import uuid
from pathlib import Path
from typing import Optional, List

import typer
from rich.console import Console
from rich.logging import <PERSON><PERSON><PERSON><PERSON>
from dotenv import load_dotenv

# ---------------------------------------------------------------------------
# Module-level logger (avoids NameError in async callbacks)
# ---------------------------------------------------------------------------
logger = logging.getLogger(__name__)

from .config import Config<PERSON>anager, config_manager
from .claude_wrapper import <PERSON><PERSON>rapper
from .telegram_bot import TelegramBotManager
from .state_manager import StateManager, LogRotator
from .menu_detector import DetectedMenu


# Load environment variables
load_dotenv()

# Rich console for pretty output
console = Console()
app = typer.Typer(help="RemoCode - Remote Claude Code Control via Telegram")


class RemoCodeApp:
    """Main RemoCode application orchestrator."""

    def __init__(self):
        """Initialize RemoCode application."""
        self.config_manager = config_manager
        self.state_manager: Optional[StateManager] = None
        self.claude_wrapper: Optional[ClaudeWrapper] = None
        self.telegram_manager: Optional[TelegramBotManager] = None

        # Runtime state
        self.session_id = str(uuid.uuid4())[:8]
        self.running = False
        self.shutdown_event = asyncio.Event()

        # Setup logging
        self._setup_logging()

    def _setup_logging(self) -> None:
        """Setup rich logging configuration."""
        log_level = self.config_manager.env.log_level

        # Console should be less chatty (WARNING+), but the log file keeps full detail.
        console_handler = RichHandler(
            console=console, show_path=False, level=logging.WARNING
        )
        file_handler = logging.FileHandler("remocode.log")

        logging.basicConfig(
            level=getattr(
                logging, log_level.upper(), logging.INFO
            ),  # Root level for file
            format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            handlers=[console_handler, file_handler],
        )

        # Silence verbose third-party libraries in console while retaining them in file
        logging.getLogger("httpx").setLevel(logging.INFO)  # still recorded, not printed

        # Rotate log file if needed
        log_rotator = LogRotator(Path("remocode.log"))
        log_rotator.rotate_if_needed()

    async def start(self, claude_args: Optional[List[str]] = None) -> None:
        """
        Start RemoCode application.

        Args:
            claude_args: Optional arguments to pass to Claude CLI
        """
        if self.running:
            console.print("❌ RemoCode is already running", style="red")
            return

        try:
            console.print("🚀 Starting RemoCode...", style="blue")

            # Load configuration
            self.config_manager.load()

            # Initialize state manager
            self.state_manager = StateManager(Path("state.json"))

            # Try to load existing state or create new session
            existing_state = self.state_manager.load_state()
            if existing_state:
                console.print(
                    f"📂 Resumed session: {existing_state.session_id}", style="green"
                )
                self.session_id = existing_state.session_id
            else:
                self.state_manager.create_new_session(
                    self.session_id, self.config_manager.env.tg_chat
                )
                console.print(
                    f"🆕 Created new session: {self.session_id}", style="green"
                )

            # Initialize Telegram bot manager
            self.telegram_manager = TelegramBotManager(self.config_manager.env)

            # Setup Claude wrapper with callbacks
            self.claude_wrapper = ClaudeWrapper(
                claude_cmd=self.config_manager.env.claude_cmd,
                state_manager=self.state_manager,
                on_output=self._handle_claude_output,
                on_menu_detected=self._handle_menu_detected,
            )

            # Set up state change callback for live view
            self.claude_wrapper.on_state_change = self._handle_claude_state_change

            # Start Telegram bot
            if not await self.telegram_manager.start(
                on_menu_choice=self._handle_menu_choice,
                on_command=self._handle_command,
                get_batch_interval=lambda: self.claude_wrapper.get_adaptive_batch_interval()
                if self.claude_wrapper
                else 1.5,
                get_claude_state=lambda: self.claude_wrapper.claude_state
                if self.claude_wrapper
                else "waiting",
                on_user_input=self._handle_user_input,
            ):
                console.print(
                    "⚠️ Failed to start Telegram bot, continuing in terminal-only mode",
                    style="yellow",
                )

            # Start Claude CLI
            if not await self.claude_wrapper.start(claude_args):
                console.print("❌ Failed to start Claude CLI", style="red")
                await self.stop()
                return

            self.running = True

            # Setup signal handlers
            self._setup_signal_handlers()

            console.print("✅ RemoCode started successfully!", style="green")
            console.print(
                "💡 You can now control Claude through both terminal and Telegram",
                style="cyan",
            )

            # Wait for shutdown
            await self.shutdown_event.wait()

        except Exception as e:
            console.print(f"❌ Error starting RemoCode: {e}", style="red")
            logging.error(f"Failed to start RemoCode: {e}", exc_info=True)
            await self.stop()

    async def stop(self) -> None:
        """Stop RemoCode application gracefully."""
        if not self.running:
            return

        console.print("🛑 Stopping RemoCode...", style="yellow")
        self.running = False

        # Stop components in reverse order
        if self.claude_wrapper:
            await self.claude_wrapper.stop()

        if self.telegram_manager:
            await self.telegram_manager.stop()

        # Final state save
        if self.state_manager:
            self.state_manager.force_save()

        console.print("✅ RemoCode stopped", style="green")
        self.shutdown_event.set()

    def _setup_signal_handlers(self) -> None:
        """Setup signal handlers for graceful shutdown."""

        def signal_handler(sig, frame):
            console.print(
                f"\n🔄 Received signal {sig}, shutting down gracefully...",
                style="yellow",
            )
            asyncio.create_task(self.stop())

        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

    async def _handle_claude_output(self, line: str) -> None:
        """Passes every line of output to the Telegram manager for processing."""
        logger.debug(f"[RemoCodeApp] Forwarding line to Telegram: {line[:120]!r}")
        if self.telegram_manager:
            try:
                await self.telegram_manager.send_output(line)
            except Exception as e:
                logger.error(f"Failed to queue output to Telegram: {e}")
        else:
            logger.debug("Telegram manager not configured; skipping Telegram send.")

    def _handle_claude_state_change(self, old_state: str, new_state: str) -> None:
        """Handle Claude state changes for live view management."""
        logger.debug(f"[RemoCodeApp] Claude state change: {old_state} -> {new_state}")
        if self.telegram_manager:
            try:
                self.telegram_manager.handle_claude_state_change(old_state, new_state)
            except Exception as e:
                logger.error(f"Failed to handle Claude state change: {e}")
        else:
            logger.debug("Telegram manager not configured; skipping state change.")

    async def _handle_menu_detected(self, menu: DetectedMenu) -> None:
        """Handle detected menu from Claude CLI."""
        console.print(
            f"🔍 Menu detected: {menu.menu_type.value} with {menu.option_count} options",
            style="cyan",
        )

        # Show menu in Telegram
        if self.telegram_manager:
            telegram_shown = await self.telegram_manager.show_menu(menu)
            if telegram_shown:
                console.print("📱 Menu sent to Telegram", style="green")

        # Wait for choice from either terminal or Telegram
        if self.claude_wrapper:
            console.print(
                "⏳ Waiting for your choice (terminal or Telegram)...", style="blue"
            )

            choice = await self.claude_wrapper.wait_for_menu_choice(
                timeout=self.config_manager.config.telegram.timeout_seconds
            )

            if choice:
                console.print(f"✅ Choice selected: {choice}", style="green")
                await self.claude_wrapper.send_input(choice, "menu_response")

                # Clear menu in Telegram after successful choice
                if self.telegram_manager:
                    await self.telegram_manager.clear_menu()
            else:
                console.print("⏰ Menu choice timed out, sending 'skip'", style="yellow")
                await self.claude_wrapper.send_input("skip", "timeout")

                # Clear menu after timeout as well
                if self.telegram_manager:
                    await self.telegram_manager.clear_menu()

    def _handle_menu_choice(self, choice: str, source: str) -> None:
        """Handle menu choice from terminal or Telegram."""
        if self.claude_wrapper:
            success = self.claude_wrapper.submit_menu_choice(choice, source)
            if success:
                console.print(
                    f"✅ Choice '{choice}' received from {source}", style="green"
                )
            else:
                console.print(
                    f"⚠️ Choice '{choice}' from {source} ignored (not waiting)",
                    style="yellow",
                )

    def _handle_command(self, command: str, args: List[str]) -> None:
        """Handle command from Telegram."""
        console.print(f"🎛️ Command received: /{command} {' '.join(args)}", style="cyan")

        # Handle specific commands
        if command == "handy":
            # This would be implemented with the handy functionality
            pass
        elif command == "cost":
            # This would be implemented with cost tracking
            pass

    def _handle_user_input(self, text: str) -> None:
        """Receive free-form user text from Telegram and forward to Claude."""
        if not self.claude_wrapper:
            return

        async def _send():
            success = await self.claude_wrapper.send_input(text, "telegram_input")
            if success:
                console.print(f"📤 Sent input from Telegram: {text}", style="magenta")
            else:
                console.print(f"⚠️ Failed sending Telegram input: {text}", style="red")

        asyncio.create_task(_send())

    # ---------------------------------------------------------------------
    # Utility helpers
    # ---------------------------------------------------------------------

    @staticmethod
    def _strip_ansi(text: str) -> str:
        """Remove ANSI escape sequences so that plain text is safe for Telegram."""
        import re

        ansi_re = re.compile(r"\x1b\[[0-9;]*[A-Za-z]")
        return ansi_re.sub("", text)


# CLI Commands


@app.command()
def run(
    spec_file: Optional[Path] = typer.Argument(
        None, help="Optional specification file for Claude"
    ),
    config: Optional[Path] = typer.Option(
        None, "--config", "-c", help="Path to config file"
    ),
    log_level: Optional[str] = typer.Option(
        None, "--log-level", "-l", help="Log level (DEBUG, INFO, WARNING, ERROR)"
    ),
) -> None:
    """Run RemoCode with dual terminal/Telegram control."""

    # Override configuration if provided
    if config:
        config_manager.config_path = config

    if log_level:
        import os

        os.environ["LOG_LEVEL"] = log_level.upper()

    # Build Claude arguments
    claude_args = []
    if spec_file:
        if not spec_file.exists():
            console.print(f"❌ Specification file not found: {spec_file}", style="red")
            raise typer.Exit(1)
        claude_args.append(str(spec_file))

    # Create and run application
    remocode_app = RemoCodeApp()

    try:
        asyncio.run(remocode_app.start(claude_args))
    except KeyboardInterrupt:
        console.print("\n👋 Goodbye!", style="cyan")
    except Exception as e:
        console.print(f"❌ Fatal error: {e}", style="red")
        raise typer.Exit(1)


@app.command()
def status() -> None:
    """Show RemoCode status and configuration."""
    config_manager.load()

    console.print("📊 RemoCode Status", style="bold blue")
    console.print("=" * 50)

    # Environment status
    env = config_manager.env
    console.print(f"🤖 Claude Command: {env.claude_cmd}")
    console.print(
        f"📱 Telegram: {'✅ Configured' if env.has_telegram else '❌ Not configured'}"
    )
    if env.has_telegram:
        console.print(f"   Chat ID: {env.tg_chat}")

    # Configuration status
    config = config_manager.config
    console.print(
        f"🔧 Autonomous Mode: {'✅ Enabled' if config.autonomous_mode_enabled else '❌ Disabled'}"
    )
    console.print(f"🛠️ Autonomous Tools: {len(config.autonomous_tools)}")
    console.print(f"🚫 Blocked Operations: {len(config.blocked_operations)}")

    # State file status
    state_file = Path("state.json")
    if state_file.exists():
        import json

        try:
            with open(state_file) as f:
                state_data = json.load(f)
            console.print(f"💾 Session: {state_data.get('session_id', 'unknown')}")
            console.print(
                f"   Age: {(time.time() - state_data.get('start_time', 0)):.1f}s"
            )
        except:
            console.print("💾 Session: ❌ Invalid state file")
    else:
        console.print("💾 Session: No active session")


@app.command()
def init() -> None:
    """Initialize RemoCode configuration."""
    console.print("🎯 Initializing RemoCode configuration...", style="blue")

    config_path = Path("config.json")
    if config_path.exists():
        if not typer.confirm(f"Config file already exists. Overwrite?"):
            console.print("❌ Initialization cancelled", style="red")
            return

    # Create default configuration
    config_manager.config_path = config_path
    config_manager.save_default_config()

    console.print(f"✅ Created default configuration: {config_path}", style="green")
    console.print("💡 Edit the config file to customize RemoCode behavior", style="cyan")

    # Check for environment variables
    env = config_manager.env
    if not env.has_telegram:
        console.print(
            "⚠️ Telegram not configured. Set TG_TOKEN and TG_CHAT environment variables",
            style="yellow",
        )


def main() -> None:
    """Main entry point."""
    app()


if __name__ == "__main__":
    main()
