{
  "permissions": {
    "allow": [
      "Bash(tail:*)",
      "Bash(rg:*)",
      "<PERSON>sh(npm run build:*)",
      "<PERSON><PERSON>(npm test:*)",
      "<PERSON><PERSON>(mv:*)",
      "<PERSON>sh(find:*)",
      "<PERSON>sh(npm run lint)",
      "<PERSON>sh(npm run typecheck:*)",
      "Bash(npm run:*)",
      "WebFetch(domain:reactflow.dev)",
      "WebFetch(domain:github.com)",
      "Bash(npm install)",
      "<PERSON>sh(grep:*)",
      "Bash(curl:*)",
      "mcp__ide__getDiagnostics",
      "WebFetch(domain:ai-sdk.dev)",
      "WebFetch(domain:vercel.com)",
      "WebFetch(domain:ai-sdk-reasoning.vercel.app)",
      "WebFetch(domain:sdk.vercel.ai)",
      "WebFetch(domain:www.assistant-ui.com)",
      "<PERSON>sh(gemini:*)",
      "<PERSON>sh(mkdir:*)",
      "<PERSON>sh(npx tsc:*)",
      "Bash(npx eslint:*)",
      "Bash(ls:*)",
      "Bash(npx prisma migrate dev:*)",
      "Bash(timeout:*)",
      "WebFetch(domain:docs.anthropic.com)",
      "Bash(cp:*)",
      "Bash(uv run pre-commit run:*)"
    ],
    "deny": []
  },
  "hooks": {
    "PreToolUse": [
      {
        "hooks": [
          {
            "type": "command",
            "command": "python3 /absolute/path/to/your/remocode/src/remocode/hooks/pretool_guard.py"
          }
        ]
      }
    ],
    "Notification": [
      {
        "hooks": [
          {
            "type": "command",
            "command": "python3 /absolute/path/to/your/remocode/src/remocode/hooks/notification_handler.py \"$CLAUDE_NOTIFICATION_TYPE\" \"$CLAUDE_NOTIFICATION_MESSAGE\""
          }
        ]
      }
    ],
    "UserPromptSubmit": [
      {
        "hooks": [
          {
            "type": "command",
            "command": "python3 /absolute/path/to/your/remocode/src/remocode/hooks/menu_detector.py \"$CLAUDE_USER_PROMPT\""
          }
        ]
      }
    ]
  }
}


  "hooks": {
    "PreToolUse": [
      {
        "hooks": [
          {
            "type": "command",
            "command": "python3 /Users/<USER>/Desktop/github/remocode/src/remocode/hooks/pretool_guard.py"
          }
        ]
      }
    ],
    "Notification": [
      {
        "hooks": [
          {
            "type": "command",
            "command": "python3 /Users/<USER>/Desktop/github/remocode/src/remocode/hooks/notification_handler.py \"$CLAUDE_NOTIFICATION_TYPE\" \"$CLAUDE_NOTIFICATION_MESSAGE\""
          }
        ]
      }
    ],
    "UserPromptSubmit": [
      {
        "hooks": [
          {
            "type": "command",
            "command": "python3 /Users/<USER>/Desktop/github/remocode/src/remocode/hooks/menu_detector.py \"$CLAUDE_USER_PROMPT\""
          }
        ]
      }
    ]
  }
