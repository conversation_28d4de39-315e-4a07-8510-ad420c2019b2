#!/usr/bin/env python3
"""Simple Hello World web server for RemoCode."""

from __future__ import annotations

import asyncio
import logging
from typing import Any, Dict

import uvicorn
from fastapi import FastAPI
from fastapi.responses import HTMLResponse

logger = logging.getLogger(__name__)


def create_app() -> FastAPI:
    """Create and configure the FastAPI application."""
    app = FastAPI(
        title="RemoCode Hello Big World",
        description="A simple Hello Big World web server built with Python and FastAPI",
        version="1.0.0",
    )

    @app.get("/", response_class=HTMLResponse)
    async def hello_world() -> str:
        """Return a Hello World HTML page."""
        return """
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Hello Big World - RemoCode</title>
            <style>
                body {
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                    margin: 0;
                    padding: 0;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    min-height: 100vh;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
                .container {
                    text-align: center;
                    background: white;
                    padding: 3rem;
                    border-radius: 20px;
                    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
                    max-width: 500px;
                    margin: 2rem;
                }
                h1 {
                    color: #333;
                    margin-bottom: 1rem;
                    font-size: 3rem;
                    font-weight: 700;
                }
                p {
                    color: #666;
                    font-size: 1.2rem;
                    margin-bottom: 2rem;
                    line-height: 1.6;
                }
                .tech-stack {
                    background: #f8f9fa;
                    padding: 1rem;
                    border-radius: 10px;
                    margin-top: 2rem;
                }
                .tech-stack h3 {
                    color: #495057;
                    margin-bottom: 0.5rem;
                }
                .tech-list {
                    color: #6c757d;
                    font-size: 0.9rem;
                }
                .emoji {
                    font-size: 2rem;
                    margin-bottom: 1rem;
                }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="emoji">👋🐍</div>
                <h1>Hello Big World!</h1>
                <p>Welcome to your Python-powered Big World page, built with RemoCode and served by FastAPI.</p>

                <div class="tech-stack">
                    <h3>Tech Stack</h3>
                    <div class="tech-list">
                        Python • FastAPI • Uvicorn • RemoCode
                    </div>
                </div>

                <p style="margin-top: 2rem; font-size: 1rem; color: #888;">
                    Running on localhost - your Python Hello Big World is live! 🎉
                </p>
            </div>
        </body>
        </html>
        """

    @app.get("/api/status")
    async def status() -> Dict[str, Any]:
        """Return API status information."""
        return {
            "status": "ok",
            "message": "Hello Big World API is running",
            "service": "RemoCode Hello Big World",
            "version": "1.0.0",
        }

    @app.get("/api/hello/{name}")
    async def hello_name(name: str) -> Dict[str, str]:
        """Return a personalized greeting."""
        return {
            "message": f"Hello, {name}! Welcome to RemoCode's Big World.",
            "greeting_type": "personalized",
        }

    return app


async def run_server(host: str = "127.0.0.1", port: int = 8000) -> None:
    """Run the Hello World web server."""
    app = create_app()

    logger.info(f"Starting Hello Big World server on http://{host}:{port}")
    logger.info(
        "Visit http://127.0.0.1:8000 in your browser to see the Hello Big World page"
    )
    logger.info("API endpoints:")
    logger.info("  • GET /api/status - Server status")
    logger.info("  • GET /api/hello/{name} - Personalized greeting")

    config = uvicorn.Config(
        app=app,
        host=host,
        port=port,
        log_level="info",
        access_log=True,
    )

    server = uvicorn.Server(config)
    await server.serve()


if __name__ == "__main__":
    asyncio.run(run_server())
