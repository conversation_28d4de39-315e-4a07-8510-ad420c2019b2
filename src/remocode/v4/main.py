#!/usr/bin/env python3
"""
RemoCode v4 Main Entry Point

Launches the breakthrough log-based dual system that provides:
- Transparent Claude Code launch with normal terminal operation
- Intelligent Telegram monitoring with optional control
- Clean subprocess management without PTY complexity
- Reliable state detection using structured logs
"""

import asyncio
import logging
import os
import sys
from pathlib import Path
from typing import Optional

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent.parent))

from .core.claude_controller import <PERSON><PERSON><PERSON><PERSON><PERSON>
from .core.log_state_detector import TranscriptStateDetector
from .core.simple_telegram import SimpleTelegramBot


def setup_logging(debug: bool = False) -> None:
    """Setup logging configuration"""
    level = logging.DEBUG if debug else logging.INFO
    logging.basicConfig(
        level=level,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[logging.StreamHandler(), logging.FileHandler("logs/remocode_v4.log")],
    )


async def main(
    claude_cmd: str = "npx @anthropic-ai/claude-code",
    working_dir: Optional[str] = None,
    debug: bool = False,
    telegram_enabled: bool = True,
) -> None:
    """
    Main entry point for RemoCode v4 dual system

    Args:
        claude_cmd: Command to launch Claude Code
        working_dir: Working directory for Claude Code
        debug: Enable debug logging
        telegram_enabled: Enable Telegram integration
    """
    setup_logging(debug)
    logger = logging.getLogger(__name__)

    # Ensure logs directory exists
    Path("logs").mkdir(exist_ok=True)

    logger.info("🚀 Starting RemoCode v4 - Log-Based Dual System")

    # Validate Telegram configuration if enabled
    if telegram_enabled:
        tg_token = os.getenv("TG_TOKEN")
        tg_chat = os.getenv("TG_CHAT")

        if not tg_token or not tg_chat:
            logger.warning(
                "⚠️  Telegram credentials not found, running in terminal-only mode"
            )
            logger.warning(
                "   Set TG_TOKEN and TG_CHAT environment variables to enable Telegram"
            )
            telegram_enabled = False

    # Initialize components
    claude_controller = ClaudeController(claude_cmd, working_dir)
    transcript_detector = TranscriptStateDetector()

    telegram_bot = None
    if telegram_enabled:
        tg_token = os.getenv("TG_TOKEN")
        tg_chat = os.getenv("TG_CHAT")

        if tg_token and tg_chat:
            telegram_bot = SimpleTelegramBot(tg_token, tg_chat)

    # Connect components
    if telegram_bot:
        # Transcript detector notifies Telegram bot of state changes
        transcript_detector.register_callback(telegram_bot.handle_state_change)

        # Telegram bot can send input suggestions to Claude
        telegram_bot.set_input_handler(claude_controller.send_input)

    try:
        # Start all components concurrently
        tasks = [
            claude_controller.start(),
            transcript_detector.start_monitoring(),
        ]

        if telegram_bot:
            tasks.append(telegram_bot.start())

        logger.info("✅ All components started successfully")
        logger.info("🖥️  Terminal: Normal Claude Code interface active")

        if telegram_bot:
            logger.info("📱 Telegram: Monitoring and optional control enabled")

        logger.info("🔄 Log-based state detection active")
        logger.info("\n" + "=" * 60)
        logger.info("RemoCode v4 Dual System Ready!")
        logger.info("- Use terminal for normal Claude Code interaction")
        logger.info("- Check Telegram for monitoring and optional control")
        logger.info("- Press Ctrl+C to stop")
        logger.info("=" * 60 + "\n")

        # Run all tasks concurrently
        await asyncio.gather(*tasks, return_exceptions=True)

    except KeyboardInterrupt:
        logger.info("\n🛑 Shutting down RemoCode v4...")
    except Exception as e:
        logger.error(f"❌ Fatal error: {e}")
        raise
    finally:
        # Cleanup
        await claude_controller.stop()
        await transcript_detector.stop()
        if telegram_bot:
            await telegram_bot.stop()
        logger.info("✅ RemoCode v4 shutdown complete")


def cli_main():
    """CLI entry point"""
    import argparse

    parser = argparse.ArgumentParser(
        description="RemoCode v4 - Log-Based Dual System",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Start with Telegram integration
  TG_TOKEN=your_token TG_CHAT=your_chat_id python -m remocode.v4.main

  # Terminal-only mode
  python -m remocode.v4.main --no-telegram

  # Debug mode
  python -m remocode.v4.main --debug

  # Custom Claude command
  python -m remocode.v4.main --claude-cmd "claude --model claude-3-5-sonnet"
        """,
    )

    parser.add_argument(
        "--claude-cmd",
        default="npx @anthropic-ai/claude-code",
        help="Command to launch Claude Code (default: npx @anthropic-ai/claude-code)",
    )

    parser.add_argument(
        "--working-dir",
        help="Working directory for Claude Code (default: current directory)",
    )

    parser.add_argument("--debug", action="store_true", help="Enable debug logging")

    parser.add_argument(
        "--no-telegram",
        action="store_true",
        help="Disable Telegram integration (terminal-only mode)",
    )

    args = parser.parse_args()

    # Run the async main function
    asyncio.run(
        main(
            claude_cmd=args.claude_cmd,
            working_dir=args.working_dir,
            debug=args.debug,
            telegram_enabled=not args.no_telegram,
        )
    )


if __name__ == "__main__":
    cli_main()
