#!/usr/bin/env python3
"""
Simple V4 Launcher - The Actually Working Approach

Just launches Claude Code normally and uses existing v2 hooks for monitoring.
This avoids all the PTY complexity while still providing the dual system.
"""

import asyncio
import logging
import os
import sys
from pathlib import Path


async def launch_claude_with_monitoring(
    claude_cmd: str = "npx @anthropic-ai/claude-code",
    working_dir: str = None,
    telegram_enabled: bool = True,
):
    """
    Launch Claude Code with v2 hook monitoring

    This is the simple approach that actually works:
    1. Configure v2 hooks for monitoring
    2. Launch Claude Code normally (inherits terminal)
    3. Let hooks handle Telegram integration
    """
    logger = logging.getLogger(__name__)

    # Set working directory
    if working_dir:
        os.chdir(working_dir)

    # Configure v2 hooks for monitoring
    if telegram_enabled:
        logger.info("📋 Configuring v2 hooks for monitoring...")
        await _configure_v2_hooks()

    # Parse command
    cmd_parts = claude_cmd.split()

    logger.info(f"🚀 Launching Claude Code: {' '.join(cmd_parts)}")
    logger.info("🖥️  Claude Code interface will appear normally")
    logger.info("📱 Telegram monitoring active via v2 hooks")

    # Launch Claude Code normally - it inherits the terminal
    process = await asyncio.create_subprocess_exec(
        *cmd_parts,
        # No stdin/stdout/stderr specified = inherit terminal (normal operation)
    )

    logger.info(f"✅ Claude Code started (PID: {process.pid})")
    logger.info("💡 Use Claude Code normally - Telegram monitoring is active")

    try:
        # Wait for Claude Code to finish
        return_code = await process.wait()
        logger.info(f"🏁 Claude Code finished (exit code: {return_code})")

    except KeyboardInterrupt:
        logger.info("🛑 Interrupted - terminating Claude Code...")
        process.terminate()
        await process.wait()
        logger.info("✅ Claude Code terminated")


async def _configure_v2_hooks():
    """Configure v2 hooks for monitoring"""
    import json

    # Ensure .claude directory exists
    claude_dir = Path(".claude")
    claude_dir.mkdir(exist_ok=True)

    # Configure hooks in settings.local.json
    settings_file = claude_dir / "settings.local.json"

    # Get absolute path to hooks
    hooks_dir = Path(__file__).parent.parent / "hooks"

    config = {}
    if settings_file.exists():
        try:
            config = json.loads(settings_file.read_text())
        except json.JSONDecodeError:
            pass

    # Configure notification handler for Telegram
    hooks_section = config.setdefault("hooks", {})
    hooks_section["notification_handler"] = {
        "command": f"python {hooks_dir / 'notification_handler.py'}",
        "when": "before_tool_use",
    }

    # Write configuration
    settings_file.write_text(json.dumps(config, indent=2))

    print(f"✅ Configured v2 hooks in {settings_file}")


async def main():
    """Main entry point for simple v4 launcher"""
    import argparse

    parser = argparse.ArgumentParser(description="RemoCode v4 Simple Launcher")
    parser.add_argument("--claude-cmd", default="npx @anthropic-ai/claude-code")
    parser.add_argument("--working-dir", default=None)
    parser.add_argument("--no-telegram", action="store_true")
    parser.add_argument("--debug", action="store_true")

    args = parser.parse_args()

    # Setup logging
    level = logging.DEBUG if args.debug else logging.INFO
    logging.basicConfig(level=level, format="%(levelname)s: %(message)s")

    logger = logging.getLogger(__name__)

    # Check Telegram credentials
    telegram_enabled = not args.no_telegram
    if telegram_enabled:
        if not os.getenv("TG_TOKEN") or not os.getenv("TG_CHAT"):
            logger.warning("⚠️  Telegram credentials not found - running terminal-only")
            telegram_enabled = False

    print("🚀 RemoCode v4 Simple Launcher")
    print("=" * 40)

    if telegram_enabled:
        print("📱 Telegram monitoring: ENABLED")
    else:
        print("🖥️  Terminal-only mode: ENABLED")

    print(
        "💡 This approach uses v2 hooks for monitoring while keeping Claude Code normal"
    )
    print()

    try:
        await launch_claude_with_monitoring(
            claude_cmd=args.claude_cmd,
            working_dir=args.working_dir,
            telegram_enabled=telegram_enabled,
        )
    except Exception as e:
        logger.error(f"❌ Error: {e}")
        return 1

    return 0


if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
