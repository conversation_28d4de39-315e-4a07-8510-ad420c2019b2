"""
RemoCode v4 - Log-Based State Detection Dual System

The breakthrough implementation that solves the fundamental dual interface problem
using clean subprocess management and intelligent log-based state detection.

Key innovations:
- Clean subprocess with PIPE (no PTY complexity)
- Log-based state detection (reliable state oracle)
- Adaptive Telegram interface (state-driven UI)
- Bidirectional synchronization (complete conversation flow)
"""

from .core.adaptive_telegram import AdaptiveT<PERSON>gram<PERSON>ot
from .core.claude_controller import <PERSON><PERSON><PERSON><PERSON><PERSON>
from .core.log_state_detector import <PERSON><PERSON><PERSON>, LogStateDetector
from .main import main

__version__ = "4.0.0"
__all__ = [
    "main",
    "ClaudeController",
    "LogStateDetector",
    "ClaudeState",
    "AdaptiveTelegramBot",
]
