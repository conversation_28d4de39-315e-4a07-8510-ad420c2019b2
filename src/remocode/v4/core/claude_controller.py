#!/usr/bin/env python3
"""
<PERSON> Controller - Non-Invasive Dual System

The truly working approach: Launch Claude Code completely normally,
no interception, no hooks, no PTY complexity. Just pure monitoring
and optional control for working outside.

Key principles:
- Claude Code runs 100% normally in terminal
- Zero interference with <PERSON>'s operation
- Passive monitoring via transcript files
- Optional control when requested from Telegram
"""

import asyncio
import logging
import os
from pathlib import Path
from typing import Optional


class ClaudeController:
    """
    Non-invasive Claude Code launcher

    Launches Claude Code completely normally with zero interference.
    The terminal experience is exactly as if you ran Claude Code directly.
    """

    def __init__(
        self,
        claude_cmd: str = "npx @anthropic-ai/claude-code",
        working_dir: Optional[str] = None,
    ):
        """
        Initialize non-invasive Claude controller

        Args:
            claude_cmd: Command to launch Claude Code
            working_dir: Working directory for Claude Code
        """
        self.claude_cmd = claude_cmd
        self.working_dir = Path(working_dir) if working_dir else Path.cwd()
        self.logger = logging.getLogger(__name__)

        # Simple process management
        self.process: Optional[asyncio.subprocess.Process] = None
        self.running = False

    async def start(self) -> None:
        """
        Start Claude Code completely normally - zero interference

        This is the key: Claude Code runs exactly as if you launched it directly.
        No PTY, no pipes, no interception, no complexity.
        """
        if self.running:
            self.logger.warning("Claude controller already running")
            return

        try:
            self.logger.info(f"🚀 Starting Claude Code: {self.claude_cmd}")
            self.logger.info(f"📁 Working directory: {self.working_dir}")
            self.logger.info(
                "🎯 Claude Code will run completely normally - zero interference"
            )

            # Parse command
            cmd_parts = self.claude_cmd.split()

            # Launch Claude Code with complete terminal inheritance
            # Force plan mode to ensure plan approval menus appear
            cmd_string = " ".join(cmd_parts)
            if (
                "--permission-mode" not in cmd_string
                and "permission-mode" not in cmd_string
            ):
                cmd_parts.extend(["--permission-mode", "plan"])
                self.logger.info("🎯 Added plan mode to force plan approval menus")

            self.logger.info(f"🎯 Final command: {' '.join(cmd_parts)}")

            self.process = await asyncio.create_subprocess_exec(
                *cmd_parts,
                cwd=str(self.working_dir),
                env=self._get_clean_env()
                # No stdin/stdout/stderr = inherit everything from parent terminal
            )

            self.running = True
            self.logger.info(
                f"✅ Claude Code started normally (PID: {self.process.pid})"
            )
            self.logger.info(
                "💡 Use Claude Code exactly as normal - monitoring is passive"
            )

            # Just wait for the process - no complex I/O handling
            await self._monitor_process()

        except Exception as e:
            self.logger.error(f"❌ Failed to start Claude Code: {e}")
            await self.stop()
            raise

    def _get_clean_env(self) -> dict:
        """Get environment for Claude subprocess"""
        env = os.environ.copy()

        # Ensure proper terminal settings for best Claude Code experience
        env.update(
            {
                "TERM": "xterm-256color",
                "FORCE_COLOR": "1",
                "PYTHONUNBUFFERED": "1",
                # Force plan mode via environment variable as backup
                "CLAUDE_PERMISSION_MODE": "plan",
            }
        )

        return env

    async def _monitor_process(self) -> None:
        """Simply monitor Claude process - no interference"""
        if not self.process:
            return

        try:
            self.logger.debug("👀 Monitoring Claude Code process...")

            # Just wait for the process to complete
            return_code = await self.process.wait()

            if return_code == 0:
                self.logger.info("🏁 Claude Code finished normally")
            else:
                self.logger.info(f"🏁 Claude Code finished (exit code: {return_code})")

        except Exception as e:
            self.logger.error(f"❌ Process monitoring error: {e}")
        finally:
            self.running = False

    async def send_input(self, text: str, source: str = "telegram") -> bool:
        """
        Send input to Claude Code via file-based communication

        Uses a simple file-based approach to inject input into Claude Code.
        This works by creating a temporary input file that can be processed.
        """
        self.logger.info(f"📨 Input request from {source}: {text}")

        try:
            # Create input file for Claude to process
            input_file = Path("remocode_input.txt")
            input_file.write_text(text + "\n", encoding="utf-8")

            # Show prominent notification in terminal
            print("\n" + "=" * 60)
            print("📱 TELEGRAM INPUT RECEIVED")
            print("=" * 60)
            print(f"💬 Command: {text}")
            print("📁 Saved to: remocode_input.txt")
            print("")
            print("🔄 TO USE THIS INPUT:")
            print("   1. Run: ./copy_telegram_input.sh")
            print("   2. Or: cat remocode_input.txt | pbcopy")
            print("   3. Then paste with Cmd+V in Claude Code above ↑")
            print("=" * 60 + "\n")

            return True

        except Exception as e:
            self.logger.error(f"❌ Failed to save input: {e}")
            return False

    async def stop(self) -> None:
        """Stop Claude controller"""
        if not self.running:
            return

        self.logger.info("🛑 Stopping Claude controller...")
        self.running = False

        # Terminate Claude process if still running
        if self.process and self.process.returncode is None:
            try:
                self.process.terminate()
                await asyncio.wait_for(self.process.wait(), timeout=5.0)
            except asyncio.TimeoutError:
                self.logger.warning("⚠️  Force killing Claude process")
                self.process.kill()
                await self.process.wait()
            except Exception as e:
                self.logger.error(f"Error terminating process: {e}")

        self.logger.info("✅ Claude controller stopped")
