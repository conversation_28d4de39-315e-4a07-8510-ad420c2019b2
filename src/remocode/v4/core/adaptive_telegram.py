#!/usr/bin/env python3
"""
Adaptive Telegram Bot - State-Driven Interface

The breakthrough Telegram integration that adapts its interface
based on <PERSON>'s current state for optimal user experience.

Key innovations:
- State-driven UI adaptation (working/idle/menu)
- Intelligent batching for working states
- Read-only plan monitoring with optional control
- Clean bidirectional synchronization
"""

import asyncio
import json
import logging
import os
import time
import urllib.parse
import urllib.request
from dataclasses import dataclass
from typing import Any, Callable, Dict, List, Optional

from .log_state_detector import Claude<PERSON><PERSON>, StateEvent


@dataclass
class TelegramMessage:
    """Represents a Telegram message"""

    text: str
    chat_id: str
    message_id: Optional[int] = None
    reply_markup: Optional[Dict] = None


class AdaptiveTelegramBot:
    """
    State-driven Telegram bot that adapts interface based on <PERSON>'s state

    Provides intelligent monitoring and optional control without interfering
    with the primary terminal interface.
    """

    def __init__(self):
        """Initialize adaptive Telegram bot"""
        self.logger = logging.getLogger(__name__)

        # Telegram configuration
        self.token = os.getenv("TG_TOKEN")
        self.chat_id = os.getenv("TG_CHAT")

        if not self.token or not self.chat_id:
            raise ValueError("TG_TOKEN and TG_CHAT environment variables required")

        # State management
        self.running = False
        self.current_state = ClaudeState.UNKNOWN
        self.last_update_id = 0

        # Input handling
        self.input_handler: Optional[Callable[[str, str], bool]] = None

        # Message batching for working states
        self.working_message_id: Optional[int] = None
        self.working_start_time: Optional[float] = None

        # Rate limiting
        self.message_timestamps: List[float] = []
        self.max_messages_per_minute = 20

    def set_input_handler(self, handler: Callable[[str, str], bool]) -> None:
        """Set handler for sending input to Claude"""
        self.input_handler = handler

    async def start(self) -> None:
        """Start the adaptive Telegram bot"""
        self.logger.info("📱 Starting adaptive Telegram bot")
        self.running = True

        # Send startup notification
        await self._send_message(
            "🚀 RemoCode v4 Dual System Started\n\n"
            "🖥️ Terminal: Primary interface active\n"
            "📱 Telegram: Monitoring and optional control enabled\n"
            "🔄 Log-based state detection active"
        )

        # Start update polling
        await self._poll_updates()

    async def handle_state_change(self, event: StateEvent) -> None:
        """
        Handle Claude state changes with adaptive UI

        This is the breakthrough - different UI for different states!
        """
        self.current_state = event.state

        if event.state == ClaudeState.WORKING:
            await self._handle_working_state(event)
        elif event.state == ClaudeState.IDLE:
            await self._handle_idle_state(event)
        elif event.state == ClaudeState.MENU:
            await self._handle_menu_state(event)

    async def _handle_working_state(self, event: StateEvent) -> None:
        """Handle working state with intelligent batching"""
        if event.metadata and event.metadata.get("batched"):
            # This is a batched working update
            if self.working_message_id:
                # Update existing working message
                await self._edit_message(
                    self.working_message_id,
                    f"⚡ Claude Working\n\n{event.content}\n\n"
                    f"🕐 Started: {time.strftime('%H:%M:%S', time.localtime(self.working_start_time))}",
                )
            else:
                # Send new working message
                self.working_start_time = time.time()
                message = await self._send_message(
                    f"⚡ Claude Working\n\n{event.content}\n\n"
                    f"🕐 Started: {time.strftime('%H:%M:%S')}"
                )
                if message:
                    self.working_message_id = message.get("message_id")
        else:
            # Individual working event - just log it
            self.logger.debug(f"Working event: {event.content[:50]}...")

    async def _handle_idle_state(self, event: StateEvent) -> None:
        """Handle idle state with optional text input"""
        # Clear working message
        self.working_message_id = None

        # Show idle notification with optional input
        keyboard = {
            "inline_keyboard": [
                [
                    {"text": "💬 Send Message", "callback_data": "send_message"},
                    {"text": "ℹ️ Status", "callback_data": "status"},
                ]
            ]
        }

        await self._send_message(
            "💤 Claude Idle\n\n"
            "Claude is waiting for input in the terminal.\n\n"
            "You can continue using the terminal normally, or click 'Send Message' "
            "to send a command from Telegram.",
            reply_markup=keyboard,
        )

    async def _handle_menu_state(self, event: StateEvent) -> None:
        """Handle menu state with read-only display and optional control"""
        # Clear working message
        self.working_message_id = None

        # Extract menu options from content
        menu_text = self._extract_menu_text(event.content)

        # Create optional control buttons
        keyboard = {
            "inline_keyboard": [
                [
                    {"text": "✅ Yes, auto-accept", "callback_data": "menu_1"},
                    {"text": "👀 Yes, manually approve", "callback_data": "menu_2"},
                ],
                [
                    {"text": "❌ No, keep planning", "callback_data": "menu_3"},
                    {
                        "text": "ℹ️ Terminal handles this",
                        "callback_data": "terminal_control",
                    },
                ],
            ]
        }

        await self._send_message(
            f"📋 Plan Approval Menu\n\n"
            f"{menu_text}\n\n"
            f"🖥️ This menu is active in your terminal.\n"
            f"You can respond there normally, or use the buttons below for remote control.",
            reply_markup=keyboard,
        )

    def _extract_menu_text(self, content: str) -> str:
        """Extract clean menu text from log content"""
        # Remove log prefixes and clean up
        lines = content.split("\n")
        clean_lines = []

        for line in lines:
            # Remove timestamp and log prefixes
            clean_line = line
            if "] " in line:
                clean_line = line.split("] ", 1)[-1]

            # Keep lines with menu options or questions
            if any(
                indicator in clean_line.lower()
                for indicator in ["❯", "1.", "2.", "3.", "would you like", "ready to"]
            ):
                clean_lines.append(clean_line.strip())

        return "\n".join(clean_lines) if clean_lines else content[:200]

    async def _poll_updates(self) -> None:
        """Poll for Telegram updates"""
        while self.running:
            try:
                updates = await self._get_updates()

                for update in updates:
                    await self._process_update(update)
                    self.last_update_id = update["update_id"]

            except Exception as e:
                self.logger.error(f"❌ Update polling error: {e}")
                await asyncio.sleep(5)  # Wait before retrying

            await asyncio.sleep(1)  # Poll every second

    async def _process_update(self, update: Dict[str, Any]) -> None:
        """Process a single Telegram update"""
        if "callback_query" in update:
            await self._handle_callback_query(update["callback_query"])
        elif "message" in update and "text" in update["message"]:
            await self._handle_text_message(update["message"])

    async def _handle_callback_query(self, callback_query: Dict[str, Any]) -> None:
        """Handle callback query from inline keyboard"""
        data = callback_query["data"]

        # Acknowledge the callback
        await self._answer_callback_query(callback_query["id"])

        if data == "send_message":
            await self._request_text_input(callback_query["message"]["message_id"])
        elif data == "status":
            await self._send_status_update()
        elif data.startswith("menu_"):
            await self._handle_menu_choice(data)
        elif data == "terminal_control":
            await self._edit_message(
                callback_query["message"]["message_id"],
                "ℹ️ Menu is being handled in terminal.\n\n"
                "The terminal interface remains the primary control method.",
            )

    async def _request_text_input(self, message_id: int) -> None:
        """Request text input from user"""
        await self._edit_message(
            message_id,
            "💬 Send Text Input\n\n"
            "Reply to this message with the text you want to send to Claude.\n\n"
            "Your message will be forwarded to Claude and visible in the terminal.",
        )

    async def _handle_menu_choice(self, choice_data: str) -> None:
        """Handle menu choice from Telegram"""
        choice_map = {"menu_1": "1", "menu_2": "2", "menu_3": "3"}

        choice = choice_map.get(choice_data)
        if choice and self.input_handler:
            success = await self.input_handler(choice, "telegram")

            if success:
                await self._send_message(
                    f"✅ Menu choice sent: Option {choice}\n\n"
                    f"This choice has been forwarded to Claude and is visible in the terminal."
                )
            else:
                await self._send_message("❌ Failed to send menu choice to Claude")

    async def _handle_text_message(self, message: Dict[str, Any]) -> None:
        """Handle text message from user"""
        text = message["text"].strip()

        if text and self.input_handler:
            success = await self.input_handler(text, "telegram")

            if success:
                await self._send_message(
                    f"✅ Message sent to Claude:\n\n{text}\n\n"
                    f"This message has been forwarded and is visible in the terminal."
                )
            else:
                await self._send_message("❌ Failed to send message to Claude")

    def echo_to_terminal(self, text: str, source: str) -> None:
        """Echo Telegram input to terminal (called by Claude controller)"""
        print(f"\n📱 [Telegram] {text}\n", flush=True)

    async def _send_status_update(self) -> None:
        """Send current status update"""
        status_text = (
            f"📊 RemoCode v4 Status\n\n"
            f"🔄 Current State: {self.current_state.value.title()}\n"
            f"🕐 Time: {time.strftime('%H:%M:%S')}\n"
            f"🖥️ Terminal: Active\n"
            f"📱 Telegram: Monitoring"
        )

        await self._send_message(status_text)

    # Telegram API methods
    async def _send_message(
        self, text: str, reply_markup: Optional[Dict] = None
    ) -> Optional[Dict]:
        """Send message to Telegram"""
        if not self._check_rate_limit():
            self.logger.warning("Rate limit exceeded, skipping message")
            return None

        try:
            params = {
                "chat_id": self.chat_id,
                "text": text[:4096],  # Telegram message limit
                "parse_mode": "HTML",
            }

            if reply_markup:
                params["reply_markup"] = json.dumps(reply_markup)

            response = await self._telegram_request("sendMessage", params)
            return response.get("result")

        except Exception as e:
            self.logger.error(f"❌ Failed to send message: {e}")
            return None

    async def _edit_message(
        self, message_id: int, text: str, reply_markup: Optional[Dict] = None
    ) -> bool:
        """Edit existing message"""
        try:
            params = {
                "chat_id": self.chat_id,
                "message_id": message_id,
                "text": text[:4096],
                "parse_mode": "HTML",
            }

            if reply_markup:
                params["reply_markup"] = json.dumps(reply_markup)

            await self._telegram_request("editMessageText", params)
            return True

        except Exception as e:
            self.logger.error(f"❌ Failed to edit message: {e}")
            return False

    async def _get_updates(self) -> List[Dict[str, Any]]:
        """Get updates from Telegram"""
        params = {"offset": self.last_update_id + 1, "timeout": 1}

        response = await self._telegram_request("getUpdates", params)
        return response.get("result", [])

    async def _answer_callback_query(self, callback_query_id: str) -> None:
        """Answer callback query"""
        try:
            await self._telegram_request(
                "answerCallbackQuery", {"callback_query_id": callback_query_id}
            )
        except Exception as e:
            self.logger.debug(f"Failed to answer callback query: {e}")

    async def _telegram_request(
        self, method: str, params: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Make request to Telegram API"""
        url = f"https://api.telegram.org/bot{self.token}/{method}"

        data = json.dumps(params).encode("utf-8")
        req = urllib.request.Request(
            url, data=data, headers={"Content-Type": "application/json"}
        )

        loop = asyncio.get_event_loop()
        response = await loop.run_in_executor(None, urllib.request.urlopen, req)

        return json.loads(response.read().decode("utf-8"))

    def _check_rate_limit(self) -> bool:
        """Check if we're within rate limits"""
        current_time = time.time()

        # Remove old timestamps
        self.message_timestamps = [
            ts
            for ts in self.message_timestamps
            if current_time - ts < 60  # Keep last minute
        ]

        # Check limit
        if len(self.message_timestamps) >= self.max_messages_per_minute:
            return False

        # Add current timestamp
        self.message_timestamps.append(current_time)
        return True

    async def stop(self) -> None:
        """Stop the Telegram bot"""
        self.logger.info("🛑 Stopping adaptive Telegram bot...")
        self.running = False

        try:
            await self._send_message("🛑 RemoCode v4 Dual System Stopped")
        except Exception:
            pass  # Don't fail on shutdown message

        self.logger.info("✅ Adaptive Telegram bot stopped")
