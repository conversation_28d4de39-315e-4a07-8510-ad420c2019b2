#!/usr/bin/env python3
"""
Transcript State Detector - Non-Invasive Monitoring

Uses Claude Code transcript files for passive monitoring, providing
state detection without any interference with <PERSON>'s operation.

Key innovations:
- Transcript-based monitoring (completely passive)
- No interference with Claude Code operation
- Real-time transcript file monitoring
- Simple state detection for Telegram updates
"""

import asyncio
import logging
import time
from dataclasses import dataclass
from enum import Enum
from pathlib import Path
from typing import Any, Callable, Dict, List, Optional


class ClaudeState(Enum):
    """<PERSON>'s operational states detected from logs"""

    WORKING = "working"  # <PERSON> is actively processing (⏺ equivalent)
    IDLE = "idle"  # <PERSON> waiting for input (> equivalent)
    MENU = "menu"  # <PERSON> presenting menu options (> with choices)
    UNKNOWN = "unknown"  # Unable to determine state


@dataclass
class StateEvent:
    """Represents a state change event"""

    state: ClaudeState
    content: str
    timestamp: float
    metadata: Dict[str, Any] = None

    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


class TranscriptStateDetector:
    """
    Non-invasive transcript file monitor for passive state detection

    Monitors Claude Code transcript files to provide Telegram updates
    without any interference with <PERSON>'s normal operation.
    """

    def __init__(self, claude_dir: str = ".claude"):
        """
        Initialize transcript state detector

        Args:
            claude_dir: Path to Claude Code directory containing transcripts
        """
        self.claude_dir = Path(claude_dir)
        self.logger = logging.getLogger(__name__)

        # State management
        self.current_state = ClaudeState.UNKNOWN
        self.running = False

        # Callback system
        self.state_callbacks: List[Callable[[StateEvent], None]] = []

        # Session tracking
        self.monitored_sessions = set()
        self.last_activity_time = 0.0

        # Simple patterns for transcript content
        self.activity_patterns = [
            r"tool_use",  # Claude using tools
            r"text_editor",  # File editing
            r"bash",  # Shell commands
            r"str_replace_editor",  # Code editing
            r"view",  # File viewing
        ]

        self.plan_patterns = [
            r"plan",
            r"proceed",
            r"implement",
            r"ready to",
            r"would you like",
        ]

    def register_callback(self, callback: Callable[[StateEvent], None]) -> None:
        """Register callback for state change events"""
        self.state_callbacks.append(callback)
        self.logger.debug(f"Registered state callback: {callback.__name__}")

    async def start_monitoring(self) -> None:
        """Start passive transcript monitoring"""
        self.logger.info(f"🔍 Starting transcript monitoring: {self.claude_dir}")

        # Ensure Claude directory exists
        self.claude_dir.mkdir(exist_ok=True)

        self.running = True

        # Start monitoring tasks
        tasks = [
            asyncio.create_task(self._monitor_sessions()),
            asyncio.create_task(self._periodic_activity_check()),
        ]

        try:
            await asyncio.gather(*tasks)
        except Exception as e:
            self.logger.error(f"❌ Transcript monitoring error: {e}")
        finally:
            self.running = False

    async def _monitor_sessions(self) -> None:
        """Monitor Claude Code sessions for activity"""
        self.logger.debug("📄 Started session monitoring")

        try:
            while self.running:
                # Look for session directories
                sessions_dir = self.claude_dir / "sessions"
                if sessions_dir.exists():
                    for session_dir in sessions_dir.iterdir():
                        if (
                            session_dir.is_dir()
                            and session_dir.name not in self.monitored_sessions
                        ):
                            # New session found
                            self.monitored_sessions.add(session_dir.name)
                            await self._notify_new_session(session_dir.name)

                        # Check for activity in existing sessions
                        if session_dir.is_dir():
                            await self._check_session_activity(session_dir)

                # Wait before next check
                await asyncio.sleep(2.0)

        except Exception as e:
            if self.running:
                self.logger.error(f"❌ Session monitoring error: {e}")

    async def _notify_new_session(self, session_id: str) -> None:
        """Notify about new Claude session"""
        event = StateEvent(
            state=ClaudeState.WORKING,
            content=f"New Claude Code session started: {session_id}",
            timestamp=time.time(),
            metadata={"session_id": session_id, "event_type": "session_start"},
        )

        await self._trigger_state_callbacks(event)

    async def _check_session_activity(self, session_dir: Path) -> None:
        """Check for activity in a session"""
        try:
            transcript_file = session_dir / "transcript.jsonl"
            if not transcript_file.exists():
                return

            # Check file modification time
            mtime = transcript_file.stat().st_mtime
            if mtime > self.last_activity_time:
                self.last_activity_time = mtime

                # Read recent activity
                activity = await self._read_recent_activity(transcript_file)
                if activity:
                    await self._process_activity(activity, session_dir.name)

        except Exception as e:
            self.logger.debug(f"Error checking session activity: {e}")

    async def _read_recent_activity(self, transcript_file: Path) -> Optional[str]:
        """Read recent activity from transcript file"""
        try:
            # Read last few lines of transcript
            with open(transcript_file, "r", encoding="utf-8") as f:
                lines = f.readlines()
                if lines:
                    # Return last line for analysis
                    return lines[-1].strip()
        except Exception as e:
            self.logger.debug(f"Error reading transcript: {e}")

        return None

    async def _process_activity(self, activity_line: str, session_id: str) -> None:
        """Process activity line and determine state"""
        # Simple activity detection
        if any(pattern in activity_line.lower() for pattern in self.activity_patterns):
            event = StateEvent(
                state=ClaudeState.WORKING,
                content=f"Claude is working in session {session_id}",
                timestamp=time.time(),
                metadata={"session_id": session_id, "activity": activity_line[:100]},
            )
            await self._trigger_state_callbacks(event)

        elif any(pattern in activity_line.lower() for pattern in self.plan_patterns):
            event = StateEvent(
                state=ClaudeState.MENU,
                content=f"Plan approval may be needed in session {session_id}",
                timestamp=time.time(),
                metadata={
                    "session_id": session_id,
                    "plan_context": activity_line[:200],
                },
            )
            await self._trigger_state_callbacks(event)

    async def _periodic_activity_check(self) -> None:
        """Periodic check for idle state - reduced frequency to avoid spam"""
        last_idle_notification = 0

        while self.running:
            await asyncio.sleep(120)  # Check every 2 minutes (reduced frequency)

            # Only send idle notification once every 10 minutes to avoid spam
            current_time = time.time()
            if (
                current_time - self.last_activity_time > 300
                and current_time - last_idle_notification > 600  # 5 minutes idle
            ):  # 10 minutes since last notification
                event = StateEvent(
                    state=ClaudeState.IDLE,
                    content="Claude has been idle for a while",
                    timestamp=current_time,
                    metadata={"event_type": "idle_detection"},
                )
                await self._trigger_state_callbacks(event)
                last_idle_notification = current_time

    async def _trigger_state_callbacks(self, event: StateEvent) -> None:
        """Trigger callbacks for state change"""
        self.logger.info(
            f"🔄 State event: {event.state.value} - {event.content[:100]}..."
        )

        # Update current state
        self.current_state = event.state

        # Trigger all registered callbacks
        for callback in self.state_callbacks:
            try:
                # Handle both sync and async callbacks
                if asyncio.iscoroutinefunction(callback):
                    await callback(event)
                else:
                    callback(event)

            except Exception as e:
                self.logger.error(f"❌ State callback error ({callback.__name__}): {e}")

    def register_callback(self, callback: Callable[[StateEvent], None]) -> None:
        """Register callback for state changes"""
        self.state_callbacks.append(callback)
        self.logger.debug(f"📝 Registered state callback: {callback.__name__}")

    def get_current_state(self) -> ClaudeState:
        """Get current Claude state"""
        return self.current_state

    async def stop(self) -> None:
        """Stop transcript monitoring"""
        self.logger.info("🛑 Stopping transcript state detector...")
        self.running = False
        self.logger.info("✅ Transcript state detector stopped")


# Compatibility alias for existing code
LogStateDetector = TranscriptStateDetector
