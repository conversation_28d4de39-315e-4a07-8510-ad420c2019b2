#!/usr/bin/env python3
"""
Simple Telegram Bot - Non-Invasive Monitoring

A simple Telegram bot that provides monitoring and basic control
for the dual system without interfering with Claude Code operation.

Key features:
- Passive monitoring updates
- Simple input suggestions (not direct injection)
- Status reporting
- Clean, minimal interface
"""

import asyncio
import logging
import time
from typing import Callable, Optional

from telegram import InlineKeyboardButton, InlineKeyboardMarkup, Update
from telegram.ext import (
    Application,
    CallbackQueryHandler,
    CommandHandler,
    MessageHandler,
    filters,
)

from .log_state_detector import ClaudeState, StateEvent


class SimpleTelegramBot:
    """
    Simple Telegram bot for non-invasive Claude monitoring

    Provides basic monitoring and status updates without complex
    state management or direct process interference.
    """

    def __init__(self, token: str, chat_id: str):
        """
        Initialize simple Telegram bot

        Args:
            token: Telegram bot token
            chat_id: Target chat ID for notifications
        """
        self.token = token
        self.chat_id = chat_id
        self.logger = logging.getLogger(__name__)

        # Bot management
        self.application: Optional[Application] = None
        self.running = False

        # Input handler for Claude controller
        self.input_handler: Optional[Callable[[str, str], bool]] = None

        # Simple state tracking
        self.last_update_time = 0.0
        self.session_start_time = time.time()

    async def start(self) -> None:
        """Start the Telegram bot"""
        try:
            self.logger.info("📱 Starting simple Telegram bot...")

            # Create application
            self.application = Application.builder().token(self.token).build()

            # Add handlers
            self.application.add_handler(CommandHandler("start", self._handle_start))
            self.application.add_handler(CommandHandler("status", self._handle_status))
            self.application.add_handler(
                MessageHandler(filters.TEXT & ~filters.COMMAND, self._handle_message)
            )
            self.application.add_handler(CallbackQueryHandler(self._handle_callback))

            # Start bot
            await self.application.initialize()
            await self.application.start()

            self.running = True
            self.logger.info("✅ Simple Telegram bot started")

            # Send startup notification
            await self._send_startup_notification()

            # Keep running
            while self.running:
                await asyncio.sleep(1)

        except Exception as e:
            self.logger.error(f"❌ Telegram bot error: {e}")
            raise

    async def _send_startup_notification(self) -> None:
        """Send startup notification"""
        message = (
            "🚀 *RemoCode v4 Dual System Started*\n\n"
            "🖥️ *Terminal*: Claude Code running normally\n"
            "📱 *Telegram*: Passive monitoring active\n"
            "🔄 *Mode*: Non-invasive dual system\n\n"
            "💡 Use Claude Code normally in terminal.\n"
            "📊 This bot will show activity updates."
        )

        keyboard = InlineKeyboardMarkup(
            [
                [InlineKeyboardButton("📊 Status", callback_data="status")],
                [InlineKeyboardButton("💬 Send Input", callback_data="input")],
            ]
        )

        await self._send_message(message, keyboard)

    async def _handle_start(self, update: Update, context) -> None:
        """Handle /start command"""
        await self._send_startup_notification()

    async def _handle_status(self, update: Update, context) -> None:
        """Handle /status command"""
        uptime = int(time.time() - self.session_start_time)
        hours, remainder = divmod(uptime, 3600)
        minutes, seconds = divmod(remainder, 60)

        message = (
            "📊 *RemoCode v4 Status*\n\n"
            f"⏱️ *Uptime*: {hours:02d}:{minutes:02d}:{seconds:02d}\n"
            f"🔄 *Last Update*: {int(time.time() - self.last_update_time)}s ago\n"
            f"🖥️ *Terminal*: Claude Code running normally\n"
            f"📱 *Telegram*: Monitoring active\n\n"
            "💡 Claude Code is running in your terminal.\n"
            "Use it normally - this bot just provides updates."
        )

        await update.message.reply_text(message, parse_mode="Markdown")

    async def _handle_message(self, update: Update, context) -> None:
        """Handle text messages (input suggestions)"""
        user_input = update.message.text

        # Send input suggestion to Claude controller
        if self.input_handler:
            success = await self.input_handler(user_input, "telegram")

            if success:
                await update.message.reply_text(
                    f"✅ *Input Saved Successfully*\n\n"
                    f"📝 Command: `{user_input}`\n\n"
                    f"💡 *Next Steps:*\n"
                    f"1. Check your terminal - you'll see the input notification\n"
                    f"2. Copy the text from `remocode_input.txt` or\n"
                    f"3. Use: `cat remocode_input.txt | pbcopy` to copy to clipboard\n"
                    f"4. Paste it into Claude Code\n\n"
                    f"🎯 This ensures your remote input reaches Claude!",
                    parse_mode="Markdown",
                )
            else:
                await update.message.reply_text(
                    "❌ *Failed to save input*\n\n"
                    "💡 Try typing your request directly in the terminal.",
                    parse_mode="Markdown",
                )
        else:
            await update.message.reply_text(
                f"📝 *Received:* `{user_input}`\n\n"
                "⚠️ Input handler not connected.\n"
                "💡 Type this directly in your Claude Code terminal.",
                parse_mode="Markdown",
            )

    async def _handle_callback(self, update: Update, context) -> None:
        """Handle callback queries"""
        query = update.callback_query

        try:
            # Always answer the callback query first to stop loading
            await query.answer()

            if query.data == "status":
                # Handle status request
                uptime = int(time.time() - self.session_start_time)
                hours, remainder = divmod(uptime, 3600)
                minutes, seconds = divmod(remainder, 60)

                status_message = (
                    "📊 *RemoCode v4 Status*\n\n"
                    f"⏱️ *Uptime*: {hours:02d}:{minutes:02d}:{seconds:02d}\n"
                    f"🔄 *Last Update*: {int(time.time() - self.last_update_time)}s ago\n"
                    f"🖥️ *Terminal*: Claude Code running\n"
                    f"📱 *Telegram*: Monitoring active\n\n"
                    "💡 Claude Code is running in your terminal."
                )

                await query.edit_message_text(status_message, parse_mode="Markdown")

            elif query.data == "input":
                # Handle input request
                input_message = (
                    "💬 *Send Input to Claude*\n\n"
                    "📝 Type your message below and I'll save it for you.\n\n"
                    "🔄 *How it works:*\n"
                    "1. Type your command in the next message\n"
                    "2. I'll save it to `remocode_input.txt`\n"
                    "3. Use `./copy_telegram_input.sh` to copy to clipboard\n"
                    "4. Paste into Claude Code with Cmd+V\n\n"
                    "💡 This ensures your remote input reaches Claude!"
                )

                await query.edit_message_text(input_message, parse_mode="Markdown")

        except Exception as e:
            self.logger.error(f"❌ Callback handler error: {e}")
            # Try to answer the query even if there's an error
            try:
                await query.answer("❌ Error processing request")
            except:
                pass

    async def handle_state_change(self, event: StateEvent) -> None:
        """Handle state change events from transcript detector"""
        self.last_update_time = time.time()

        # Filter out spam - only send important updates
        if event.state == ClaudeState.WORKING:
            message = f"⚡ *Claude Working*\n\n{event.content}"
            await self._send_message(message)

        elif event.state == ClaudeState.MENU:
            message = (
                f"📋 *Plan Approval Needed*\n\n"
                f"{event.content}\n\n"
                f"🖥️ Check your terminal to approve the plan.\n"
                f"💡 You can also send input from here if needed."
            )

            keyboard = InlineKeyboardMarkup(
                [
                    [InlineKeyboardButton("💬 Send Input", callback_data="input")],
                    [InlineKeyboardButton("📊 Status", callback_data="status")],
                ]
            )

            await self._send_message(message, keyboard)

        elif event.state == ClaudeState.IDLE:
            # Only send idle notifications occasionally to avoid spam
            if "idle for a while" in event.content.lower():
                message = (
                    "💤 *Claude Idle*\n\n"
                    "Claude has been waiting for input.\n\n"
                    "💡 You can send commands from here or use the terminal."
                )

                keyboard = InlineKeyboardMarkup(
                    [
                        [InlineKeyboardButton("💬 Send Input", callback_data="input")],
                    ]
                )

                await self._send_message(message, keyboard)

    def set_input_handler(self, handler: Callable[[str, str], bool]) -> None:
        """Set input handler for Claude controller"""
        self.input_handler = handler

    async def _send_message(self, text: str, keyboard=None) -> None:
        """Send message to Telegram"""
        try:
            if self.application and self.application.bot:
                await self.application.bot.send_message(
                    chat_id=self.chat_id,
                    text=text,
                    parse_mode="Markdown",
                    reply_markup=keyboard,
                )
        except Exception as e:
            self.logger.error(f"❌ Failed to send Telegram message: {e}")

    async def stop(self) -> None:
        """Stop the Telegram bot"""
        self.logger.info("🛑 Stopping simple Telegram bot...")
        self.running = False

        if self.application:
            await self.application.stop()
            await self.application.shutdown()

        self.logger.info("✅ Simple Telegram bot stopped")


# Compatibility alias
AdaptiveTelegramBot = SimpleTelegramBot
