# RemoCode v4 - Log-Based Dual System

## 🎯 The Breakthrough Implementation

RemoCode v4 represents the breakthrough solution to the dual interface problem, using **log-based state detection** and **clean subprocess management** to provide a truly seamless bidirectional interface between terminal and Telegram.

## 🔑 Key Innovations

### 1. **Clean Subprocess Management** (No PTY Complexity)
- Uses simple `stdin/stdout/stderr` pipes instead of complex PTY
- Eliminates the file descriptor juggling that plagued v1
- Reliable process lifecycle management
- No race conditions or terminal state issues

### 2. **Log-Based State Detection** (The Breakthrough)
- Uses structured logs as a reliable "state oracle"
- No more fragile output parsing or ANSI escape sequence issues
- Real-time file tailing with intelligent event batching
- Precise detection of working/idle/menu states

### 3. **Adaptive Telegram Interface** (State-Driven UI)
- **Working State**: Batched progress updates (reduces noise)
- **Idle State**: Optional text input interface
- **Menu State**: Read-only plan display with optional approval buttons
- Context-aware interface that adapts to <PERSON>'s current mode

### 4. **True Bidirectional Synchronization**
- Terminal remains the primary interface (works exactly as normal)
- Telegram provides monitoring and optional control
- Both interfaces show complete conversation flow
- Clean echo system for Telegram inputs in terminal

## 🏗️ Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Terminal      │    │  Claude Code     │    │   Telegram      │
│   Interface     │◄──►│   Subprocess     │◄──►│   Interface     │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         ▲                        │                       ▲
         │                        ▼                       │
         │              ┌──────────────────┐              │
         │              │  logs/remocode   │              │
         │              │      .log        │              │
         │              └──────────────────┘              │
         │                        ▲                       │
         │                        │                       │
         └────────────────────────┼───────────────────────┘
                                  ▼
                        ┌──────────────────┐
                        │ Log State        │
                        │ Detector         │
                        └──────────────────┘
```

## 🚀 Quick Start

### Prerequisites
- Node.js with `@anthropic-ai/claude-code` installed
- Python 3.8+ with asyncio support
- Telegram bot token and chat ID (optional)

### Installation
```bash
cd /Users/<USER>/Desktop/github/remocode
```

### Basic Usage (Terminal Only)
```bash
# Start v4 without Telegram
uv run remocode v4 --no-telegram
```

### Full Dual System
```bash
# Set Telegram credentials
export TG_TOKEN="your_bot_token"
export TG_CHAT="your_chat_id"

# Start v4 with full dual system
uv run remocode v4
```

### Advanced Options
```bash
# Debug mode
uv run remocode v4 --debug

# Custom Claude command
uv run remocode v4 --claude-cmd "claude --model claude-3-5-sonnet"

# Custom working directory
uv run remocode v4 --working-dir /path/to/project
```

## 📱 User Experience

### Terminal Interface
- **Normal Operation**: Claude Code works exactly as usual
- **Full Functionality**: Type requests, see responses, interact normally
- **Plan Approval**: Menus appear in terminal as normal
- **Telegram Echo**: See Telegram inputs with clear `📱 [Telegram]` labels

### Telegram Interface
- **Working State**: Batched progress updates
  ```
  ⚡ Claude Working

  📊 Claude processed 5 operations

  🕐 Started: 14:30:25
  ```

- **Idle State**: Optional text input
  ```
  💤 Claude Idle

  Claude is waiting for input in the terminal.

  [💬 Send Message] [ℹ️ Status]
  ```

- **Menu State**: Read-only plan display with optional control
  ```
  📋 Plan Approval Menu

  ❯ 1. Yes, and auto-accept edits
  ❯ 2. Yes, and manually approve edits
  ❯ 3. No, keep planning

  🖥️ This menu is active in your terminal.

  [✅ Yes, auto-accept] [👀 Yes, manually approve]
  [❌ No, keep planning] [ℹ️ Terminal handles this]
  ```

## 🔧 Technical Details

### Components

#### ClaudeController
- Manages Claude Code as controlled subprocess
- Handles clean I/O with pipes (no PTY)
- Provides bidirectional input/output
- Logs all activity for state detection

#### LogStateDetector
- Real-time log file monitoring
- Pattern-based state detection
- Intelligent event batching
- Callback system for state changes

#### AdaptiveTelegramBot
- State-driven interface adaptation
- Rate limiting and error handling
- Bidirectional message handling
- Clean integration with Claude controller

### State Detection Patterns

```python
# Working State Patterns
r'\[STDOUT\].*❯.*\d+\.\s+'     # Menu options appearing
r'\[STDOUT\].*Reading.*file'    # File operations
r'\[INPUT\[terminal\]\]'        # User input triggers work

# Idle State Patterns
r'\[STDOUT\].*waiting for.*input'
r'\[STDOUT\].*What would you like.*'

# Menu State Patterns
r'\[STDOUT\].*❯\s*1\.\s+Yes.*auto-accept'
r'\[STDOUT\].*Would you like.*proceed.*plan'
```

## 🧪 Testing

Run the comprehensive test suite:
```bash
cd /Users/<USER>/Desktop/github/remocode
python test_v4_system.py
```

Expected output:
```
🚀 RemoCode v4 System Test Suite
============================================================

📋 Running Log State Detector Test...
  📊 State change: working - [STDOUT] Reading file: test.py...
  ✅ working state detected
  ✅ menu state detected
  ✅ idle state detected

📋 Running Claude Controller Test...
  ✅ Controller started and stopped successfully
  ✅ Input sending: success

📋 Running Adaptive Telegram Test...
  ✅ Handled working state
  ✅ Handled idle state
  ✅ Handled menu state

📋 Running Component Integration Test...
  ✅ Components created and connected
  ✅ Integration test completed

============================================================
🎯 Test Results Summary:
  ✅ PASSED - Log State Detector
  ✅ PASSED - Claude Controller
  ✅ PASSED - Adaptive Telegram
  ✅ PASSED - Component Integration

📊 Overall: 4/4 tests passed

🎉 All tests passed! RemoCode v4 is ready to use.
```

## 🔍 Troubleshooting

### Common Issues

**Claude Code not starting**
- Check that `npx @anthropic-ai/claude-code` works in terminal
- Verify working directory permissions
- Check logs in `logs/remocode_v4.log`

**Telegram not working**
- Verify `TG_TOKEN` and `TG_CHAT` environment variables
- Test bot with `/start` command
- Check network connectivity

**State detection not working**
- Check `logs/remocode.log` file exists and is being written
- Verify log patterns match your Claude Code version
- Enable debug mode with `--debug`

### Debug Mode
```bash
uv run remocode v4 --debug
```

This enables detailed logging to help diagnose issues.

## 🎯 Why v4 Succeeds Where Others Failed

### vs v1 (PTY-based)
- ❌ v1: Complex PTY management, race conditions, fragile output parsing
- ✅ v4: Clean subprocess pipes, reliable log-based state detection

### vs v2 (Hook-based)
- ❌ v2: Breaks Claude's normal flow, tight coupling, fragile with updates
- ✅ v4: Zero coupling to Claude internals, transparent operation

### vs v3 (Transcript-based)
- ❌ v3: Limited to transcript files, no plan approval menus, read-only
- ✅ v4: Complete state information from logs, full bidirectional control

## 🚀 The Breakthrough Formula

```
Clean Subprocess + Log State Detection + Adaptive UI = Working Dual System
```

**Key Insight**: Don't fight the terminal, use the logs as a state oracle!

RemoCode v4 finally delivers the true dual interface experience with:
- **Reliability**: No PTY complexity or hook fragility
- **Intelligence**: State-driven adaptive interface
- **Transparency**: Terminal works exactly as normal
- **Control**: Optional Telegram control without interference

This is the implementation that actually works. 🎯
