"""
Menu detection and parsing for Claude CLI output.

<PERSON>les detecting numbered menu options, generating Telegram buttons,
and managing user responses from both terminal and Telegram.
"""

import asyncio
import logging
import re
from collections import deque
from dataclasses import dataclass
from enum import Enum
from typing import List, Optional

logger = logging.getLogger(__name__)


class MenuType(Enum):
    """Types of menus detected in Claude output."""

    NUMBERED_OPTIONS = "numbered_options"
    YES_NO_PROMPT = "yes_no_prompt"
    INTERACTIVE_PROMPT = "interactive_prompt"
    PLAN_APPROVAL = "plan_approval"


@dataclass
class MenuOption:
    """Represents a single menu option."""

    number: int
    text: str
    full_line: str


@dataclass
class DetectedMenu:
    """Represents a detected menu with its options and context."""

    menu_type: MenuType
    options: List[MenuOption]
    context_lines: List[str]
    raw_output: str

    @property
    def option_count(self) -> int:
        """Get the number of options in this menu."""
        return len(self.options)

    @property
    def button_texts(self) -> List[str]:
        """Get button text for Telegram inline keyboard."""
        return [f"{opt.number}. {opt.text}" for opt in self.options]


class MenuDetector:
    """Detects and parses menus from Claude CLI output."""

    # Regex patterns for different menu types
    MENU_RE = re.compile(r"^\s*[│\|]?\s*(\d+)\)\s*(.+?)$", re.MULTILINE)
    NUMBERED_OPTION_RE = re.compile(r"^\s*[│\|]?\s*❯?\s*(\d+)\.\s*(.+?)$", re.MULTILINE)
    YES_NO_RE = re.compile(r"\b(y/n|yes/no)\b", re.IGNORECASE)

    # Interactive prompt keywords (from config)
    INTERACTIVE_KEYWORDS = [
        "Would you like to proceed?",
        "Ready to code?",
        "Here is Claude's plan:",
        "❯ 1.",
        "Choose an option:",
        "What would you like to do?",
        "Select:",
    ]

    # Plan approval specific patterns
    PLAN_APPROVAL_KEYWORDS = [
        "here is claude's plan",
        "plan to",
        "ready to code",
        "would you like to proceed",
        "shall i proceed",
        "exit_plan_mode",
        "confirm the plan",
        "approve",
    ]

    ANSI_ESCAPE_RE = re.compile(r"\x1B\[[0-?]*[ -/]*[@-~]")

    def __init__(self):
        """Initialize menu detector."""
        self.last_output_buffer: List[str] = []
        self.current_menu: Optional[DetectedMenu] = None
        self.max_buffer_size = 50

    def add_output_line(self, line: str) -> Optional[DetectedMenu]:
        """
        Add a line of output and check for menu detection.

        Args:
            line: New output line from Claude CLI

        Returns:
            DetectedMenu if a menu is detected, None otherwise
        """
        # Strip ANSI escape sequences that can break regex matching
        clean_line = self.ANSI_ESCAPE_RE.sub("", line)

        # Add to buffer
        self.last_output_buffer.append(clean_line.rstrip())
        if len(self.last_output_buffer) > self.max_buffer_size:
            self.last_output_buffer.pop(0)

        # Try to detect menu in recent output
        recent_output = "\n".join(self.last_output_buffer[-10:])  # Last 10 lines
        return self._detect_menu(recent_output)

    def _detect_menu(self, output: str) -> Optional[DetectedMenu]:
        """
        Detect menu type and extract options from output.

        Args:
            output: Raw output text to analyze

        Returns:
            DetectedMenu if detected, None otherwise
        """
        # Check for numbered options (❯ 1., 2., 3. format)
        numbered_matches = list(self.NUMBERED_OPTION_RE.finditer(output))
        if numbered_matches:
            return self._parse_numbered_options(output, numbered_matches)

        # Check for classic menu format (1), 2), 3) format)
        menu_matches = list(self.MENU_RE.finditer(output))
        if menu_matches and len(menu_matches) > 1:
            return self._parse_classic_menu(output, menu_matches)

        # Check for interactive prompts - require multiple keywords or a very specific phrase
        interactive_trigger_count = sum(
            1 for kw in self.INTERACTIVE_KEYWORDS if kw.lower() in output.lower()
        )
        is_plan_context = self._is_plan_approval_context(output)

        if (
            interactive_trigger_count >= 1
            or "would you like to proceed?" in output.lower()
            or is_plan_context
        ):
            # Further check: avoid triggering on simple code blocks that contain numbers
            if not (output.strip().startswith("`") and output.strip().endswith("`")):
                return self._parse_interactive_prompt(output, is_plan_context)

        # Check for yes/no prompts
        if self.YES_NO_RE.search(output):
            return self._parse_yes_no_prompt(output)

        return None

    def _parse_numbered_options(
        self, output: str, matches: List[re.Match]
    ) -> DetectedMenu:
        """Parse numbered options format (❯ 1. Option text)."""
        options = []
        for match in matches:
            number = int(match.group(1))
            text = match.group(2).strip()
            full_line = match.group(0).strip()
            options.append(MenuOption(number, text, full_line))

        # Determine if this is a plan approval menu
        menu_type = (
            MenuType.PLAN_APPROVAL
            if self._is_plan_approval_context(output)
            else MenuType.NUMBERED_OPTIONS
        )

        return DetectedMenu(
            menu_type=menu_type,
            options=options,
            context_lines=self.last_output_buffer[-5:],  # Last 5 lines for context
            raw_output=output,
        )

    def _parse_classic_menu(self, output: str, matches: List[re.Match]) -> DetectedMenu:
        """Parse classic menu format (1) Option text)."""
        options = []
        for match in matches:
            number = int(match.group(1))
            text = match.group(2).strip()
            full_line = match.group(0).strip()
            options.append(MenuOption(number, text, full_line))

        return DetectedMenu(
            menu_type=MenuType.NUMBERED_OPTIONS,
            options=options,
            context_lines=self.last_output_buffer[-5:],
            raw_output=output,
        )

    def _parse_interactive_prompt(
        self, output: str, is_plan_context: bool = False
    ) -> DetectedMenu:
        """Parse interactive prompt (usually plan approval)."""
        # Determine menu type and options based on context
        if is_plan_context:
            menu_type = MenuType.PLAN_APPROVAL
            # Look for common plan approval options
            default_options = [
                MenuOption(
                    1, "Yes, proceed with the plan", "1. Yes, proceed with the plan"
                ),
                MenuOption(2, "Modify the plan", "2. Modify the plan"),
                MenuOption(3, "No, cancel", "3. No, cancel"),
            ]
        else:
            menu_type = MenuType.INTERACTIVE_PROMPT
            default_options = [
                MenuOption(
                    1, "Yes, and auto-accept edits", "1. Yes, and auto-accept edits"
                ),
                MenuOption(
                    2,
                    "Yes, and manually approve edits",
                    "2. Yes, and manually approve edits",
                ),
                MenuOption(3, "No, keep planning", "3. No, keep planning"),
            ]

        return DetectedMenu(
            menu_type=menu_type,
            options=default_options,
            context_lines=self.last_output_buffer[-3:],
            raw_output=output,
        )

    def _parse_yes_no_prompt(self, output: str) -> DetectedMenu:
        """Parse yes/no prompt."""
        options = [MenuOption(1, "Yes", "1. Yes"), MenuOption(2, "No", "2. No")]

        return DetectedMenu(
            menu_type=MenuType.YES_NO_PROMPT,
            options=options,
            context_lines=self.last_output_buffer[-2:],
            raw_output=output,
        )

    def _contains_interactive_keywords(self, output: str) -> bool:
        """Check if output contains interactive prompt keywords."""
        output_lower = output.lower()
        return any(
            keyword.lower() in output_lower for keyword in self.INTERACTIVE_KEYWORDS
        )

    def clear_current_menu(self) -> None:
        """Clear the currently detected menu."""
        self.current_menu = None

    def set_current_menu(self, menu: DetectedMenu) -> None:
        """Set the current menu being processed."""
        self.current_menu = menu
        logger.info(
            f"Detected menu with {menu.option_count} options: {menu.menu_type.value}"
        )

    def _is_plan_approval_context(self, output: str) -> bool:
        """Check if the output indicates a plan approval context."""
        output_lower = output.lower()

        # Check recent buffer for plan approval keywords
        recent_buffer = " ".join(
            self.last_output_buffer[-15:]
        ).lower()  # Check more context

        # Strong indicators for plan approval
        for keyword in self.PLAN_APPROVAL_KEYWORDS:
            if keyword in output_lower or keyword in recent_buffer:
                logger.debug(f"Plan approval detected via keyword: {keyword}")
                return True

        # Additional patterns that indicate plan presentation
        plan_patterns = [
            r"here\s+is\s+.*plan",
            r"plan\s+to\s+\w+",
            r"implementation\s+steps",
            r"approach\s+will\s+be",
            r"strategy.*:",
            r"phase\s+\d+",
            r"step\s+\d+",
        ]

        for pattern in plan_patterns:
            if re.search(pattern, recent_buffer, re.IGNORECASE):
                logger.debug(f"Plan approval detected via pattern: {pattern}")
                return True

        return False


class MenuResponseHandler:
    """Handles responses to detected menus from both terminal and Telegram."""

    def __init__(self):
        """Initialize response handler."""
        self.pending_response: Optional[asyncio.Future] = None
        # Store early responses so they are not lost if they arrive
        # before the waiter is set up.  A simple deque is sufficient
        # and avoids requiring an event-loop context for queue puts/gets.
        self.response_queue: deque[str] = deque()

    async def wait_for_choice(self, timeout: float = 300.0) -> Optional[str]:
        """
        Wait for user choice from either terminal or Telegram.

        Args:
            timeout: Maximum time to wait for response in seconds

        Returns:
            User's choice as string, or None if timeout
        """
        # First consume any choice that may have arrived early.
        if self.response_queue:
            early_choice = self.response_queue.popleft()
            logger.debug(f"Using queued menu choice: {early_choice}")
            return early_choice

        if self.pending_response is not None:
            logger.warning("Already waiting for a response")
            return None

        self.pending_response = asyncio.Future()

        try:
            response = await asyncio.wait_for(self.pending_response, timeout=timeout)
            return response
        except asyncio.TimeoutError:
            logger.info("Menu response timed out")
            return None
        finally:
            self.pending_response = None

    def submit_choice(self, choice: str, source: str = "unknown") -> bool:
        """
        Submit a user choice.

        Args:
            choice: User's choice (e.g., "1", "2", "yes", "no")
            source: Source of the choice ("terminal" or "telegram")

        Returns:
            True if choice was accepted, False if no one was waiting
        """
        # If a waiter is present fulfil it immediately; otherwise queue the choice
        if self.pending_response is None or self.pending_response.done():
            self.response_queue.append(choice)
            logger.info(f"Queued choice '{choice}' from {source} (no waiter yet)")
            return True

        self.pending_response.set_result(choice)
        logger.info(f"Delivered choice '{choice}' from {source}")
        return True

    def cancel_waiting(self) -> None:
        """Cancel any pending response wait."""
        if self.pending_response is not None and not self.pending_response.done():
            self.pending_response.cancel()
            self.pending_response = None
