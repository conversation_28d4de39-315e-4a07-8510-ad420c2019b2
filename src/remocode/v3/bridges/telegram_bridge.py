"""
Telegram bridge for RemoCode v3.

Handles sending menu prompts to Tel<PERSON>ram and receiving user responses.
Supports multiple debug modes controlled by CLI flags.
"""

import asyncio
import logging
import time
from typing import Any, Dict, List, Optional

import httpx

from ..config import debug_config, telegram_config
from ..core.menu_detector import <PERSON><PERSON><PERSON>rom<PERSON>
from ..core.telegram_listener import TelegramListener
from ..utils.session_utils import SessionInfo, SessionUtils
from .claude_writer import Claude<PERSON>rite<PERSON>
from .claude_writer_v2 import ClaudeWriterV2


class TelegramBridge:
    """Bridge for Telegram integration with debug modes."""

    def __init__(self, args):
        self.args = args
        self.logger = logging.getLogger(__name__)

        # Telegram configuration
        self.token = telegram_config.get_token()
        self.chat_id = telegram_config.get_chat_id()
        self.is_configured = telegram_config.is_configured()

        if not self.is_configured:
            # Bridge disabled – ensure attributes exist so .stop() is safe
            self.logger.warning("Telegram not configured - bridge disabled")
            self.listener = None  # type: ignore[attr-defined]
            return

        # Claude writers for each session
        self.claude_writers: Dict[str, ClaudeWriter] = {}
        self.session_mapping: Dict[str, str] = {}  # session_short -> full_session_id

        # HTTP client
        self.http_client: Optional[httpx.AsyncClient] = None

        # Telegram listener for responses
        self.listener: Optional[TelegramListener] = None

        # Claude launcher (set by SessionWatcher if available)
        self.claude_launcher = None

        # Rate limiting
        self.last_request_time = 0.0
        self.request_count = 0
        self.minute_start = time.time()

        self.logger.info("Telegram bridge initialized")

    def set_claude_launcher(self, launcher):
        """Set the Claude launcher for spawned processes."""
        self.claude_launcher = launcher

    async def send_menu_prompt(
        self,
        session_info: SessionInfo,
        menu_prompt: MenuPrompt,
        recent_events: List[Dict[str, Any]],
    ):
        """Send a menu prompt to Telegram."""
        if not self.is_configured:
            return

        try:
            # Register session mapping for response handling
            session_short = SessionUtils.get_session_short_id(session_info.session_id)
            self.session_mapping[session_short] = session_info.session_id

            # Ensure we have a Claude writer for this session
            await self._ensure_claude_writer(session_info)

            # Format message based on debug mode
            message_text = self._format_menu_message(
                session_info, menu_prompt, recent_events
            )

            # Create inline keyboard
            keyboard = self._create_inline_keyboard(session_info, menu_prompt)

            # Send message
            await self._send_message(
                text=message_text,
                reply_markup=keyboard,
                disable_notification=debug_config.DEFAULT_DISABLE_NOTIFICATION,
            )

            self.logger.info(
                f"Sent menu prompt to Telegram for session {session_info.session_id[:8]}"
            )

        except Exception as e:
            self.logger.error(f"Failed to send menu prompt: {e}")

    async def send_text_prompt(
        self,
        session_info: SessionInfo,
        prompt_text: str,
        recent_events: List[Dict[str, Any]],
    ):
        """Send a text input prompt to Telegram."""
        if not self.is_configured:
            return

        try:
            # Format message
            if self.args.minimal_mode:
                message_text = f"💬 {prompt_text}"
            else:
                session_short = SessionUtils.get_session_short_id(
                    session_info.session_id
                )
                message_text = f"💬 ({session_short}) {prompt_text}"

                if self.args.show_session_info:
                    project_name = SessionUtils.get_project_name(
                        session_info.project_path
                    )
                    message_text += f"\n📍 Project: {project_name}"

            # Send message
            await self._send_message(
                text=message_text,
                disable_notification=debug_config.DEFAULT_DISABLE_NOTIFICATION,
            )

            self.logger.info(
                f"Sent text prompt to Telegram for session {session_info.session_id[:8]}"
            )

        except Exception as e:
            self.logger.error(f"Failed to send text prompt: {e}")

    async def send_plan_approval_notification(
        self,
        session_info: SessionInfo,
        message: str,
        recent_events: List[Dict[str, Any]],
    ):
        """Send a plan approval notification to Telegram."""
        if not self.is_configured:
            return

        try:
            # Format message
            if self.args.minimal_mode:
                message_text = f"📋 {message}"
            else:
                session_short = SessionUtils.get_session_short_id(
                    session_info.session_id
                )
                message_text = f"📋 ({session_short}) {message}"

                if self.args.show_session_info:
                    project_name = SessionUtils.get_project_name(
                        session_info.project_path
                    )
                    message_text += f"\n📍 Project: {project_name}"
                    message_text += f"\n🆔 Session: {session_short}"

            # Add helpful instructions
            message_text += "\n\n💡 Plan approval menus appear in the terminal but not in transcript files."
            message_text += (
                "\n🔄 v3 can detect plan context but cannot intercept the approval menu."
            )
            message_text += "\n⚡ Consider using v2 transparent mode for full plan approval integration."

            # Send message
            await self._send_message(
                text=message_text,
                disable_notification=debug_config.DEFAULT_DISABLE_NOTIFICATION,
            )

            self.logger.info(
                f"Sent plan approval notification to Telegram for session {session_info.session_id[:8]}"
            )

        except Exception as e:
            self.logger.error(f"Failed to send plan approval notification: {e}")

    async def send_session_start_notification(self, session_info: SessionInfo):
        """Send notification when a new session starts."""
        if not self.is_configured:
            return

        try:
            if self.args.minimal_mode:
                message_text = debug_config.SESSION_START_MINIMAL
            else:
                message_text = debug_config.SESSION_START_DEBUG.format(
                    project_path=SessionUtils.get_project_name(
                        session_info.project_path
                    ),
                    session_id=SessionUtils.get_session_short_id(
                        session_info.session_id
                    ),
                    cwd=session_info.cwd or "Unknown",
                )

            await self._send_message(
                text=message_text,
                disable_notification=True,  # Always silent for session starts
            )

            self.logger.info(
                f"Sent session start notification for {session_info.session_id[:8]}"
            )

        except Exception as e:
            self.logger.error(f"Failed to send session start notification: {e}")

    def _format_menu_message(
        self,
        session_info: SessionInfo,
        menu_prompt: MenuPrompt,
        recent_events: List[Dict[str, Any]],
    ) -> str:
        """Format menu message based on debug mode."""
        # Choose template based on debug flags
        if self.args.minimal_mode:
            template = debug_config.MINIMAL_TEMPLATE
        elif self.args.show_full_json:
            template = debug_config.FULL_JSON_TEMPLATE
        elif self.args.show_session_info:
            template = debug_config.DEBUG_TEMPLATE
        else:
            template = debug_config.STANDARD_TEMPLATE

        # Prepare template variables
        session_short = SessionUtils.get_session_short_id(
            session_info.session_id, debug_config.SESSION_SHORT_LENGTH
        )

        project_name = SessionUtils.get_project_name(session_info.project_path)

        # Format message
        message_vars = {
            "prompt": menu_prompt.prompt,
            "session_short": session_short,
            "project_path": project_name,
        }

        # Add transcript snippet for full JSON mode
        if self.args.show_full_json and recent_events:
            from ..utils.json_parser import JSONLParser

            parser = JSONLParser()
            snippet = parser.find_recent_content(
                recent_events, debug_config.MAX_SNIPPET_CHARS
            )
            message_vars["transcript_snippet"] = snippet[
                : debug_config.MAX_SNIPPET_CHARS
            ]

        return template.format(**message_vars)

    def _create_inline_keyboard(
        self, session_info: SessionInfo, menu_prompt: MenuPrompt
    ) -> Dict[str, Any]:
        """Create Telegram inline keyboard for menu options."""
        keyboard_rows = []

        # Group options into rows (max 3 buttons per row)
        current_row = []
        for option in menu_prompt.options:
            # Create callback data (session_id:option_key)
            callback_data = f"{session_info.session_id[:8]}:{option.key}"

            # Ensure callback data fits Telegram limits
            if len(callback_data) > telegram_config.MAX_CALLBACK_DATA_LENGTH:
                callback_data = callback_data[
                    : telegram_config.MAX_CALLBACK_DATA_LENGTH
                ]

            button = {
                "text": f"{option.key} - {option.label}",
                "callback_data": callback_data,
            }

            current_row.append(button)

            # Start new row after 3 buttons or if this is a yes/no choice
            if len(current_row) >= 3 or (
                len(menu_prompt.options) == 2 and len(current_row) == 1
            ):
                keyboard_rows.append(current_row)
                current_row = []

        # Add any remaining buttons
        if current_row:
            keyboard_rows.append(current_row)

        return {"inline_keyboard": keyboard_rows}

    async def _send_message(
        self,
        text: str,
        reply_markup: Optional[Dict] = None,
        disable_notification: bool = True,
    ):
        """Send message to Telegram with rate limiting."""
        await self._rate_limit()

        # Prepare request data
        data = {
            "chat_id": self.chat_id,
            "text": text,
            "parse_mode": debug_config.DEFAULT_PARSE_MODE,
            "disable_notification": disable_notification,
        }

        if reply_markup:
            data["reply_markup"] = reply_markup

        # Send request
        await self._telegram_request("sendMessage", data)

    async def _telegram_request(
        self, method: str, data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Make a Telegram API request."""
        # Execute request with retries
        for attempt in range(telegram_config.MAX_RETRIES):
            try:
                response = await self.http_client.post(method, json=data)
                response.raise_for_status()

                result = response.json()
                if not result.get("ok", False):
                    raise Exception(f"Telegram API error: {result}")

                return result

            except Exception as e:
                if attempt == telegram_config.MAX_RETRIES - 1:
                    raise

                self.logger.warning(
                    f"Telegram request failed (attempt {attempt + 1}): {e}"
                )
                await asyncio.sleep(
                    telegram_config.RETRY_DELAY
                    * (telegram_config.BACKOFF_MULTIPLIER**attempt)
                )

    async def _rate_limit(self):
        """Implement rate limiting for Telegram API."""
        current_time = time.time()

        # Reset minute counter
        if current_time - self.minute_start >= 60:
            self.request_count = 0
            self.minute_start = current_time

        # Check per-minute limit
        if self.request_count >= telegram_config.MAX_REQUESTS_PER_MINUTE:
            sleep_time = 60 - (current_time - self.minute_start)
            if sleep_time > 0:
                await asyncio.sleep(sleep_time)
                self.request_count = 0
                self.minute_start = time.time()

        # Check per-second limit
        time_since_last = current_time - self.last_request_time
        min_interval = 1.0 / telegram_config.MAX_REQUESTS_PER_SECOND

        if time_since_last < min_interval:
            await asyncio.sleep(min_interval - time_since_last)

        self.last_request_time = time.time()
        self.request_count += 1

    async def start(self):
        """Start the Telegram bridge and listener."""
        if not self.is_configured:
            return

        # Initialize HTTP client
        self.http_client = httpx.AsyncClient(
            base_url=f"{telegram_config.API_BASE_URL}{self.token}/", timeout=30.0
        )

        # Start Telegram listener for responses
        self.listener = TelegramListener(self.handle_telegram_response)
        await self.listener.start()

        self.logger.info("Telegram bridge started")

    async def handle_telegram_response(self, session_short: str, response: str):
        """Handle response from Telegram listener."""
        try:
            # Find full session ID
            full_session_id = self.session_mapping.get(session_short)
            if not full_session_id:
                self.logger.warning(f"No session mapping found for {session_short}")
                return

            # Get Claude writer for session
            claude_writer = self.claude_writers.get(full_session_id)
            if not claude_writer:
                self.logger.warning(
                    f"No Claude writer found for session {full_session_id[:8]}"
                )
                return

            # Send response to Claude
            success = await claude_writer.write_response(response)
            if success:
                self.logger.info(
                    f"Successfully sent response '{response}' to Claude session {full_session_id[:8]}"
                )
            else:
                self.logger.error(
                    f"Failed to send response to Claude session {full_session_id[:8]}"
                )

        except Exception as e:
            self.logger.error(f"Error handling Telegram response: {e}")

    async def _ensure_claude_writer(self, session_info: SessionInfo):
        """Ensure we have a Claude writer for the session."""
        session_id = session_info.session_id

        if session_id not in self.claude_writers:
            # Choose writer type based on whether we have a launcher
            if self.claude_launcher:
                # Use improved writer with subprocess pipes
                claude_writer = ClaudeWriterV2(session_info, self.claude_launcher)
            else:
                # Fallback to original writer (for attached processes)
                claude_writer = ClaudeWriter(session_info)

            # Try to connect
            if await claude_writer.connect():
                self.claude_writers[session_id] = claude_writer
                self.logger.info(f"Created Claude writer for session {session_id[:8]}")
            else:
                self.logger.warning(
                    f"Failed to connect Claude writer for session {session_id[:8]}"
                )

    async def stop(self):
        """Stop the Telegram bridge."""
        # Stop Telegram listener
        if self.listener:
            await self.listener.stop()

        # Stop all Claude writers
        for writer in self.claude_writers.values():
            await writer.stop()

        # Close HTTP client
        if self.http_client:
            await self.http_client.aclose()

        self.claude_writers.clear()
        self.session_mapping.clear()
        self.logger.info("Telegram bridge stopped")

    def get_claude_writer(self, session_id: str) -> Optional[ClaudeWriter]:
        """Get Claude writer for session."""
        return self.claude_writers.get(session_id)
