"""
Improved Claude writer using subprocess pipes for cross-platform reliability.

This version works with processes launched by <PERSON><PERSON><PERSON><PERSON><PERSON> and uses
direct subprocess communication instead of fragile procfs access.
"""

import asyncio
import logging
import time
from typing import Any, Dict

from ..core.claude_launcher import <PERSON><PERSON><PERSON><PERSON><PERSON>
from ..utils.session_utils import SessionInfo


class ClaudeWriterV2:
    """Cross-platform Claude writer using subprocess pipes."""

    def __init__(self, session_info: SessionInfo, launcher: <PERSON><PERSON>aunch<PERSON>):
        """
        Initialize writer with session info and launcher.

        Args:
            session_info: Information about the <PERSON> session
            launcher: <PERSON><PERSON><PERSON><PERSON><PERSON> instance managing the process
        """
        self.session_info = session_info
        self.launcher = launcher
        self.logger = logging.getLogger(__name__)

        # Rate limiting
        self.last_write_time = 0.0
        self.min_write_interval = 0.1  # 100ms minimum between writes

        # Connection state
        self.is_connected = False

    async def connect(self) -> bool:
        """Connect to the Claude Code process."""
        try:
            # Check if the process is running in the launcher
            if not self.launcher.is_session_running(self.session_info.session_id):
                self.logger.warning(
                    f"No running process found for session {self.session_info.session_id[:8]}"
                )
                return False

            self.is_connected = True
            self.logger.info(
                f"Connected to <PERSON> process for session {self.session_info.session_id[:8]}"
            )
            return True

        except Exception as e:
            self.logger.error(f"Error connecting to Claude process: {e}")
            return False

    async def write_response(self, response: str) -> bool:
        """Write a response to <PERSON>'s stdin."""
        if not self.is_connected:
            if not await self.connect():
                return False

        try:
            # Rate limiting
            await self._rate_limit()

            # Use the launcher to send input
            success = await self.launcher.send_input(
                self.session_info.session_id, response
            )

            if success:
                self.logger.info(
                    f"Wrote response to Claude session {self.session_info.session_id[:8]}: {response.strip()}"
                )
            else:
                self.logger.error(
                    f"Failed to write response to Claude session {self.session_info.session_id[:8]}"
                )
                self.is_connected = False  # Mark as disconnected on failure

            return success

        except Exception as e:
            self.logger.error(f"Error writing response: {e}")
            self.is_connected = False
            return False

    async def _rate_limit(self):
        """Rate limit writes to prevent overwhelming Claude."""
        current_time = time.time()
        time_since_last = current_time - self.last_write_time

        if time_since_last < self.min_write_interval:
            sleep_time = self.min_write_interval - time_since_last
            await asyncio.sleep(sleep_time)

        self.last_write_time = time.time()

    async def stop(self):
        """Stop the writer and cleanup resources."""
        self.is_connected = False
        self.logger.debug(
            f"Claude writer stopped for session {self.session_info.session_id[:8]}"
        )

    def is_process_running(self) -> bool:
        """Check if the Claude process is still running."""
        return self.launcher.is_session_running(self.session_info.session_id)

    def get_status(self) -> Dict[str, Any]:
        """Get status information about the writer."""
        return {
            "session_id": self.session_info.session_id,
            "is_connected": self.is_connected,
            "process_running": self.is_process_running(),
            "session_path": str(self.session_info.transcript_path),
        }
