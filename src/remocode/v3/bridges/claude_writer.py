"""
Claude writer for sending responses to Claude <PERSON> processes.

Handles writing user responses to <PERSON>'s stdin using
various methods depending on how the process was started.
"""

import asyncio
import logging
import os
import time
from pathlib import Path
from typing import Any, Dict, Optional

import psutil

from ..utils.session_utils import SessionInfo


class ClaudeWriter:
    """Writes responses to Claude Code processes."""

    def __init__(self, session_info: SessionInfo):
        self.session_info = session_info
        self.logger = logging.getLogger(__name__)

        # Process information
        self.process_id: Optional[int] = None
        self.stdin_path: Optional[Path] = None
        self.stdin_fd: Optional[int] = None

        # Rate limiting
        self.last_write_time = 0.0
        self.min_write_interval = 0.1  # 100ms minimum between writes

        # Connection state
        self.is_connected = False

    async def connect(self) -> bool:
        """Connect to the Claude Code process for this session."""
        try:
            # Try to find the Claude process for this session
            process_id = await self._find_claude_process()
            if not process_id:
                self.logger.warning(
                    f"Could not find <PERSON> process for session {self.session_info.session_id[:8]}"
                )
                return False

            self.process_id = process_id

            # Try to open stdin for writing
            if await self._connect_to_stdin():
                self.is_connected = True
                self.logger.info(
                    f"Connected to Claude process {process_id} for session {self.session_info.session_id[:8]}"
                )
                return True
            else:
                self.logger.error(
                    f"Failed to connect to stdin for process {process_id}"
                )
                return False

        except Exception as e:
            self.logger.error(f"Error connecting to Claude process: {e}")
            return False

    async def write_response(self, response: str) -> bool:
        """Write a response to Claude's stdin."""
        if not self.is_connected:
            if not await self.connect():
                return False

        try:
            # Rate limiting
            await self._rate_limit()

            # Ensure response ends with newline
            if not response.endswith("\n"):
                response += "\n"

            # Write to stdin
            success = await self._write_to_stdin(response)

            if success:
                self.logger.info(
                    f"Wrote response to Claude session {self.session_info.session_id[:8]}: {response.strip()}"
                )
            else:
                self.logger.error(
                    f"Failed to write response to Claude session {self.session_info.session_id[:8]}"
                )

            return success

        except Exception as e:
            self.logger.error(f"Error writing response: {e}")
            return False

    async def _find_claude_process(self) -> Optional[int]:
        """Find the Claude Code process for this session."""
        try:
            # Look for processes with "claude" in the command line
            for proc in psutil.process_iter(["pid", "name", "cmdline", "cwd"]):
                try:
                    proc_info = proc.info
                    cmdline = proc_info.get("cmdline", [])

                    if not cmdline:
                        continue

                    # Check if this looks like a Claude Code process
                    cmdline_str = " ".join(cmdline).lower()
                    if "claude" in cmdline_str and (
                        "code" in cmdline_str or "@anthropic-ai" in cmdline_str
                    ):
                        # Check if working directory matches our session
                        proc_cwd = proc_info.get("cwd")
                        if proc_cwd and proc_cwd == self.session_info.cwd:
                            return proc_info["pid"]

                        # Also check if running in project directory
                        if proc_cwd and self.session_info.project_path in proc_cwd:
                            return proc_info["pid"]

                except (
                    psutil.NoSuchProcess,
                    psutil.AccessDenied,
                    psutil.ZombieProcess,
                ):
                    continue

        except Exception as e:
            self.logger.error(f"Error finding Claude process: {e}")

        return None

    async def _connect_to_stdin(self) -> bool:
        """Connect to the process's stdin."""
        if not self.process_id:
            return False

        try:
            # Try different methods to access stdin

            # Method 1: /proc/<pid>/fd/0 (Linux)
            if os.path.exists(f"/proc/{self.process_id}/fd/0"):
                self.stdin_path = Path(f"/proc/{self.process_id}/fd/0")
                return True

            # Method 2: /dev/fd/0 approach (macOS)
            # This is more complex and may require different approach

            # Method 3: Try to find pipe or tty
            proc_fd_dir = Path(f"/proc/{self.process_id}/fd")
            if proc_fd_dir.exists():
                # Look for stdin-like file descriptors
                for fd_link in proc_fd_dir.iterdir():
                    if fd_link.name == "0":  # stdin is fd 0
                        try:
                            target = fd_link.readlink()
                            if "pipe" in str(target) or "pts" in str(target):
                                self.stdin_path = fd_link
                                return True
                        except Exception:
                            continue

            self.logger.warning(
                f"Could not find accessible stdin for process {self.process_id}"
            )
            return False

        except Exception as e:
            self.logger.error(f"Error connecting to stdin: {e}")
            return False

    async def _write_to_stdin(self, data: str) -> bool:
        """Write data to the process stdin."""
        if not self.stdin_path:
            return False

        try:
            # Write to the stdin file/pipe
            with open(self.stdin_path, "w") as f:
                f.write(data)
                f.flush()
            return True

        except PermissionError:
            self.logger.error(f"Permission denied writing to {self.stdin_path}")
            return False
        except BrokenPipeError:
            self.logger.error(f"Broken pipe writing to {self.stdin_path}")
            self.is_connected = False
            return False
        except Exception as e:
            self.logger.error(f"Error writing to stdin: {e}")
            return False

    async def _rate_limit(self):
        """Rate limit writes to prevent overwhelming Claude."""
        current_time = time.time()
        time_since_last = current_time - self.last_write_time

        if time_since_last < self.min_write_interval:
            sleep_time = self.min_write_interval - time_since_last
            await asyncio.sleep(sleep_time)

        self.last_write_time = time.time()

    async def stop(self):
        """Stop the writer and cleanup resources."""
        self.is_connected = False
        self.stdin_path = None
        self.stdin_fd = None
        self.process_id = None

        self.logger.debug(
            f"Claude writer stopped for session {self.session_info.session_id[:8]}"
        )

    def is_process_running(self) -> bool:
        """Check if the Claude process is still running."""
        if not self.process_id:
            return False

        try:
            proc = psutil.Process(self.process_id)
            return proc.is_running()
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            return False

    def get_status(self) -> Dict[str, Any]:
        """Get status information about the writer."""
        return {
            "session_id": self.session_info.session_id,
            "process_id": self.process_id,
            "is_connected": self.is_connected,
            "stdin_path": str(self.stdin_path) if self.stdin_path else None,
            "process_running": self.is_process_running(),
        }
