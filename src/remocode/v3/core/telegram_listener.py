"""
Telegram listener for receiving callback query responses.

Polls Telegram API for callback queries from inline keyboard interactions
and routes responses to appropriate Claude sessions.
"""

import asyncio
import logging
from typing import Any, Callable, Dict, Optional

import httpx

from ..config import telegram_config


class TelegramListener:
    """Listens for Telegram callback queries and routes responses."""

    def __init__(self, response_handler: Callable[[str, str], None]):
        """
        Initialize listener with response handler.

        Args:
            response_handler: Function called with (session_id, response) when user responds
        """
        self.response_handler = response_handler
        self.logger = logging.getLogger(__name__)

        # Telegram configuration
        self.token = telegram_config.get_token()
        self.chat_id = telegram_config.get_chat_id()
        self.is_configured = telegram_config.is_configured()

        if not self.is_configured:
            self.logger.warning("Telegram not configured - listener disabled")
            return

        # Polling state
        self.running = False
        self.last_update_id = 0
        self.polling_task: Optional[asyncio.Task] = None

        # HTTP client
        self.http_client: Optional[httpx.AsyncClient] = None

        self.logger.info("Telegram listener initialized")

    async def start(self):
        """Start listening for callback queries."""
        if not self.is_configured:
            return

        self.logger.info("Starting Telegram listener...")

        # Initialize HTTP client
        self.http_client = httpx.AsyncClient(
            base_url=f"{telegram_config.API_BASE_URL}{self.token}/", timeout=30.0
        )

        self.running = True
        self.polling_task = asyncio.create_task(self.polling_loop())

    async def stop(self):
        """Stop listening and cleanup."""
        self.logger.info("Stopping Telegram listener...")
        self.running = False

        if self.polling_task:
            self.polling_task.cancel()
            try:
                await self.polling_task
            except asyncio.CancelledError:
                pass

        if self.http_client:
            await self.http_client.aclose()

        self.logger.info("Telegram listener stopped")

    async def polling_loop(self):
        """Main polling loop for getUpdates."""
        while self.running:
            try:
                await self.poll_for_updates()
                await asyncio.sleep(1.0)  # Poll every second
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error in polling loop: {e}")
                await asyncio.sleep(5.0)  # Back off on errors

    async def poll_for_updates(self):
        """Poll Telegram for new updates."""
        try:
            # Get updates from Telegram
            params = {
                "offset": self.last_update_id + 1,
                "timeout": 10,
                "allowed_updates": ["callback_query"],
            }

            response = await self.http_client.get("getUpdates", params=params)
            response.raise_for_status()

            result = response.json()
            if not result.get("ok"):
                self.logger.error(f"Telegram API error: {result}")
                return

            updates = result.get("result", [])

            # Process each update
            for update in updates:
                self.last_update_id = max(self.last_update_id, update["update_id"])

                if "callback_query" in update:
                    await self.handle_callback_query(update["callback_query"])

        except httpx.TimeoutException:
            # Timeout is expected with long polling
            pass
        except Exception as e:
            self.logger.error(f"Error polling for updates: {e}")

    async def handle_callback_query(self, callback_query: Dict[str, Any]):
        """Handle a callback query from inline keyboard."""
        try:
            query_id = callback_query["id"]
            callback_data = callback_query["data"]
            user = callback_query.get("from", {})
            message = callback_query.get("message", {})

            self.logger.info(
                f"Received callback query: {callback_data} from user {user.get('id')}"
            )

            # Parse callback data: session_id:response
            if ":" not in callback_data:
                self.logger.warning(f"Invalid callback data format: {callback_data}")
                await self.answer_callback_query(query_id, "Invalid response format")
                return

            session_short, response = callback_data.split(":", 1)

            # Answer the callback query
            await self.answer_callback_query(query_id, f"Selected: {response}")

            # Update the original message to show selection
            if message:
                await self.update_message_with_selection(message, response)

            # Route response to handler
            await self.route_response(session_short, response)

        except Exception as e:
            self.logger.error(f"Error handling callback query: {e}")
            try:
                await self.answer_callback_query(
                    callback_query["id"], "Error processing response"
                )
            except:
                pass

    async def answer_callback_query(self, query_id: str, text: str = ""):
        """Answer a callback query to remove loading state."""
        try:
            data = {"callback_query_id": query_id, "text": text, "show_alert": False}

            response = await self.http_client.post("answerCallbackQuery", json=data)
            response.raise_for_status()

        except Exception as e:
            self.logger.error(f"Error answering callback query: {e}")

    async def update_message_with_selection(
        self, message: Dict[str, Any], selection: str
    ):
        """Update the original message to show user's selection."""
        try:
            chat_id = message.get("chat", {}).get("id")
            message_id = message.get("message_id")
            original_text = message.get("text", "")

            if not chat_id or not message_id:
                return

            # Add selection to message
            updated_text = f"{original_text}\n\n✅ **Selected:** {selection}"

            data = {
                "chat_id": chat_id,
                "message_id": message_id,
                "text": updated_text,
                "parse_mode": "Markdown",
            }

            response = await self.http_client.post("editMessageText", json=data)
            # Don't raise for status here as message might be too old to edit

        except Exception as e:
            self.logger.debug(
                f"Could not update message (expected for old messages): {e}"
            )

    async def route_response(self, session_short: str, response: str):
        """Route the response to the appropriate session."""
        try:
            # Call the response handler
            if self.response_handler:
                await asyncio.get_event_loop().run_in_executor(
                    None, self.response_handler, session_short, response
                )
            else:
                self.logger.warning(
                    f"No response handler configured for {session_short}:{response}"
                )

        except Exception as e:
            self.logger.error(f"Error routing response: {e}")

    def get_status(self) -> Dict[str, Any]:
        """Get listener status information."""
        return {
            "running": self.running,
            "configured": self.is_configured,
            "last_update_id": self.last_update_id,
            "has_http_client": self.http_client is not None,
        }
