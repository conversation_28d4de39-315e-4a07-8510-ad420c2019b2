"""
Session watcher for monitoring Claude Code sessions.

Watches ~/.claude/projects for new session files and manages
TranscriptTailer instances for active sessions.
"""

import asyncio
import logging
import time
from dataclasses import dataclass
from pathlib import Path
from typing import Dict, Optional, Set

try:
    from watchdog.events import (
        FileCreatedEvent,
        FileModifiedEvent,
        FileSystemEventHandler,
    )
    from watchdog.observers import Observer

    WATCHDOG_AVAILABLE = True
except ImportError:
    WATCHDOG_AVAILABLE = False

    # Create dummy base class if watchdog not available
    class FileSystemEventHandler:
        def on_created(self, event):
            pass

        def on_modified(self, event):
            pass


from ..config import path_config
from ..utils.session_utils import SessionInfo, SessionUtils
from .transcript_tailer import TranscriptTailer


@dataclass
class SessionState:
    """State tracking for a monitored session."""

    info: SessionInfo
    tailer: TranscriptTailer
    last_activity: float


class SessionEventHandler(FileSystemEventHandler):
    """File system event handler for session monitoring."""

    def __init__(self, session_watcher: "SessionWatcher"):
        self.session_watcher = session_watcher
        self.logger = logging.getLogger(__name__)

    def on_created(self, event):
        """Handle file creation events."""
        if event.is_directory:
            return

        file_path = Path(event.src_path)
        if SessionUtils.is_session_file(file_path):
            self.logger.info(f"New session file detected: {file_path.name}")
            # Schedule the async task in the main event loop
            if self.session_watcher.event_loop:
                self.session_watcher.event_loop.call_soon_threadsafe(
                    lambda: asyncio.create_task(
                        self.session_watcher.add_session(file_path)
                    )
                )
            else:
                self.logger.warning(
                    f"Could not schedule session addition for {file_path.name} - no event loop available"
                )

    def on_modified(self, event):
        """Handle file modification events."""
        if event.is_directory:
            return

        file_path = Path(event.src_path)
        if SessionUtils.is_session_file(file_path):
            # Update last activity for existing sessions
            self.session_watcher.update_session_activity(file_path)


class SessionWatcher:
    """Monitors Claude Code sessions and manages transcript tailers."""

    def __init__(self, projects_dir: Path, telegram_bridge, claude_launcher, args):
        self.projects_dir = projects_dir
        self.telegram_bridge = telegram_bridge
        self.claude_launcher = claude_launcher
        self.args = args
        self.logger = logging.getLogger(__name__)

        # Active session tracking
        self.sessions: Dict[str, SessionState] = {}
        self.max_sessions = args.max_sessions

        # File system monitoring
        self.observer: Optional[Observer] = None
        self.event_handler: Optional[SessionEventHandler] = None
        self.event_loop: Optional[asyncio.AbstractEventLoop] = None

        # Polling fallback
        self.polling_task: Optional[asyncio.Task] = None
        self.known_files: Set[Path] = set()

        self.running = False

    async def start(self):
        """Start monitoring for sessions."""
        self.logger.info(f"Starting session watcher on {self.projects_dir}")
        self.running = True

        # Store the current event loop for cross-thread communication
        self.event_loop = asyncio.get_running_loop()

        # Discover existing sessions
        await self.discover_existing_sessions()

        # Start file system monitoring
        if WATCHDOG_AVAILABLE and self.projects_dir.exists():
            await self.start_watchdog_monitoring()
        else:
            await self.start_polling_monitoring()

        # Start cleanup task
        asyncio.create_task(self.cleanup_inactive_sessions())

    async def stop(self):
        """Stop monitoring and cleanup."""
        self.logger.info("Stopping session watcher")
        self.running = False

        # Stop file system monitoring
        if self.observer:
            self.observer.stop()
            self.observer.join()

        if self.polling_task:
            self.polling_task.cancel()

        # Stop all tailers
        for session_state in self.sessions.values():
            await session_state.tailer.stop()

        self.sessions.clear()

    async def discover_existing_sessions(self):
        """Discover and start monitoring existing sessions."""
        self.logger.info("Discovering existing Claude sessions...")

        existing_sessions = SessionUtils.find_active_sessions(self.projects_dir)
        self.logger.info(f"Found {len(existing_sessions)} existing sessions")

        for session_info in existing_sessions:
            if SessionUtils.is_session_active(session_info):
                await self.add_session(session_info.transcript_path)

    async def start_watchdog_monitoring(self):
        """Start watchdog-based file system monitoring."""
        try:
            self.event_handler = SessionEventHandler(self)
            self.observer = Observer()

            # Watch the projects directory and all subdirectories
            self.observer.schedule(
                self.event_handler, str(self.projects_dir), recursive=True
            )

            self.observer.start()
            self.logger.info("Started watchdog file system monitoring")

        except Exception as e:
            self.logger.warning(f"Failed to start watchdog monitoring: {e}")
            self.logger.info("Falling back to polling mode")
            await self.start_polling_monitoring()

    async def start_polling_monitoring(self):
        """Start polling-based file monitoring."""
        self.logger.info("Starting polling-based session monitoring")
        self.polling_task = asyncio.create_task(self.polling_loop())

    async def polling_loop(self):
        """Polling loop for file system changes."""
        while self.running:
            try:
                await self.poll_for_new_sessions()
                await asyncio.sleep(path_config.WATCHER_POLL_INTERVAL)
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error in polling loop: {e}")
                await asyncio.sleep(5)  # Back off on errors

    async def poll_for_new_sessions(self):
        """Poll for new session files."""
        if not self.projects_dir.exists():
            return

        try:
            current_files = set()

            # Scan all project directories for session files
            for project_dir in self.projects_dir.iterdir():
                if project_dir.is_dir():
                    for session_file in project_dir.glob(
                        path_config.SESSION_FILE_PATTERN
                    ):
                        if SessionUtils.is_session_file(session_file):
                            current_files.add(session_file)

            # Find new files
            new_files = current_files - self.known_files
            for new_file in new_files:
                self.logger.info(f"New session file detected: {new_file.name}")
                await self.add_session(new_file)

            self.known_files = current_files

        except Exception as e:
            self.logger.error(f"Error polling for sessions: {e}")

    async def add_session(self, transcript_path: Path):
        """Add a new session for monitoring."""
        session_info = SessionUtils.create_session_info(transcript_path)
        if not session_info:
            return

        session_id = session_info.session_id

        # Check if already monitoring
        if session_id in self.sessions:
            return

        # Check session limit
        if len(self.sessions) >= self.max_sessions:
            self.logger.warning(
                f"Maximum sessions ({self.max_sessions}) reached, skipping {session_id}"
            )
            return

        try:
            # Create and start transcript tailer
            tailer = TranscriptTailer(
                session_info=session_info,
                telegram_bridge=self.telegram_bridge,
                claude_launcher=self.claude_launcher,
                args=self.args,
            )

            await tailer.start()

            # Track session
            self.sessions[session_id] = SessionState(
                info=session_info, tailer=tailer, last_activity=time.time()
            )

            self.logger.info(
                f"Started monitoring session: {session_id[:8]}... ({session_info.project_path})"
            )

            # Send session start notification if enabled
            if self.args.enable_session_start_hook and self.telegram_bridge:
                await self.telegram_bridge.send_session_start_notification(session_info)

        except Exception as e:
            self.logger.error(f"Failed to add session {session_id}: {e}")

    def update_session_activity(self, transcript_path: Path):
        """Update last activity time for a session."""
        session_id = SessionUtils.extract_session_id(transcript_path)
        if session_id and session_id in self.sessions:
            self.sessions[session_id].last_activity = time.time()

    async def cleanup_inactive_sessions(self):
        """Periodically clean up inactive sessions."""
        while self.running:
            try:
                await asyncio.sleep(300)  # Check every 5 minutes

                current_time = time.time()
                inactive_sessions = []

                for session_id, session_state in self.sessions.items():
                    # Check if session is inactive
                    if (
                        current_time - session_state.last_activity
                        > path_config.SESSION_IDLE_TIMEOUT
                        or not session_state.info.transcript_path.exists()
                    ):
                        inactive_sessions.append(session_id)

                # Remove inactive sessions
                for session_id in inactive_sessions:
                    await self.remove_session(session_id)

            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error in cleanup task: {e}")

    async def remove_session(self, session_id: str):
        """Remove a session from monitoring."""
        if session_id not in self.sessions:
            return

        session_state = self.sessions[session_id]

        try:
            await session_state.tailer.stop()
            del self.sessions[session_id]
            self.logger.info(f"Stopped monitoring session: {session_id[:8]}...")
        except Exception as e:
            self.logger.error(f"Error removing session {session_id}: {e}")

    def get_active_sessions(self) -> Dict[str, SessionInfo]:
        """Get information about all active sessions."""
        return {session_id: state.info for session_id, state in self.sessions.items()}
