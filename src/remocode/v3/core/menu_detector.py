"""
Menu detection and parsing for <PERSON> Code prompts.

Identifies menu prompts, numbered options, and text input prompts
from <PERSON>'s assistant messages.
"""

import logging
import re
from dataclasses import dataclass
from typing import List, Optional

from ..config import detection_config


@dataclass
class MenuOption:
    """Represents a menu option."""

    key: str
    label: str
    full_text: str


@dataclass
class MenuPrompt:
    """Represents a detected menu prompt."""

    prompt: str
    options: List[MenuOption]
    full_text: str
    prompt_type: str = "menu"  # "menu" or "text"


class MenuDetector:
    """Detects and parses menu prompts from <PERSON>'s output."""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

        # Compile regex patterns for efficiency
        self.option_patterns = [
            re.compile(pattern) for pattern in detection_config.MENU_OPTION_PATTERNS
        ]

    def detect_menu_prompt(self, text: str) -> Optional[MenuPrompt]:
        """Detect if text contains a menu prompt."""
        if not text or not text.strip():
            return None

        # Check for menu indicators
        if not self._has_menu_indicators(text):
            return None

        # Extract menu options
        options = self._extract_menu_options(text)
        if not options:
            return None

        # Extract prompt text (text before options)
        prompt_text = self._extract_prompt_text(text, options)

        return MenuPrompt(
            prompt=prompt_text, options=options, full_text=text, prompt_type="menu"
        )

    def detect_text_prompt(self, text: str) -> Optional[str]:
        """Detect if text contains a text input prompt."""
        if not text or not text.strip():
            return None

        # Check for text prompt indicators
        for indicator in detection_config.TEXT_PROMPT_INDICATORS:
            if indicator.lower() in text.lower():
                return text.strip()

        return None

    def _has_menu_indicators(self, text: str) -> bool:
        """Check if text contains menu indicators."""
        text_lower = text.lower()

        # Check for explicit menu indicators
        for indicator in detection_config.MENU_INDICATORS:
            if indicator.lower() in text_lower:
                return True

        # Check for numbered/lettered options pattern
        if self._has_option_patterns(text):
            return True

        return False

    def _has_option_patterns(self, text: str) -> bool:
        """Check if text has option-like patterns."""
        lines = text.split("\n")
        option_count = 0

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # Check if line matches any option pattern
            for pattern in self.option_patterns:
                if pattern.match(line):
                    option_count += 1
                    break

        # Consider it a menu if we have 2+ options
        return option_count >= 2

    def _extract_menu_options(self, text: str) -> List[MenuOption]:
        """Extract menu options from text."""
        options = []
        lines = text.split("\n")

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # Try each pattern to extract option
            for pattern in self.option_patterns:
                match = pattern.match(line)
                if match:
                    key = match.group(1)
                    label = match.group(2).strip()

                    options.append(MenuOption(key=key, label=label, full_text=line))
                    break

        return options

    def _extract_prompt_text(self, text: str, options: List[MenuOption]) -> str:
        """Extract the prompt text (before options)."""
        if not options:
            return text.strip()

        # Find where options start
        first_option_text = options[0].full_text
        option_start = text.find(first_option_text)

        if option_start == -1:
            return text.strip()

        # Extract text before first option
        prompt_text = text[:option_start].strip()

        # Clean up prompt text
        prompt_text = self._clean_prompt_text(prompt_text)

        return prompt_text

    def _clean_prompt_text(self, prompt: str) -> str:
        """Clean and normalize prompt text."""
        # Remove excessive whitespace
        prompt = re.sub(r"\s+", " ", prompt)

        # Remove common prefixes/suffixes
        prompt = re.sub(
            r"^(Here is|Here\'s|Claude\'s plan:|Plan:)\s*",
            "",
            prompt,
            flags=re.IGNORECASE,
        )
        prompt = re.sub(
            r"\s*(Please choose|Select an option|Choose):?\s*$",
            "",
            prompt,
            flags=re.IGNORECASE,
        )

        # Capitalize first letter if needed
        if prompt and prompt[0].islower():
            prompt = prompt[0].upper() + prompt[1:]

        return prompt.strip()

    def is_plan_approval_prompt(self, menu_prompt: MenuPrompt) -> bool:
        """Check if this is a plan approval prompt."""
        prompt_lower = menu_prompt.prompt.lower()

        plan_indicators = [
            "proceed with the plan",
            "approve the plan",
            "plan look good",
            "ready to code",
            "would you like to proceed",
            "exit plan mode",
        ]

        return any(indicator in prompt_lower for indicator in plan_indicators)

    def is_confirmation_prompt(self, menu_prompt: MenuPrompt) -> bool:
        """Check if this is a yes/no confirmation prompt."""
        if len(menu_prompt.options) != 2:
            return False

        option_texts = [opt.label.lower() for opt in menu_prompt.options]

        # Common yes/no patterns
        yes_no_patterns = [
            ["yes", "no"],
            ["y", "n"],
            ["continue", "stop"],
            ["proceed", "cancel"],
            ["ok", "cancel"],
        ]

        for pattern in yes_no_patterns:
            if all(any(p in opt for p in pattern) for opt in option_texts):
                return True

        return False

    def get_menu_type(self, menu_prompt: MenuPrompt) -> str:
        """Classify the type of menu prompt."""
        if self.is_plan_approval_prompt(menu_prompt):
            return "plan_approval"
        elif self.is_confirmation_prompt(menu_prompt):
            return "confirmation"
        elif len(menu_prompt.options) <= 3:
            return "simple_choice"
        else:
            return "complex_menu"
