"""
Transcript tailer for monitoring Claude Code session transcripts.

Tails individual session JSONL files and extracts menu prompts
and other interactive events.
"""

import asyncio
import logging
from typing import Any, Dict, List, Optional

from ..config import path_config
from ..utils.json_parser import <PERSON>SO<PERSON>Pars<PERSON>
from ..utils.session_utils import SessionInfo
from .menu_detector import MenuDetector


class TranscriptTailer:
    """Tails a Claude Code session transcript and extracts events."""

    def __init__(
        self, session_info: SessionInfo, telegram_bridge, claude_launcher, args
    ):
        self.session_info = session_info
        self.telegram_bridge = telegram_bridge
        self.claude_launcher = claude_launcher
        self.args = args
        self.logger = logging.getLogger(__name__)

        # Parsing components
        self.json_parser = JSONLParser()
        self.menu_detector = MenuDetector()

        # State tracking
        self.file_position = 0
        self.running = False
        self.tail_task: Optional[asyncio.Task] = None
        self.recent_events: List[Dict[str, Any]] = []

        # Extract metadata from transcript
        self._update_session_metadata()

    async def start(self):
        """Start tailing the transcript file."""
        self.logger.debug(
            f"Starting transcript tailer for session {self.session_info.session_id[:8]}..."
        )

        # Initialize file position to end of file (only monitor new content)
        self.file_position = self.json_parser.get_file_position(
            self.session_info.transcript_path
        )

        self.running = True
        self.tail_task = asyncio.create_task(self.tail_loop())

    async def stop(self):
        """Stop tailing the transcript."""
        self.logger.debug(
            f"Stopping transcript tailer for session {self.session_info.session_id[:8]}..."
        )
        self.running = False

        if self.tail_task:
            self.tail_task.cancel()
            try:
                await self.tail_task
            except asyncio.CancelledError:
                pass

    async def tail_loop(self):
        """Main tailing loop."""
        while self.running:
            try:
                await self.process_new_events()
                await asyncio.sleep(path_config.TRANSCRIPT_POLL_INTERVAL)
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error in transcript tailer: {e}")
                await asyncio.sleep(5)  # Back off on errors

    async def process_new_events(self):
        """Process new events from transcript file."""
        if not self.session_info.transcript_path.exists():
            return

        # Get new events from current position
        new_events = list(
            self.json_parser.parse_tail(
                self.session_info.transcript_path, self.file_position
            )
        )

        if not new_events:
            return

        # Update file position
        self.file_position = self.json_parser.get_file_position(
            self.session_info.transcript_path
        )

        # Process each new event
        for event in new_events:
            await self.process_event(event)

        # Keep recent events for context
        self.recent_events.extend(new_events)
        if len(self.recent_events) > 50:  # Keep last 50 events
            self.recent_events = self.recent_events[-50:]

    async def process_event(self, event: Dict[str, Any]):
        """Process a single transcript event."""
        event_type = event.get("type", "") or event.get(
            "t", ""
        )  # Support both schema variants

        self.logger.debug(
            f"Processing event: {event_type} in session {self.session_info.session_id[:8]}"
        )

        # Update session metadata if available
        if "sessionId" in event:
            self._update_session_metadata_from_event(event)

        # Check for menu prompts
        if event_type == "assistant":
            await self._check_for_menu_prompt(event)
            await self._check_for_plan_context(event)
        elif event_type == "user":
            await self._check_for_text_prompt(event)

    async def _check_for_menu_prompt(self, event: Dict[str, Any]):
        """Check if assistant event contains a menu prompt."""
        message = event.get("message", {})
        content = message.get("content", [])

        if not isinstance(content, list):
            return

        # Extract text content
        text_parts = []
        for item in content:
            if isinstance(item, dict) and item.get("type") == "text":
                text_parts.append(item.get("text", ""))

        full_text = "\n".join(text_parts)

        # Detect menu prompts
        menu_prompt = self.menu_detector.detect_menu_prompt(full_text)
        if menu_prompt:
            self.logger.info(
                f"Menu prompt detected in session {self.session_info.session_id[:8]}: {menu_prompt.prompt[:50]}..."
            )

            # Send to Telegram if configured
            if self.telegram_bridge:
                await self.telegram_bridge.send_menu_prompt(
                    session_info=self.session_info,
                    menu_prompt=menu_prompt,
                    recent_events=self.recent_events,
                )

    async def _check_for_text_prompt(self, event: Dict[str, Any]):
        """Check if user event indicates a text input prompt."""
        message = event.get("message", {})
        content = message.get("content", "")

        # Check for text prompt indicators
        text_prompt = self.menu_detector.detect_text_prompt(content)
        if text_prompt:
            self.logger.info(
                f"Text prompt detected in session {self.session_info.session_id[:8]}"
            )

            # Send to Telegram if configured
            if self.telegram_bridge:
                await self.telegram_bridge.send_text_prompt(
                    session_info=self.session_info,
                    prompt_text=text_prompt,
                    recent_events=self.recent_events,
                )

    async def _check_for_plan_context(self, event: Dict[str, Any]):
        """Check if assistant event indicates plan mode context that might trigger a plan approval menu."""
        message = event.get("message", {})
        content = message.get("content", [])

        if not isinstance(content, list):
            return

        # Extract text content
        text_parts = []
        for item in content:
            if isinstance(item, dict) and item.get("type") == "text":
                text_parts.append(item.get("text", ""))

        full_text = "\n".join(text_parts)

        # Check for plan context indicators
        plan_indicators = [
            "here is claude's plan",
            "here's my plan",
            "plan to",
            "implementation plan",
            "ready to code",
            "would you like to proceed",
            "shall i proceed",
            "ready to proceed",
            "plan mode",
            "exit plan mode",
        ]

        text_lower = full_text.lower()
        has_plan_context = any(indicator in text_lower for indicator in plan_indicators)

        if has_plan_context:
            self.logger.info(
                f"Plan context detected in session {self.session_info.session_id[:8]}: {full_text[:100]}..."
            )

            # Start monitoring for plan approval menu (which won't appear in transcript)
            await self._start_plan_approval_monitoring()

    async def _start_plan_approval_monitoring(self):
        """Start monitoring for plan approval menu that appears in terminal but not in transcript."""
        self.logger.info(
            f"Starting plan approval monitoring for session {self.session_info.session_id[:8]}"
        )

        # Send a notification that plan approval is expected
        if self.telegram_bridge:
            await self.telegram_bridge.send_plan_approval_notification(
                session_info=self.session_info,
                message="🎯 Plan approval menu expected - check Claude Code terminal for options",
                recent_events=self.recent_events,
            )

    def _update_session_metadata(self):
        """Update session metadata from transcript file."""
        metadata = self.json_parser.get_session_metadata(
            self.session_info.transcript_path
        )
        if metadata:
            self.session_info.cwd = metadata.get("cwd")
            self.session_info.git_branch = metadata.get("git_branch")
            self.session_info.version = metadata.get("version")
            self.session_info.timestamp = metadata.get("timestamp")

    def _update_session_metadata_from_event(self, event: Dict[str, Any]):
        """Update session metadata from a transcript event."""
        if not self.session_info.cwd and "cwd" in event:
            self.session_info.cwd = event["cwd"]
        if not self.session_info.git_branch and "gitBranch" in event:
            self.session_info.git_branch = event["gitBranch"]
        if not self.session_info.version and "version" in event:
            self.session_info.version = event["version"]
        if not self.session_info.timestamp and "timestamp" in event:
            self.session_info.timestamp = event["timestamp"]

    def get_recent_context(self, max_chars: int = 500) -> str:
        """Get recent context for debugging."""
        return self.json_parser.find_recent_content(self.recent_events, max_chars)

    def get_session_summary(self) -> Dict[str, Any]:
        """Get summary of session state."""
        return {
            "session_id": self.session_info.session_id,
            "project_path": self.session_info.project_path,
            "cwd": self.session_info.cwd,
            "git_branch": self.session_info.git_branch,
            "version": self.session_info.version,
            "file_position": self.file_position,
            "recent_events_count": len(self.recent_events),
            "running": self.running,
        }
