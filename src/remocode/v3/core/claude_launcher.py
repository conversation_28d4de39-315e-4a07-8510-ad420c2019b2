"""
Claude Code launcher for cross-platform process management.

Provides reliable process spawning and stdin/stdout access for
Claude Code instances, eliminating the need for fragile procfs access.
"""

import asyncio
import logging
import os
import shlex
import subprocess
from pathlib import Path
from typing import Any, Dict, List, Optional

from ..config import path_config
from ..utils.session_utils import SessionInfo, SessionUtils


class ClaudeLauncher:
    """Launches and manages Claude Code processes."""

    def __init__(self, claude_cmd: str = "npx @anthropic-ai/claude-code"):
        """
        Initialize launcher.

        Args:
            claude_cmd: Command to launch Claude Code
        """
        self.claude_cmd = claude_cmd
        self.logger = logging.getLogger(__name__)

        # Active processes
        self.processes: Dict[str, subprocess.Popen] = {}
        self.session_info: Dict[str, SessionInfo] = {}

    async def launch_claude(
        self,
        working_dir: Path,
        args: Optional[List[str]] = None,
        session_id: Optional[str] = None,
    ) -> Optional[SessionInfo]:
        """
        Launch a new Claude Code process.

        Args:
            working_dir: Directory to run Claude Code in
            args: Additional arguments for Claude Code
            session_id: Optional session ID (will be generated if not provided)

        Returns:
            SessionInfo for the launched session or None if failed
        """
        try:
            # Prepare command
            cmd_parts = shlex.split(self.claude_cmd)
            if args:
                cmd_parts.extend(args)

            self.logger.info(f"Launching Claude Code in {working_dir}")
            self.logger.debug(f"Command: {' '.join(cmd_parts)}")

            # Launch Claude with stdout/stderr inherited so the normal
            # Claude Code interface is visible in the *current* terminal.
            # We still keep stdin as PIPE so the Telegram bridge can inject
            # choices programmatically.

            process = subprocess.Popen(
                cmd_parts,
                cwd=str(working_dir),
                stdin=subprocess.PIPE,
                stdout=None,  # inherit parent – shows interactive UI
                stderr=None,  # inherit parent – shows errors inline
                text=True,
                bufsize=0,  # Unbuffered for real-time interaction
                env=os.environ.copy(),
            )

            # Wait briefly to ensure process started
            await asyncio.sleep(0.5)

            if process.poll() is not None:
                # Process already exited
                stdout, stderr = process.communicate()
                self.logger.error(f"Claude Code process exited immediately: {stderr}")
                return None

            # Generate session ID if not provided
            if not session_id:
                session_id = self._generate_session_id()

            # Create session info
            project_path = str(working_dir.resolve())
            encoded_project = SessionUtils.encode_project_path(project_path)

            # We'll need to wait for the actual transcript file to be created
            # For now, create a placeholder path
            transcript_path = (
                Path(path_config.CLAUDE_PROJECTS_DIR)
                / encoded_project
                / f"{session_id}.jsonl"
            )

            session_info = SessionInfo(
                session_id=session_id,
                transcript_path=transcript_path,
                project_path=project_path,
                cwd=str(working_dir),
            )

            # Store process and session info
            self.processes[session_id] = process
            self.session_info[session_id] = session_info

            self.logger.info(
                f"Successfully launched Claude Code session {session_id[:8]} (PID: {process.pid})"
            )

            return session_info

        except Exception as e:
            self.logger.error(f"Failed to launch Claude Code: {e}")
            return None

    async def send_input(self, session_id: str, text: str) -> bool:
        """
        Send input to a Claude Code process.

        Args:
            session_id: Session ID
            text: Text to send to stdin

        Returns:
            True if successful, False otherwise
        """
        process = self.processes.get(session_id)
        if not process:
            self.logger.error(f"No process found for session {session_id}")
            return False

        try:
            # Ensure text ends with newline
            if not text.endswith("\n"):
                text += "\n"

            # Send to stdin
            process.stdin.write(text)
            process.stdin.flush()

            self.logger.info(f"Sent input to session {session_id[:8]}: {text.strip()}")
            return True

        except BrokenPipeError:
            self.logger.error(
                f"Broken pipe for session {session_id} - process may have exited"
            )
            await self.cleanup_session(session_id)
            return False
        except Exception as e:
            self.logger.error(f"Error sending input to session {session_id}: {e}")
            return False

    async def cleanup_session(self, session_id: str):
        """Clean up a session and terminate its process."""
        process = self.processes.get(session_id)
        if process:
            try:
                # Try graceful termination first
                if process.poll() is None:
                    process.terminate()

                    # Wait briefly for graceful exit
                    try:
                        await asyncio.wait_for(
                            asyncio.create_task(self._wait_for_process(process)),
                            timeout=5.0,
                        )
                    except asyncio.TimeoutError:
                        # Force kill if necessary
                        self.logger.warning(
                            f"Force killing Claude process for session {session_id}"
                        )
                        process.kill()

                # Clean up streams
                if process.stdin:
                    process.stdin.close()
                if process.stdout:
                    process.stdout.close()
                if process.stderr:
                    process.stderr.close()

            except Exception as e:
                self.logger.error(f"Error cleaning up session {session_id}: {e}")
            finally:
                # Remove from tracking
                self.processes.pop(session_id, None)
                self.session_info.pop(session_id, None)

        self.logger.info(f"Cleaned up session {session_id}")

    async def _wait_for_process(self, process: subprocess.Popen):
        """Wait for process to exit (async wrapper)."""
        while process.poll() is None:
            await asyncio.sleep(0.1)

    def _generate_session_id(self) -> str:
        """Generate a new session ID."""
        import uuid

        return str(uuid.uuid4())

    def get_process(self, session_id: str) -> Optional[subprocess.Popen]:
        """Get the process for a session."""
        return self.processes.get(session_id)

    def get_session_info(self, session_id: str) -> Optional[SessionInfo]:
        """Get session info for a session."""
        return self.session_info.get(session_id)

    def is_session_running(self, session_id: str) -> bool:
        """Check if a session is still running."""
        process = self.processes.get(session_id)
        return process is not None and process.poll() is None

    async def stop_all_sessions(self):
        """Stop all managed sessions."""
        self.logger.info("Stopping all Claude Code sessions...")

        # Get list of session IDs to avoid dict changing during iteration
        session_ids = list(self.processes.keys())

        for session_id in session_ids:
            await self.cleanup_session(session_id)

        self.logger.info("All Claude Code sessions stopped")

    def get_active_sessions(self) -> Dict[str, SessionInfo]:
        """Get information about all active sessions."""
        active_sessions = {}

        for session_id, session_info in self.session_info.items():
            if self.is_session_running(session_id):
                active_sessions[session_id] = session_info

        return active_sessions

    def get_status(self) -> Dict[str, Any]:
        """Get launcher status information."""
        active_count = sum(
            1 for sid in self.processes.keys() if self.is_session_running(sid)
        )

        return {
            "total_sessions": len(self.processes),
            "active_sessions": active_count,
            "claude_command": self.claude_cmd,
            "session_ids": list(self.processes.keys()),
        }
