"""
Session management utilities for RemoCode v3.

Provides helper functions for session detection, metadata extraction,
and path manipulation.
"""

import re
from dataclasses import dataclass
from pathlib import Path
from typing import List, Optional

from ..config import path_config


@dataclass
class SessionInfo:
    """Information about a Claude Code session."""

    session_id: str
    transcript_path: Path
    project_path: str
    cwd: Optional[str] = None
    git_branch: Optional[str] = None
    version: Optional[str] = None
    timestamp: Optional[str] = None


class SessionUtils:
    """Utilities for Claude Code session management."""

    @staticmethod
    def is_session_file(file_path: Path) -> bool:
        """Check if a file is a Claude session transcript."""
        if not file_path.suffix == ".jsonl":
            return False

        # Check if filename matches session UUID pattern
        return bool(re.match(path_config.SESSION_UUID_PATTERN, file_path.name))

    @staticmethod
    def extract_session_id(file_path: Path) -> Optional[str]:
        """Extract session ID from transcript file path."""
        if not SessionUtils.is_session_file(file_path):
            return None
        return file_path.stem  # filename without .jsonl extension

    @staticmethod
    def decode_project_path(encoded_path: str) -> str:
        """Decode project path from Claude's encoding scheme."""
        # Claude encodes paths like: -Users-geoffreyhung-Desktop-github-remocode
        # Convert back to: /Users/<USER>/Desktop/github/remocode
        if encoded_path.startswith("-"):
            # Remove leading dash and replace remaining dashes with slashes
            decoded = "/" + encoded_path[1:].replace("-", "/")
            return decoded
        return encoded_path

    @staticmethod
    def encode_project_path(project_path: str) -> str:
        """Encode project path using Claude's scheme."""
        # Convert /Users/<USER>/Desktop/github/remocode
        # to: -Users-geoffreyhung-Desktop-github-remocode
        if project_path.startswith("/"):
            encoded = "-" + project_path[1:].replace("/", "-")
            return encoded
        return project_path

    @staticmethod
    def find_active_sessions(projects_dir: Path) -> List[SessionInfo]:
        """Find all active Claude sessions."""
        sessions = []

        try:
            if not projects_dir.exists():
                return sessions

            for project_dir in projects_dir.iterdir():
                if not project_dir.is_dir():
                    continue

                # Find all session files in this project directory
                for session_file in project_dir.glob(path_config.SESSION_FILE_PATTERN):
                    if SessionUtils.is_session_file(session_file):
                        session_info = SessionUtils.create_session_info(session_file)
                        if session_info:
                            sessions.append(session_info)

        except Exception:
            pass  # Silently handle permission errors, etc.

        return sessions

    @staticmethod
    def create_session_info(transcript_path: Path) -> Optional[SessionInfo]:
        """Create SessionInfo from transcript file path."""
        session_id = SessionUtils.extract_session_id(transcript_path)
        if not session_id:
            return None

        # Extract project path from parent directory name
        project_encoded = transcript_path.parent.name
        project_path = SessionUtils.decode_project_path(project_encoded)

        return SessionInfo(
            session_id=session_id,
            transcript_path=transcript_path,
            project_path=project_path,
        )

    @staticmethod
    def get_session_short_id(session_id: str, length: int = 8) -> str:
        """Get shortened session ID for display."""
        return session_id[:length]

    @staticmethod
    def get_project_name(project_path: str) -> str:
        """Extract project name from full path."""
        return Path(project_path).name

    @staticmethod
    def is_session_active(session_info: SessionInfo) -> bool:
        """Check if a session appears to be active."""
        # Simple heuristic: file exists and is not too old
        if not session_info.transcript_path.exists():
            return False

        try:
            # Check if file was modified recently (within last hour)
            import time

            file_age = time.time() - session_info.transcript_path.stat().st_mtime
            return file_age < path_config.SESSION_IDLE_TIMEOUT
        except Exception:
            return True  # Assume active if we can't check

    @staticmethod
    def watch_session_directories(projects_dir: Path) -> List[Path]:
        """Get list of directories to watch for new sessions."""
        watch_dirs = [projects_dir]

        try:
            if projects_dir.exists():
                # Also watch existing project directories
                for project_dir in projects_dir.iterdir():
                    if project_dir.is_dir():
                        watch_dirs.append(project_dir)
        except Exception:
            pass

        return watch_dirs

    @staticmethod
    def normalize_path(path: str) -> str:
        """Normalize a file system path."""
        return str(Path(path).resolve())

    @staticmethod
    def safe_filename(text: str, max_length: int = 50) -> str:
        """Convert text to safe filename."""
        # Remove/replace unsafe characters
        safe = re.sub(r"[^\w\-_\.]", "_", text)
        # Truncate if too long
        if len(safe) > max_length:
            safe = safe[: max_length - 3] + "..."
        return safe
