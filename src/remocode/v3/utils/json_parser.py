"""
JSON Lines (JSONL) parsing utilities for Claude transcripts.

Handles streaming JSON parsing, event filtering, and transcript processing.
"""

import json
import logging
from pathlib import Path
from typing import Any, Dict, Iterator, List, Optional


class JSONLParser:
    """Parser for JSON Lines transcript files."""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

    def parse_line(self, line: str) -> Optional[Dict[str, Any]]:
        """Parse a single JSONL line."""
        line = line.strip()
        if not line:
            return None

        try:
            return json.loads(line)
        except json.JSONDecodeError as e:
            self.logger.warning(f"Failed to parse JSON line: {e}")
            return None

    def parse_file(self, file_path: Path) -> Iterator[Dict[str, Any]]:
        """Parse entire JSONL file, yielding events."""
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                for line_num, line in enumerate(f, 1):
                    event = self.parse_line(line)
                    if event:
                        yield event
        except FileNotFoundError:
            self.logger.warning(f"Transcript file not found: {file_path}")
        except Exception as e:
            self.logger.error(f"Error reading transcript {file_path}: {e}")

    def parse_tail(
        self, file_path: Path, start_pos: int = 0
    ) -> Iterator[Dict[str, Any]]:
        """Parse file from a specific position (for tailing)."""
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                f.seek(start_pos)
                for line in f:
                    event = self.parse_line(line)
                    if event:
                        yield event
        except FileNotFoundError:
            self.logger.debug(
                f"Transcript file not found (expected during startup): {file_path}"
            )
        except Exception as e:
            self.logger.error(f"Error tailing transcript {file_path}: {e}")

    def get_file_position(self, file_path: Path) -> int:
        """Get current file size/position for tailing."""
        try:
            return file_path.stat().st_size
        except FileNotFoundError:
            return 0

    def filter_events(
        self, events: Iterator[Dict[str, Any]], event_types: List[str]
    ) -> Iterator[Dict[str, Any]]:
        """Filter events by type."""
        for event in events:
            if event.get("type") in event_types:
                yield event

    def extract_user_messages(
        self, events: Iterator[Dict[str, Any]]
    ) -> Iterator[Dict[str, Any]]:
        """Extract user messages from events."""
        for event in events:
            if event.get("type") == "user" and "message" in event:
                yield event

    def extract_assistant_messages(
        self, events: Iterator[Dict[str, Any]]
    ) -> Iterator[Dict[str, Any]]:
        """Extract assistant messages from events."""
        for event in events:
            if event.get("type") == "assistant" and "message" in event:
                yield event

    def get_session_metadata(self, file_path: Path) -> Optional[Dict[str, Any]]:
        """Extract session metadata from the first few events."""
        try:
            events = list(self.parse_file(file_path))
            if not events:
                return None

            # Look for session info in first few events
            for event in events[:5]:
                if "sessionId" in event:
                    return {
                        "session_id": event.get("sessionId"),
                        "cwd": event.get("cwd"),
                        "git_branch": event.get("gitBranch"),
                        "version": event.get("version"),
                        "timestamp": event.get("timestamp"),
                    }
            return None
        except Exception as e:
            self.logger.error(
                f"Error extracting session metadata from {file_path}: {e}"
            )
            return None

    def find_recent_content(
        self, events: List[Dict[str, Any]], max_chars: int = 500
    ) -> str:
        """Find recent relevant content for debugging."""
        content_parts = []
        total_chars = 0

        # Work backwards through events to get recent context
        for event in reversed(events[-10:]):  # Last 10 events
            event_content = self._extract_event_content(event)
            if event_content and total_chars + len(event_content) < max_chars:
                content_parts.insert(0, event_content)
                total_chars += len(event_content)
            elif total_chars > 0:
                break

        return "\n".join(content_parts)

    def _extract_event_content(self, event: Dict[str, Any]) -> Optional[str]:
        """Extract readable content from an event."""
        event_type = event.get("type", "")

        if event_type == "user":
            message = event.get("message", {})
            return f"User: {message.get('content', '')[:200]}"

        elif event_type == "assistant":
            message = event.get("message", {})
            content = message.get("content", [])
            if isinstance(content, list) and content:
                # Extract text from content array
                text_parts = []
                for item in content:
                    if isinstance(item, dict) and item.get("type") == "text":
                        text_parts.append(item.get("text", ""))
                text = " ".join(text_parts)[:200]
                return f"Assistant: {text}"

        return None
