# RemoCode v3 - File Watcher Implementation

A clean, read-only approach to Claude Code integration that monitors transcript files and provides Telegram integration without modifying Claude Code itself.

## Key Features

- **Zero Coupling**: No modifications to Claude Code required
- **File Watcher Based**: Monitors `~/.claude/projects` for session changes
- **Read-Only**: Never writes to `.claude/` directory
- **CLI Controlled**: All debug features via command line flags
- **Multi-Session**: Handle multiple concurrent Claude sessions
- **Restart Resilient**: Bridge can restart and reattach automatically

## Quick Start

```bash
# Basic usage
python -m remocode.v3.main

# With debug modes
python -m remocode.v3.main --show-full-json
python -m remocode.v3.main --show-session-info
python -m remocode.v3.main --minimal-mode
python -m remocode.v3.main --debug-all

# With session notifications
python -m remocode.v3.main --enable-session-start-hook
```

## Configuration

Set environment variables for Telegram integration:

```bash
export TG_TOKEN=your_bot_token      # From @BotFather
export TG_CHAT=your_chat_id         # Your Telegram chat ID
```

## Architecture

```
v3/
├── main.py                    # Entry point with CLI args
├── config.py                  # Constants and debug templates
├── core/                      # Core monitoring components
│   ├── session_watcher.py     # Monitor ~/.claude/projects
│   ├── transcript_tailer.py   # Parse JSONL transcripts
│   └── menu_detector.py       # Extract menu prompts
├── bridges/                   # External integrations
│   ├── telegram_bridge.py     # Send to Telegram
│   └── claude_writer.py       # Write to Claude stdin
└── utils/                     # Shared utilities
    ├── json_parser.py         # JSONL parsing
    └── session_utils.py       # Session management
```

## Debug Modes

- `--minimal-mode`: Only essential menu text
- `--show-session-info`: Include session metadata
- `--show-full-json`: Include transcript snippets
- `--debug-all`: Enable all debug features
- `--enable-session-start-hook`: Notify on session start

## How It Works

1. **SessionWatcher** monitors `~/.claude/projects/` for new `.jsonl` files
2. **TranscriptTailer** tails each session transcript for menu events
3. **MenuDetector** extracts menu prompts from assistant messages
4. **TelegramBridge** sends formatted messages with inline keyboards
5. **ClaudeWriter** writes responses back to Claude's stdin

## Telegram Message Formats

**Minimal Mode:**
```
Proceed with plan? [Yes|Modify|No]
```

**Standard Mode:**
```
(a1b2c3d4) Proceed with plan? [Yes|Modify|No]
```

**Debug Mode:**
```
(a1b2c3d4) Proceed with plan? [Yes|Modify|No]
📍 Session: my-project
```

**Full JSON Mode:**
```
(a1b2c3d4) Proceed with plan? [Yes|Modify|No]
📍 Session: my-project
```json
{"type":"assistant","message":{"content":[...]}}
```

## Benefits Over v2

- **No Hooks Required**: No `.claude/settings.local.json` modification
- **Future Proof**: Works with any Claude CLI version
- **Cleaner Architecture**: Simple file watching vs complex PTY hijacking
- **Better Error Handling**: Graceful degradation when Telegram unavailable
- **Multi-Session Support**: Handle multiple Claude instances simultaneously

## Requirements

- Python 3.10+
- `psutil` for process detection
- `watchdog` for file monitoring (optional, falls back to polling)
- Claude Code installed and configured

## Testing

Start Claude Code in one terminal:
```bash
npx @anthropic-ai/claude-code
```

Start v3 bridge in another:
```bash
python -m remocode.v3.main --debug-all
```

The bridge will automatically detect the Claude session and forward any menu prompts to Telegram.
