"""
RemoCode v3 - File Watcher Approach

A clean, read-only implementation that monitors Claude Code transcript files
and provides Telegram integration without modifying Claude Code itself.

Key Features:
- Zero coupling to Claude Code internals
- File watcher based session detection
- Read-only transcript monitoring
- CLI-controlled debug modes
- Multi-session support
- Restart resilient operation

Architecture:
- SessionWatcher: Monitor ~/.claude/projects for new sessions
- TranscriptTailer: Parse JSONL transcripts for menu events
- TelegramBridge: Send menus to Telegram, receive responses
- ClaudeWriter: Write responses to <PERSON> stdin
"""

__version__ = "3.0.0"
__author__ = "RemoCode Team"

from .config import DebugConfig
from .main import main

__all__ = ["main", "DebugConfig"]
