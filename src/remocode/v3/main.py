#!/usr/bin/env python3
"""
RemoCode v3 - Main Entry Point

File watcher based Claude Code integration with Telegram.
Provides CLI-controlled debug modes and session monitoring.

Usage:
    python -m remocode.v3.main                     # Basic mode
    python -m remocode.v3.main --show-full-json    # Include transcript snippets
    python -m remocode.v3.main --show-session-info # Include session metadata
    python -m remocode.v3.main --minimal-mode      # Only essential menu text
    python -m remocode.v3.main --debug-all         # Enable all debug features
    python -m remocode.v3.main --enable-session-start-hook  # Notify on session start
"""

import argparse
import asyncio
import logging
import signal
import sys
from pathlib import Path
from typing import Optional

from .bridges.telegram_bridge import TelegramBridge
from .config import path_config, telegram_config
from .core.claude_launcher import ClaudeLauncher
from .core.session_watcher import SessionWatcher


class RemoCodeV3:
    """Main orchestrator for RemoCode v3."""

    def __init__(self, args: argparse.Namespace):
        """Initialize with CLI arguments."""
        self.args = args
        self.session_watcher: Optional[SessionWatcher] = None
        self.telegram_bridge: Optional[TelegramBridge] = None
        self.claude_launcher: Optional[ClaudeLauncher] = None
        self.running = False

        # Setup logging
        log_level = logging.DEBUG if args.verbose else logging.INFO
        logging.basicConfig(
            level=log_level,
            format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        )
        self.logger = logging.getLogger(__name__)

    async def start(self):
        """Start the v3 daemon."""
        self.logger.info("Starting RemoCode v3...")

        # Validate configuration
        if not self._validate_config():
            return 1

        # Initialize components
        self.telegram_bridge = TelegramBridge(self.args)
        await self.telegram_bridge.start()

        # Initialize Claude launcher if spawning mode enabled
        if self.args.spawn_claude:
            self.claude_launcher = ClaudeLauncher(self.args.claude_cmd)
            self.telegram_bridge.set_claude_launcher(self.claude_launcher)

        self.session_watcher = SessionWatcher(
            projects_dir=Path(path_config.CLAUDE_PROJECTS_DIR),
            telegram_bridge=self.telegram_bridge,
            claude_launcher=self.claude_launcher,
            args=self.args,
        )

        # Setup signal handlers
        self._setup_signal_handlers()

        try:
            self.running = True
            self.logger.info("RemoCode v3 daemon started successfully")

            # Start monitoring
            await self.session_watcher.start()

            # Keep running until interrupted
            while self.running:
                await asyncio.sleep(1)

        except KeyboardInterrupt:
            self.logger.info("Received interrupt signal")
        except Exception as e:
            self.logger.error(f"Unexpected error: {e}")
            return 1
        finally:
            await self.stop()

        return 0

    async def stop(self):
        """Stop the daemon and cleanup."""
        self.logger.info("Stopping RemoCode v3...")
        self.running = False

        if self.session_watcher:
            await self.session_watcher.stop()

        if self.telegram_bridge:
            await self.telegram_bridge.stop()

        if self.claude_launcher:
            await self.claude_launcher.stop_all_sessions()

        self.logger.info("RemoCode v3 stopped")

    def _validate_config(self) -> bool:
        """Validate configuration and requirements."""
        # Check if Claude projects directory exists
        projects_dir = Path(path_config.CLAUDE_PROJECTS_DIR)
        if not projects_dir.exists():
            self.logger.error(f"Claude projects directory not found: {projects_dir}")
            self.logger.info("Please ensure Claude Code has been run at least once")
            return False

        # Check Telegram configuration
        if not telegram_config.is_configured():
            self.logger.warning(
                "Telegram not configured (TG_TOKEN and TG_CHAT required)"
            )
            self.logger.info("Running in local-only mode")

        # Display configuration
        self._log_configuration()

        return True

    def _log_configuration(self):
        """Log current configuration."""
        self.logger.info("Configuration:")
        self.logger.info(f"  Projects directory: {path_config.CLAUDE_PROJECTS_DIR}")
        self.logger.info(f"  Telegram configured: {telegram_config.is_configured()}")
        self.logger.info(
            f"  Debug mode: {self.args.debug_all or self.args.show_full_json}"
        )
        self.logger.info(f"  Minimal mode: {self.args.minimal_mode}")
        self.logger.info(f"  Session info: {self.args.show_session_info}")
        self.logger.info(f"  Session start hook: {self.args.enable_session_start_hook}")
        self.logger.info(f"  Spawn Claude: {self.args.spawn_claude}")
        if self.args.spawn_claude:
            self.logger.info(f"  Claude command: {self.args.claude_cmd}")

    def _setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown."""

        def signal_handler(signum, frame):
            self.logger.info(f"Received signal {signum}")
            self.running = False

        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)


def create_parser() -> argparse.ArgumentParser:
    """Create command line argument parser."""
    parser = argparse.ArgumentParser(
        description="RemoCode v3 - File watcher based Claude Code integration",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python -m remocode.v3.main                        # Basic mode (attach to existing)
  python -m remocode.v3.main --show-full-json       # Include transcript snippets
  python -m remocode.v3.main --show-session-info    # Include session metadata
  python -m remocode.v3.main --minimal-mode         # Only essential menu text
  python -m remocode.v3.main --debug-all            # Enable all debug features
  python -m remocode.v3.main --spawn-claude         # Launch Claude processes
  python -m remocode.v3.main --enable-session-start-hook  # Notify on session start

Environment Variables:
  TG_TOKEN    Telegram bot token (from @BotFather)
  TG_CHAT     Telegram chat ID for notifications
        """,
    )

    # Debug mode flags
    debug_group = parser.add_argument_group("Debug Options")
    debug_group.add_argument(
        "--show-full-json",
        action="store_true",
        help="Include transcript snippets in Telegram messages",
    )
    debug_group.add_argument(
        "--show-session-info",
        action="store_true",
        help="Include session metadata in messages",
    )
    debug_group.add_argument(
        "--minimal-mode",
        action="store_true",
        help="Send only essential menu text (no session prefixes)",
    )
    debug_group.add_argument(
        "--debug-all", action="store_true", help="Enable all debug features"
    )

    # Feature flags
    feature_group = parser.add_argument_group("Feature Options")
    feature_group.add_argument(
        "--enable-session-start-hook",
        action="store_true",
        help="Send Telegram notification when new Claude sessions start",
    )
    feature_group.add_argument(
        "--max-sessions",
        type=int,
        default=path_config.MAX_SESSIONS_TO_MONITOR,
        help=f"Maximum number of sessions to monitor (default: {path_config.MAX_SESSIONS_TO_MONITOR})",
    )
    feature_group.add_argument(
        "--spawn-claude",
        action="store_true",
        help="Launch Claude processes (vs attach to existing)",
    )
    feature_group.add_argument(
        "--claude-cmd",
        default="npx @anthropic-ai/claude-code",
        help="Custom Claude Code command (default: npx @anthropic-ai/claude-code)",
    )
    feature_group.add_argument(
        "--polling-interval",
        type=float,
        default=1.0,
        help="Telegram polling interval in seconds (default: 1.0)",
    )

    # General options
    parser.add_argument(
        "--verbose", "-v", action="store_true", help="Enable verbose logging"
    )
    parser.add_argument("--version", action="version", version="RemoCode v3.0.0")

    return parser


async def main():
    """Main entry point."""
    parser = create_parser()
    args = parser.parse_args()

    # Handle debug-all flag
    if args.debug_all:
        args.show_full_json = True
        args.show_session_info = True
        args.enable_session_start_hook = True

    # Create and run daemon
    daemon = RemoCodeV3(args)
    exit_code = await daemon.start()
    sys.exit(exit_code)


if __name__ == "__main__":
    asyncio.run(main())
