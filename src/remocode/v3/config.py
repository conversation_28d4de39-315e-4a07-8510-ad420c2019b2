"""
Configuration and constants for RemoCode v3.

Contains debug message templates, default settings, and configuration
constants used throughout the v3 implementation.
"""

import os
from dataclasses import dataclass
from typing import Optional


@dataclass
class DebugConfig:
    """Debug configuration and message templates."""

    # Message templates for different debug levels
    MINIMAL_TEMPLATE = "{prompt}"
    STANDARD_TEMPLATE = "({session_short}) {prompt}"
    DEBUG_TEMPLATE = "({session_short}) {prompt}\n📍 Session: {project_path}"
    FULL_JSON_TEMPLATE = (
        "({session_short}) {prompt}\n"
        "📍 Session: {project_path}\n"
        "```json\n{transcript_snippet}\n```"
    )

    # Session start notification templates
    SESSION_START_MINIMAL = "🚀 Claude session started"
    SESSION_START_DEBUG = (
        "🚀 Claude session started\n"
        "📍 Project: {project_path}\n"
        "🆔 Session: {session_id}\n"
        "📂 CWD: {cwd}"
    )

    # Default Telegram settings
    DEFAULT_DISABLE_NOTIFICATION = True
    DEFAULT_PARSE_MODE = "Markdown"
    DEFAULT_TIMEOUT_SECONDS = 300

    # Session ID display length
    SESSION_SHORT_LENGTH = 8

    # Transcript snippet settings
    MAX_SNIPPET_LINES = 10
    MAX_SNIPPET_CHARS = 500


@dataclass
class PathConfig:
    """File system paths and patterns."""

    # Claude Code session locations
    CLAUDE_PROJECTS_DIR = os.path.expanduser("~/.claude/projects")
    CLAUDE_CONFIG_DIR = os.path.expanduser("~/.claude")

    # Session file patterns
    SESSION_FILE_PATTERN = "*.jsonl"
    SESSION_UUID_PATTERN = (
        r"[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}\.jsonl"
    )

    # Polling intervals (seconds)
    WATCHER_POLL_INTERVAL = 1.0
    TRANSCRIPT_POLL_INTERVAL = 0.5

    # File monitoring settings
    MAX_SESSIONS_TO_MONITOR = 50
    SESSION_IDLE_TIMEOUT = 3600  # 1 hour


@dataclass
class TelegramConfig:
    """Telegram-specific configuration."""

    # Required environment variables
    TOKEN_ENV_VAR = "TG_TOKEN"
    CHAT_ENV_VAR = "TG_CHAT"

    # API settings
    API_BASE_URL = "https://api.telegram.org/bot"
    MAX_MESSAGE_LENGTH = 4096
    MAX_CALLBACK_DATA_LENGTH = 64

    # Rate limiting
    MAX_REQUESTS_PER_SECOND = 30
    MAX_REQUESTS_PER_MINUTE = 20

    # Retry settings
    MAX_RETRIES = 3
    RETRY_DELAY = 1.0
    BACKOFF_MULTIPLIER = 2.0

    @classmethod
    def get_token(cls) -> Optional[str]:
        """Get Telegram bot token from environment."""
        return os.getenv(cls.TOKEN_ENV_VAR)

    @classmethod
    def get_chat_id(cls) -> Optional[str]:
        """Get Telegram chat ID from environment."""
        return os.getenv(cls.CHAT_ENV_VAR)

    @classmethod
    def is_configured(cls) -> bool:
        """Check if Telegram is properly configured."""
        return bool(cls.get_token() and cls.get_chat_id())


@dataclass
class DetectionConfig:
    """Menu and event detection settings."""

    # Menu prompt detection patterns
    MENU_INDICATORS = [
        "Would you like to proceed?",
        "Ready to code?",
        "Here is Claude's plan:",
        "❯ 1.",
        "Choose an option:",
        "What would you like to do?",
        "Select:",
        "Exit plan mode:",
        "Proceed with the plan?",
    ]

    # JSON event types to monitor
    MONITORED_EVENT_TYPES = [
        "user",
        "assistant",
        "tool_use",
        "tool_result",
    ]

    # Menu option patterns
    MENU_OPTION_PATTERNS = [
        r"^\s*(\d+)\.\s*(.+)$",  # "1. Option text"
        r"^\s*([a-zA-Z])\)\s*(.+)$",  # "a) Option text"
        r"^\s*\[([^\]]+)\]\s*(.+)$",  # "[key] Option text"
    ]

    # Text prompt detection
    TEXT_PROMPT_INDICATORS = [
        "Enter your response:",
        "Please provide:",
        "Type your answer:",
        "Input required:",
    ]


# Global configuration instances
debug_config = DebugConfig()
path_config = PathConfig()
telegram_config = TelegramConfig()
detection_config = DetectionConfig()
