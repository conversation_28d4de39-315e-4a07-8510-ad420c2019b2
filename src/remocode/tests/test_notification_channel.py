"""Pytest tests for notification channel abstraction."""

import json
import sys
from pathlib import Path
from types import SimpleNamespace
from typing import Any

import pytest

# add project root so we can import module relatively
project_root = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(project_root))

from adapters import notification_channel as nc  # type: ignore  # noqa: E402


class FakeHTTPResponse:
    """Minimal file-like object returned by urllib.request.urlopen."""

    def __init__(self, payload: dict) -> None:
        self._bytes = json.dumps(payload).encode()

    def read(self) -> bytes:  # noqa: D401
        return self._bytes

    def __enter__(self) -> Any:
        return self

    def __exit__(self, exc_type: Any, exc: Any, tb: Any) -> bool:
        return False


@pytest.fixture(autouse=True)
def _set_env(monkeypatch: Any) -> None:
    """Ensure env vars are present and cached channel cleared each test."""
    monkeypatch.setenv("TG_TOKEN", "TEST_TOKEN")
    monkeypatch.setenv("TG_CHAT", "12345")
    nc._DEFAULT_CHANNEL = None  # type: ignore[attr-defined]


def _patch_urlopen(monkeypatch: Any, payload: dict) -> None:
    fake_resp = FakeHTTPResponse(payload)

    def _fake_req(*_a: Any, **_kw: Any) -> FakeHTTPResponse:
        return fake_resp

    monkeypatch.setattr("urllib.request.urlopen", _fake_req)
    return fake_resp


def test_send_message(monkeypatch: Any) -> None:
    _patch_urlopen(monkeypatch, {"ok": True, "result": {"message_id": 111}})

    ch = nc.get_notification_channel()
    mid = ch.send_message("hello")

    assert mid == 111


def test_edit_and_delete(monkeypatch: Any) -> None:
    call_counter = SimpleNamespace(count=0)

    def fake_resp(*args, **kwargs):  # noqa: D401
        call_counter.count += 1
        return FakeHTTPResponse({"ok": True, "result": True})

    monkeypatch.setattr("urllib.request.urlopen", fake_resp)

    ch = nc.get_notification_channel()
    ch.edit_message(1, "new")
    ch.delete_message(1)

    assert call_counter.count == 2


def test_send_document(monkeypatch: Any, tmp_path: Any) -> None:
    # Patch subprocess.run to avoid real curl call
    called = {}

    def fake_run(*args: Any, **kwargs: Any) -> Any:
        called["ran"] = True

    monkeypatch.setattr("subprocess.run", fake_run)

    test_file = tmp_path / "dummy.txt"
    test_file.write_text("x")

    ch = nc.get_notification_channel()
    ch.send_document(str(test_file), caption="cap")

    assert called.get("ran", False) is True
