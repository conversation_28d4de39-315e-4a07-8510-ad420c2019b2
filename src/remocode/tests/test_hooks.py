#!/usr/bin/env python3
"""
test_hooks.py - Test the hook-based menu detection system
"""

import asyncio
import json

import pytest

from remocode.hooks.menu_coordinator import MenuCoordinator
from remocode.hooks.menu_detector import HookMenuDetector, MenuType


@pytest.mark.asyncio
async def test_menu_detection():
    """Test menu detection functionality"""
    detector = HookMenuDetector()

    test_prompts = [
        "Would you like me to proceed with the plan? (y/n)",
        "1. Yes, and auto-accept edits\n2. Yes, and manually approve edits\n3. No, keep planning",
        "1. Yes, proceed with the plan\n2. Modify the plan\n3. No, cancel",
        "1. Add new feature\n2. Fix bug\n3. Refactor code\n4. Write tests",
        "This is just a regular prompt with no menu options",
    ]

    print("🧪 Testing menu detection...")

    for i, prompt in enumerate(test_prompts, 1):
        print(f"\n--- Test {i} ---")
        print(f"Prompt: {prompt[:50]}...")

        menu_type, options = detector.detect_menu_type_and_options(prompt)

        print(f"Detected type: {menu_type}")
        print(f"Options found: {len(options)}")

        for option in options:
            print(f"  {option.key}: {option.text}")

        if menu_type != MenuType.UNKNOWN:
            # Test relay to Telegram (will write to state file)
            await detector.relay_menu_to_telegram(menu_type, options, prompt)
            print("✓ Menu relayed to state file")


def test_menu_coordinator():
    """Test menu coordination functionality"""
    coordinator = MenuCoordinator()

    print("\n🧪 Testing menu coordinator...")

    # Create a test menu state
    test_menu = {
        "event": "menu_detected",
        "menu_type": MenuType.YES_NO_PROMPT,
        "prompt": "Test prompt (y/n)?",
        "options": [
            {"key": "y", "text": "Yes", "description": "Confirm"},
            {"key": "n", "text": "No", "description": "Cancel"},
        ],
    }

    # Write test state
    with open(".remocode_menu.json", "w") as f:
        json.dump(test_menu, f, indent=2)

    # Load and verify
    loaded_state = coordinator.load_menu_state()
    if loaded_state:
        print("✓ Menu state loaded successfully")
        print(f"  Type: {loaded_state['menu_type']}")
        print(f"  Options: {len(loaded_state['options'])}")
    else:
        print("❌ Failed to load menu state")

    # Clean up
    coordinator.clear_state_files()
    print("✓ State files cleared")


async def main():
    """Run all tests"""
    print("🚀 Starting hook system tests...\n")

    await test_menu_detection()
    test_menu_coordinator()

    print("\n✅ All tests completed!")


if __name__ == "__main__":
    asyncio.run(main())
