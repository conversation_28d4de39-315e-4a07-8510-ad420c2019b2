#!/usr/bin/env python3
"""
menu_coordinator.py - Enhanced coordination for dual interface system
Handles true "first response wins" coordination between terminal and Telegram
with improved race condition handling and interface synchronization
"""

import asyncio
import json
import os
import sys
import time
import urllib.parse
import urllib.request
from pathlib import Path
from typing import Any, Dict, Optional

# Add the parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))

TG_TOKEN = os.getenv("TG_TOKEN")
TG_CHAT = os.getenv("TG_CHAT")

STATE_DIR = Path(__file__).parent.parent / "state"
STATE_DIR.mkdir(exist_ok=True)

# Enhanced coordination files
RESPONSE_LOCK_FILE = STATE_DIR / "response.lock"
ACTIVE_MENU_FILE = STATE_DIR / "active_menu.json"


class MenuCoordinator:
    """Enhanced coordinator for dual interface system with true 'first response wins' behavior"""

    def __init__(self):
        self.menu_state_file = STATE_DIR / "menu.json"
        self.notification_state_file = STATE_DIR / "notification.json"
        self.response_file = STATE_DIR / "response.json"
        self.active_menu_file = ACTIVE_MENU_FILE
        self.response_lock_file = RESPONSE_LOCK_FILE

    def wait_for_response(self, timeout: float = 300.0) -> Optional[str]:
        """
        Enhanced wait for user response with true 'first response wins' behavior

        This method implements atomic response handling to ensure that whichever
        interface (terminal or Telegram) responds first takes precedence, with
        proper cleanup and synchronization.

        Args:
            timeout: Maximum wait time in seconds

        Returns:
            User response string or None if timeout
        """
        start_time = time.time()
        last_update_id: Optional[int] = None

        # Create active menu marker for coordination
        self._mark_menu_active()

        try:
            while time.time() - start_time < timeout:
                # Check if another process already handled the response
                if self._is_response_handled():
                    existing_response = self._get_existing_response()
                    if existing_response:
                        return existing_response
                    break

                # Check for terminal input (non-blocking)
                terminal_response = self._check_terminal_input()
                if terminal_response and self._try_claim_response():
                    self._write_response(terminal_response, "terminal")
                    self._cleanup_telegram_menu()
                    return terminal_response

                # Check for Telegram response
                telegram_response = self._check_telegram_updates(last_update_id)
                if telegram_response:
                    response, last_update_id = telegram_response
                    if self._try_claim_response():
                        self._write_response(response, "telegram")
                        return response

                # Small delay to prevent busy waiting
                time.sleep(0.1)

        finally:
            # Always cleanup when done
            self._cleanup_menu_state()

        return None

    def _check_terminal_input(self) -> Optional[str]:
        """Check for terminal input (non-blocking) - DISABLED to avoid conflicts with Claude Code"""
        # NOTE: We don't read from terminal stdin anymore to avoid interfering
        # with Claude Code's own input handling. The user should interact with
        # Claude Code normally or use Telegram for menu responses.
        return None

    async def wait_for_telegram_response_only(
        self, timeout: float = 300.0
    ) -> Optional[str]:
        """
        Enhanced Telegram-only response waiting with proper coordination

        This method waits only for Telegram responses while ensuring proper
        coordination with terminal input handling. It respects the 'first response
        wins' principle and cleans up appropriately.

        Args:
            timeout: Maximum wait time in seconds

        Returns:
            Telegram response string or None if timeout/no response
        """
        start_time = time.time()
        last_update_id: Optional[int] = None

        # Mark menu as active for coordination
        self._mark_menu_active()

        try:
            while time.time() - start_time < timeout:
                # Check if terminal already responded
                if self._is_response_handled():
                    existing_response = self._get_existing_response()
                    if (
                        existing_response
                        and existing_response.get("source") == "terminal"
                    ):
                        # Terminal won the race, cleanup Telegram UI
                        self._cleanup_telegram_menu()
                        return None
                    elif existing_response:
                        return existing_response.get("response")

                # Check for Telegram response
                telegram_response = self._check_telegram_updates(last_update_id)
                if telegram_response:
                    response, last_update_id = telegram_response
                    if self._try_claim_response():
                        self._write_response(response, "telegram")
                        return response

                # Small delay to prevent busy waiting
                await asyncio.sleep(0.5)

        finally:
            # Cleanup when done
            self._cleanup_menu_state()

        return None

    def _check_telegram_updates(
        self, last_update_id: Optional[int]
    ) -> Optional[tuple[str, int]]:
        """Check for Telegram callback responses"""
        if not TG_TOKEN or not TG_CHAT:
            return None

        try:
            # Get updates from Telegram
            updates = self._telegram_request(
                "getUpdates",
                offset=(last_update_id or 0) + 1,
                timeout=1,  # Short timeout for non-blocking
            )["result"]

            # Find callback query responses
            for update in updates:
                update_id = update["update_id"]

                if "callback_query" in update:
                    callback = update["callback_query"]
                    response = callback["data"]

                    # Acknowledge the callback
                    self._telegram_request(
                        "answerCallbackQuery", callback_query_id=callback["id"]
                    )

                    # Update the original message
                    if "message" in callback:
                        msg_id = callback["message"]["message_id"]
                        self._telegram_request(
                            "editMessageText",
                            chat_id=TG_CHAT,
                            message_id=msg_id,
                            text=f"{callback['message']['text']}\n\n*Selected:* {response}",
                            parse_mode="Markdown",
                        )

                    return response, update_id

            # Return the last update ID even if no callback found
            if updates:
                return None, updates[-1]["update_id"]

        except Exception as e:
            print(f"Error checking Telegram updates: {e}", file=sys.stderr)

        return None

    def _telegram_request(self, method: str, **params) -> Dict[str, Any]:
        """Make a Telegram API request"""
        url = f"https://api.telegram.org/bot{TG_TOKEN}/{method}"
        data = json.dumps(params).encode()
        req = urllib.request.Request(
            url, data=data, headers={"Content-Type": "application/json"}
        )

        with urllib.request.urlopen(req, timeout=10) as resp:
            return json.loads(resp.read().decode())

    def _write_response(self, response: str, source: str) -> None:
        """Write the response to a file for coordination"""
        response_data = {
            "response": response,
            "source": source,
            "timestamp": time.time(),
            "pid": os.getpid(),
        }

        try:
            with open(self.response_file, "w") as f:
                json.dump(response_data, f, indent=2)
        except Exception as e:
            print(f"Error writing response: {e}", file=sys.stderr)

    def _try_claim_response(self) -> bool:
        """
        Atomically try to claim the right to handle the response

        Returns:
            True if this process successfully claimed the response, False otherwise
        """
        try:
            # Use file creation as atomic operation for claiming
            with open(self.response_lock_file, "x") as f:
                f.write(f"{os.getpid()}\n{time.time()}")
            return True
        except FileExistsError:
            # Another process already claimed it
            return False
        except Exception as e:
            print(f"Error claiming response: {e}", file=sys.stderr)
            return False

    def _is_response_handled(self) -> bool:
        """Check if a response has already been handled"""
        return self.response_lock_file.exists() or self.response_file.exists()

    def _get_existing_response(self) -> Optional[Dict]:
        """Get existing response if available"""
        try:
            if self.response_file.exists():
                with open(self.response_file) as f:
                    return json.load(f)
        except Exception as e:
            print(f"Error reading existing response: {e}", file=sys.stderr)
        return None

    def _mark_menu_active(self) -> None:
        """Mark that a menu is currently active"""
        try:
            menu_data = {
                "active": True,
                "timestamp": time.time(),
                "pid": os.getpid(),
            }
            with open(self.active_menu_file, "w") as f:
                json.dump(menu_data, f, indent=2)
        except Exception as e:
            print(f"Error marking menu active: {e}", file=sys.stderr)

    def _cleanup_menu_state(self) -> None:
        """Clean up menu coordination state"""
        for file_path in [self.response_lock_file, self.active_menu_file]:
            try:
                if file_path.exists():
                    file_path.unlink()
            except Exception as e:
                print(f"Error cleaning up {file_path}: {e}", file=sys.stderr)

    def _cleanup_telegram_menu(self) -> None:
        """Send cleanup signal to Telegram to remove menu buttons"""
        if not TG_TOKEN or not TG_CHAT:
            return

        try:
            # Send a message to indicate terminal response was received
            self._telegram_request(
                "sendMessage",
                chat_id=TG_CHAT,
                text="✅ *Response received via terminal* - Telegram menu disabled",
                parse_mode="Markdown",
                disable_notification=True,
            )
        except Exception as e:
            print(f"Error cleaning up Telegram menu: {e}", file=sys.stderr)

    def load_menu_state(self) -> Optional[Dict]:
        """Load current menu state from file"""
        try:
            if self.menu_state_file.exists():
                with open(self.menu_state_file) as f:
                    return json.load(f)
        except Exception as e:
            print(f"Error loading menu state: {e}", file=sys.stderr)
        return None

    def load_notification_state(self) -> Optional[Dict]:
        """Load current notification state from file"""
        try:
            if self.notification_state_file.exists():
                with open(self.notification_state_file) as f:
                    return json.load(f)
        except Exception as e:
            print(f"Error loading notification state: {e}", file=sys.stderr)
        return None

    def clear_state_files(self) -> None:
        """Clear all state files after processing"""
        for file_path in [
            self.menu_state_file,
            self.notification_state_file,
            self.response_file,
            self.response_lock_file,
            self.active_menu_file,
        ]:
            try:
                if file_path.exists():
                    file_path.unlink()
            except Exception as e:
                print(f"Error clearing {file_path}: {e}", file=sys.stderr)


def main():
    """Main entry point for testing the coordinator"""
    coordinator = MenuCoordinator()

    print("Waiting for user response...")
    response = coordinator.wait_for_response(timeout=60.0)

    if response:
        print(f"Received response: {response}")
    else:
        print("No response received (timeout)")

    coordinator.clear_state_files()


if __name__ == "__main__":
    main()
