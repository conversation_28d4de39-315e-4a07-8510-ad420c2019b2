#!/usr/bin/env python3
"""
menu_detector.py - Hook-based menu detection for Claude Code prompts
Detects and relays menu options to Telegram using Claude Code hooks
"""

import json
import os
import re
import sys
from pathlib import Path
from typing import Dict, List, Tuple

# Add the parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))

try:
    from telegram_bot import TelegramBot
except ImportError:
    # Fallback if telegram_bot is not available
    TelegramBot = None


# Check if RemoCode is active by looking for the process marker
def _is_remocode_active() -> bool:
    """Check if RemoCode is currently active by looking for the process marker"""
    try:
        marker_file = Path.home() / ".remocode" / "active.marker"
        if not marker_file.exists():
            return False

        # Check if the marker is from a currently running process
        with open(marker_file) as f:
            marker_data = json.load(f)

        pid = marker_data.get("pid")
        if pid:
            # Check if process is still running
            try:
                os.kill(pid, 0)  # Signal 0 checks if process exists
                return True
            except (OSError, ProcessLookupError):
                # Process no longer exists, clean up stale marker
                marker_file.unlink()
                return False

    except Exception:
        # If anything goes wrong, assume RemoCode is not active
        pass

    return False


REMOCODE_ENABLED = _is_remocode_active()

STATE_DIR = Path(__file__).parent.parent / "state"
STATE_DIR.mkdir(exist_ok=True)

# Configurable log path
LOG_PATH = os.getenv(
    "REMOCODE_LOG_PATH",
    str(Path(__file__).parent.parent.parent.parent / "logs" / "remocode.log"),
)


class MenuType:
    """Menu type constants"""

    NUMBERED_OPTIONS = "numbered_options"
    YES_NO_PROMPT = "yes_no_prompt"
    INTERACTIVE_PROMPT = "interactive_prompt"
    PLAN_APPROVAL = "plan_approval"
    UNKNOWN = "unknown"


class MenuOption:
    """Represents a menu option"""

    def __init__(self, key: str, text: str, description: str = ""):
        self.key = key
        self.text = text
        self.description = description

    def to_dict(self) -> Dict:
        return {"key": self.key, "text": self.text, "description": self.description}


class HookMenuDetector:
    """Hook-based menu detector that processes Claude Code prompts"""

    def __init__(self):
        # Initialize TelegramBot only if we have the required environment variables
        if TelegramBot and os.getenv("TG_TOKEN") and os.getenv("TG_CHAT"):
            try:
                self.telegram_bot = TelegramBot(
                    os.getenv("TG_TOKEN"), os.getenv("TG_CHAT")
                )
            except Exception:
                self.telegram_bot = None
        else:
            self.telegram_bot = None

    def detect_menu_type_and_options(self, prompt: str) -> Tuple[str, List[MenuOption]]:
        """
        Detect menu type and extract options from a Claude Code prompt with improved precision

        Returns:
            Tuple of (menu_type, list_of_options)
        """
        prompt_lower = prompt.lower()

        # First check: Must have actual numbered options or specific menu indicators
        # This prevents false positives on descriptive text
        if not self._has_actual_menu_structure(prompt):
            return MenuType.UNKNOWN, []

        # Plan approval detection - more specific patterns
        if any(
            phrase in prompt_lower
            for phrase in [
                "proceed with the plan?",
                "approve the plan?",
                "confirm the plan?",
                "would you like me to proceed?",
                "would you like to proceed?",
                "shall i proceed?",
                "should i proceed?",
                "ready to proceed?",
                "implement the plan?",
                "execute the plan?",
            ]
        ) and ("1." in prompt or "2." in prompt or "❯" in prompt):
            return self._detect_plan_approval(prompt)

        # Interactive prompt detection - require specific Claude Code menu structure
        if any(
            phrase in prompt_lower
            for phrase in [
                "auto-accept edits",
                "manually approve edits",
                "keep planning",
            ]
        ) and ("1." in prompt or "2." in prompt or "❯" in prompt):
            return self._detect_interactive_prompt(prompt)

        # Yes/No detection - require explicit y/n format
        if any(
            phrase in prompt_lower
            for phrase in [
                "(y/n)",
                "yes or no",
                "confirm (y/n)",
                "proceed? (y/n)",
                "continue? (y/n)",
            ]
        ) or (("y/n" in prompt_lower or "yes/no" in prompt_lower) and "?" in prompt):
            return self._detect_yes_no_prompt(prompt)

        # Numbered options detection
        numbered_options = self._detect_numbered_options(prompt)
        if numbered_options:
            return MenuType.NUMBERED_OPTIONS, numbered_options

        return MenuType.UNKNOWN, []

    def _has_actual_menu_structure(self, prompt: str) -> bool:
        """Check if prompt has actual menu structure, not just descriptive text"""
        # Must have numbered options (1., 2., 3.) or arrow indicator (❯)
        has_numbered_options = bool(re.search(r"\n\s*\d+\.\s+", prompt))
        has_arrow_indicator = "❯" in prompt
        has_explicit_choice = any(
            word in prompt.lower() for word in ["choose", "select", "option"]
        )

        # Exclude common false positives
        false_positive_indicators = [
            "successfully",
            "completed",
            "updated",
            "created",
            "perfect!",
            "done!",
            "finished",
            "great!",
        ]

        has_false_positive = any(
            indicator in prompt.lower() for indicator in false_positive_indicators
        )

        # Must have menu structure AND not be a false positive
        return (
            has_numbered_options or has_arrow_indicator or has_explicit_choice
        ) and not has_false_positive

    def _detect_plan_approval(self, prompt: str) -> Tuple[str, List[MenuOption]]:
        """Detect plan approval options"""
        options = [
            MenuOption(
                "1",
                "Yes, proceed with the plan",
                "Continue with the proposed implementation",
            ),
            MenuOption("2", "Modify the plan", "Request changes to the plan"),
            MenuOption("3", "No, cancel", "Cancel the plan and stop"),
        ]
        return MenuType.PLAN_APPROVAL, options

    def _detect_interactive_prompt(self, prompt: str) -> Tuple[str, List[MenuOption]]:
        """Detect interactive prompt options"""
        options = [
            MenuOption(
                "1",
                "Yes, and auto-accept edits",
                "Proceed and automatically accept all file changes",
            ),
            MenuOption(
                "2",
                "Yes, and manually approve edits",
                "Proceed but review each file change",
            ),
            MenuOption("3", "No, keep planning", "Continue planning without execution"),
        ]
        return MenuType.INTERACTIVE_PROMPT, options

    def _detect_yes_no_prompt(self, prompt: str) -> Tuple[str, List[MenuOption]]:
        """Detect yes/no prompts"""
        options = [
            MenuOption("y", "Yes", "Confirm the action"),
            MenuOption("n", "No", "Cancel the action"),
        ]
        return MenuType.YES_NO_PROMPT, options

    def _detect_numbered_options(self, prompt: str) -> List[MenuOption]:
        """Detect numbered menu options in the prompt"""
        options = []

        # Pattern to match numbered options like "1. Option text" or "1) Option text"
        patterns = [
            r"(\d+)\.\s+(.+?)(?=\n\d+\.|\n\n|\Z)",  # "1. Option"
            r"(\d+)\)\s+(.+?)(?=\n\d+\)|\n\n|\Z)",  # "1) Option"
        ]

        for pattern in patterns:
            matches = re.findall(pattern, prompt, re.MULTILINE | re.DOTALL)
            if matches:
                for num, text in matches:
                    clean_text = text.strip().replace("\n", " ")
                    options.append(MenuOption(num, clean_text, f"Option {num}"))
                break

        return options

    async def relay_menu_to_telegram(
        self, menu_type: str, options: List[MenuOption], prompt: str
    ) -> bool:
        """Send menu options to Telegram"""
        if not self.telegram_bot:
            # Write to state file for v2 polling fallback
            self._write_menu_state(menu_type, options, prompt)
            return True

        try:
            # Create Telegram keyboard based on menu type
            keyboard = self._create_telegram_keyboard(menu_type, options)

            # Send menu to Telegram
            message = f"🤖 Claude Code Menu:\n\n{prompt}\n\nSelect an option:"

            await self.telegram_bot.send_menu_prompt(message, keyboard)

            # Also write to state file for coordination
            self._write_menu_state(menu_type, options, prompt)

            return True
        except Exception as e:
            print(f"Error sending to Telegram: {e}", file=sys.stderr)
            # Fallback to state file
            self._write_menu_state(menu_type, options, prompt)
            return False

    def _create_telegram_keyboard(
        self, menu_type: str, options: List[MenuOption]
    ) -> Dict:
        """Create Telegram inline keyboard from menu options"""
        if menu_type == MenuType.YES_NO_PROMPT:
            return {
                "inline_keyboard": [
                    [
                        {"text": "✅ Yes", "callback_data": "y"},
                        {"text": "⛔️ No", "callback_data": "n"},
                    ]
                ]
            }
        else:
            # Multi-option keyboard
            buttons = []
            for option in options:
                buttons.append(
                    [
                        {
                            "text": f"{option.key}. {option.text[:50]}...",
                            "callback_data": option.key,
                        }
                    ]
                )

            return {"inline_keyboard": buttons}

    def _write_menu_state(
        self, menu_type: str, options: List[MenuOption], prompt: str
    ) -> None:
        """Write menu state to file for coordination with other processes"""
        state = {
            "event": "menu_detected",
            "menu_type": menu_type,
            "prompt": prompt,
            "options": [opt.to_dict() for opt in options],
            "timestamp": str(Path().cwd() / ".remocode_menu.json"),
        }

        try:
            with open(STATE_DIR / "menu.json", "w") as f:
                json.dump(state, f, indent=2)
        except Exception as e:
            print(f"Error writing menu state: {e}", file=sys.stderr)


def main():
    """Main entry point for the hook"""
    # Early exit if remocode is not enabled - make hook transparent
    if not REMOCODE_ENABLED:
        print("No menu detected in prompt - RemoCode disabled")
        return

    if len(sys.argv) < 2:
        print("Usage: menu_detector.py <prompt>", file=sys.stderr)
        sys.exit(1)

    prompt = sys.argv[1]
    detector = HookMenuDetector()

    # Detect menu type and options
    menu_type, options = detector.detect_menu_type_and_options(prompt)

    if menu_type != MenuType.UNKNOWN and options:
        # Menu detected - relay to Telegram
        import asyncio

        success = asyncio.run(
            detector.relay_menu_to_telegram(menu_type, options, prompt)
        )

        if success:
            print(f"Menu detected and relayed: {menu_type} with {len(options)} options")
        else:
            print(f"Menu detected but relay failed: {menu_type}", file=sys.stderr)
            sys.exit(1)
    else:
        # No menu detected - normal prompt
        print("No menu detected in prompt")


if __name__ == "__main__":
    main()
