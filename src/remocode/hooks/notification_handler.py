#!/usr/bin/env python3
"""
notification_handler.py - <PERSON>le Claude Code notification events
Processes notification events when <PERSON> waits for user input
"""

import json
import os
import sys
import time
import urllib.parse
import urllib.request
from pathlib import Path
from typing import Any, Dict, Optional

from .io_utils import read_terminal_line  # NEW IMPORT


# Check if RemoCode is active by looking for the process marker
def _is_remocode_active() -> bool:
    """Check if RemoCode is currently active by looking for the process marker"""
    try:
        marker_file = Path.home() / ".remocode" / "active.marker"
        if not marker_file.exists():
            return False

        # Check if the marker is from a currently running process
        with open(marker_file) as f:
            marker_data = json.load(f)

        pid = marker_data.get("pid")
        if pid:
            # Check if process is still running
            try:
                os.kill(pid, 0)  # Signal 0 checks if process exists
                return True
            except (OSError, ProcessLookupError):
                # Process no longer exists, clean up stale marker
                marker_file.unlink()
                return False

    except Exception:
        # If anything goes wrong, assume RemoCode is not active
        pass

    return False


REMOCODE_ENABLED = _is_remocode_active()

TG_TOKEN = os.getenv("TG_TOKEN")
TG_CHAT = os.getenv("TG_CHAT")

# Centralised runtime dir for all hook state
STATE_DIR = Path(__file__).parent.parent / "state"
STATE_DIR.mkdir(exist_ok=True)

# Configurable log path
LOG_PATH = os.getenv(
    "REMOCODE_LOG_PATH",
    str(Path(__file__).parent.parent.parent.parent / "logs" / "remocode.log"),
)


def _telegram(method: str, **params: Any) -> Dict[str, Any]:
    """Lightweight POST using stdlib to avoid external deps."""
    url = f"https://api.telegram.org/bot{TG_TOKEN}/{method}"
    data = json.dumps(params).encode()
    req = urllib.request.Request(
        url, data=data, headers={"Content-Type": "application/json"}
    )
    with urllib.request.urlopen(req, timeout=10) as resp:
        return json.loads(resp.read().decode())


def _wait_for_text_reply(reply_to_message_id: int, timeout: int = 300) -> Optional[str]:
    """Wait for a text reply to a specific message"""
    start_time = time.time()
    last_update_id: Optional[int] = None

    while time.time() - start_time < timeout:
        try:
            updates = _telegram(
                "getUpdates",
                offset=(last_update_id or 0) + 1,
                timeout=2,
            )["result"]

            for upd in updates:
                last_update_id = upd["update_id"]

                # Check for text message replying to our message
                if (
                    "message" in upd
                    and upd["message"].get("reply_to_message", {}).get("message_id")
                    == reply_to_message_id
                ):
                    text = upd["message"].get("text", "").strip()
                    if text:
                        return text

        except Exception as e:
            print(f"Error waiting for text reply: {e}", file=sys.stderr)
            time.sleep(1)

        time.sleep(0.5)

    return None


def _echo_telegram_input_to_terminal(input_text: str, source: str = "Telegram"):
    """Echo Telegram input to terminal for bidirectional visibility"""
    # Write to stderr so it appears in terminal but doesn't interfere with Claude's stdin
    echo_message = f"\n📱 [{source}] {input_text}\n"
    print(echo_message, file=sys.stderr, flush=True)

    # Also log to RemoCode log file for debugging
    try:
        with open(REMOCODE_LOG_PATH, "a") as f:
            timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
            f.write(f"{timestamp} - [BIDIRECTIONAL] {source} input: {input_text}\n")
    except Exception:
        pass  # Don't fail if logging doesn't work


def _send_telegram_menu(
    message: str, keyboard: Dict, raw_data: Optional[Dict] = None, timeout: int = 300
) -> bool:
    """
    Send enhanced menu to Telegram with debugging info and improved response handling

    Args:
        message: Human-readable message
        keyboard: Telegram inline keyboard
        raw_data: Optional raw JSON data for debugging
        timeout: Response timeout in seconds
    """
    if not TG_TOKEN or not TG_CHAT:
        return False

    try:
        # Enhance message with debugging info if provided
        enhanced_message = message
        if raw_data:
            # Add collapsible raw JSON section
            json_str = json.dumps(raw_data, indent=2)
            if len(json_str) > 3800:
                # Telegram message max length ~4096, send as separate document when too big
                tmp_path = "/tmp/claude_payload.json"
                with open(tmp_path, "w") as _tmp:
                    _tmp.write(json_str)

                enhanced_message += (
                    "\n\n🔧 **Debug Info:** attached as document (payload.json)"
                )
                _telegram(
                    "sendDocument",
                    chat_id=TG_CHAT,
                    document=open(tmp_path, "rb"),
                    caption="payload.json",
                )
            else:
                enhanced_message += (
                    f"\n\n🔧 **Debug Info (raw JSON)**:\n```json\n{json_str}\n```"
                )

        # Add timestamp and session info
        import datetime

        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        enhanced_message += f"\n\n⏰ {timestamp}"

        sent = _telegram(
            "sendMessage",
            chat_id=TG_CHAT,
            text=enhanced_message,
            parse_mode="Markdown",
            reply_markup=keyboard,
        )
        msg_id = sent["result"]["message_id"]

        # Send a separate "waiting for response" message
        waiting_msg = _telegram(
            "sendMessage",
            chat_id=TG_CHAT,
            text="⏳ Waiting for your response...",
            parse_mode="Markdown",
        )
        waiting_msg_id = waiting_msg["result"]["message_id"]

        # --- Race: Telegram callback *or* terminal input ---
        last_update_id: int | None = None
        start = time.time()

        while time.time() - start < timeout:
            # 1. Poll Telegram updates (non-blocking with short timeout)
            updates = _telegram(
                "getUpdates",
                offset=(last_update_id or 0) + 1,
                timeout=1,
            )["result"]

            for upd in updates:
                last_update_id = upd["update_id"]

                if (
                    "callback_query" in upd
                    and upd["callback_query"]["message"]["message_id"] == msg_id
                ):
                    choice = upd["callback_query"]["data"]

                    # Acknowledge tap
                    _telegram(
                        "answerCallbackQuery",
                        callback_query_id=upd["callback_query"]["id"],
                    )

                    # Special handling for text input request
                    if choice == "send_message":
                        # Update message to request text input
                        _telegram(
                            "editMessageText",
                            chat_id=TG_CHAT,
                            message_id=msg_id,
                            text=f"{enhanced_message}\n\n💬 **Please reply to this message with your text:**",
                            parse_mode="Markdown",
                        )

                        # Wait for text reply
                        text_input = _wait_for_text_reply(msg_id, timeout=300)
                        if text_input:
                            # Update message with the input
                            _telegram(
                                "editMessageText",
                                chat_id=TG_CHAT,
                                message_id=msg_id,
                                text=f"{enhanced_message}\n\n💬 **Your message:** {text_input}",
                                parse_mode="Markdown",
                            )

                            try:
                                _telegram(
                                    "deleteMessage",
                                    chat_id=TG_CHAT,
                                    message_id=waiting_msg_id,
                                )
                            except Exception:
                                pass

                            # Echo to terminal for bidirectional visibility
                            _echo_telegram_input_to_terminal(
                                text_input, "Telegram Text Input"
                            )

                            print(text_input, flush=True)  # Send to Claude
                            (STATE_DIR / "response.txt").write_text(text_input)
                            return True
                        else:
                            # Timeout - treat as stop
                            choice = "stop"

                    # Regular button handling
                    _telegram(
                        "editMessageText",
                        chat_id=TG_CHAT,
                        message_id=msg_id,
                        text=f"{enhanced_message}\n\n✅ **Selected:** {choice}",
                        parse_mode="Markdown",
                    )

                    try:
                        _telegram(
                            "deleteMessage", chat_id=TG_CHAT, message_id=waiting_msg_id
                        )
                    except Exception:
                        pass

                    # Echo to terminal for bidirectional visibility
                    _echo_telegram_input_to_terminal(choice, "Telegram Button")

                    print(choice, flush=True)  # <-- Feed Claude via stdout
                    (STATE_DIR / "response.txt").write_text(choice)
                    return True

                # Handle textual replies (reply-to)
                if (
                    "message" in upd
                    and upd["message"].get("reply_to_message", {}).get("message_id")
                    == msg_id
                ):
                    choice = upd["message"].get("text", "").strip()

                    _telegram(
                        "editMessageText",
                        chat_id=TG_CHAT,
                        message_id=msg_id,
                        text=f"{enhanced_message}\n\n💬 **Reply:** {choice}",
                        parse_mode="Markdown",
                    )

                    try:
                        _telegram(
                            "deleteMessage", chat_id=TG_CHAT, message_id=waiting_msg_id
                        )
                    except Exception:
                        pass

                    # Echo to terminal for bidirectional visibility
                    _echo_telegram_input_to_terminal(choice, "Telegram Reply")

                    print(choice, flush=True)
                    (STATE_DIR / "response.txt").write_text(choice)
                    return True

            # 2. Poll local terminal (non-blocking)
            term_line = read_terminal_line(0.1)
            if term_line:
                choice = term_line.split()[0]

                _telegram(
                    "editMessageText",
                    chat_id=TG_CHAT,
                    message_id=msg_id,
                    text=f"{enhanced_message}\n\n🖥 **Terminal:** {choice}",
                    parse_mode="Markdown",
                )

                try:
                    _telegram(
                        "deleteMessage", chat_id=TG_CHAT, message_id=waiting_msg_id
                    )
                except Exception:
                    pass

                # Echo to terminal for bidirectional visibility
                _echo_telegram_input_to_terminal(choice, "Terminal Input")

                print(choice, flush=True)
                return True

        # Timeout handling
        try:
            _telegram("deleteMessage", chat_id=TG_CHAT, message_id=waiting_msg_id)
        except Exception:
            pass

        _telegram(
            "editMessageText",
            chat_id=TG_CHAT,
            message_id=msg_id,
            text=f"{enhanced_message}\n\n⏰ **Timed out** ({timeout}s) - Claude Code will continue with default behavior",
            parse_mode="Markdown",
        )
        return False

    except Exception as e:
        print(f"Telegram error: {e}", file=sys.stderr)
        return False


class NotificationHandler:
    """Handle Claude Code notification events"""

    def __init__(self) -> None:
        self.raw_input_data = None  # Store raw input for debugging

    def handle_notification(
        self, notification_type: str, message: str, raw_data: Optional[Dict] = None
    ) -> bool:
        """
        Handle different types of Claude Code notifications

        According to Claude Code docs, notifications are sent when:
        1. Claude needs permission to use a tool
        2. The prompt input has been idle for at least 60 seconds

        Args:
            notification_type: Type of notification
            message: Notification message content
        """
        try:
            # Store raw data for debugging
            self.raw_input_data = raw_data

            # Write notification state for coordination
            self._write_notification_state(notification_type, message)

            # Analyze the message to determine the type of notification
            message_lower = message.lower()

            # Check for permission requests
            if any(
                phrase in message_lower
                for phrase in [
                    "needs your permission",
                    "permission to use",
                    "allow claude to",
                    "approve this action",
                ]
            ):
                return self._handle_permission_request(message)

            # Check for idle notifications
            elif any(
                phrase in message_lower
                for phrase in ["waiting for your input", "idle for", "no activity"]
            ):
                return self._handle_idle_notification(message)

            # Check for plan approval menus
            elif self._is_plan_approval_menu(message):
                return self._handle_plan_approval_menu(message)

            # Check for other interactive prompts
            elif any(
                phrase in message_lower
                for phrase in [
                    "would you like to proceed",
                    "choose an option",
                    "select:",
                    "(y/n)",
                    "1)",
                    "2)",
                    "3)",
                ]
            ):
                return self._handle_interactive_prompt(message)

            else:
                # Generic notification - just inform user
                return self._handle_generic_notification(notification_type, message)

        except Exception as e:
            print(f"Error handling notification: {e}", file=sys.stderr)
            return False

    def _handle_permission_request(self, message: str) -> bool:
        """Handle genuine permission requests from Claude Code"""
        # Determine option list
        choices = []
        if self.raw_input_data and isinstance(self.raw_input_data, dict):
            choices = [str(c) for c in self.raw_input_data.get("choices", []) if c]

        if not choices:
            # fallback yes/no
            choices = ["allow", "deny", "always_allow"]

        # Build keyboard dynamically
        keyboard = {
            "inline_keyboard": [
                [{"text": str(c), "callback_data": str(c)}] for c in choices
            ]
        }

        # Human-readable options list
        opts_list = "\n".join([f"{i+1}. {c}" for i, c in enumerate(choices)])

        notification_text = (
            f"🔒 **Claude needs permission**\n"
            f"\n{message}"
            f"\n\n**Options:**\n{opts_list}"
        )

        return _send_telegram_menu(
            notification_text, keyboard, self.raw_input_data, timeout=300
        )

    def _handle_interactive_prompt(self, message: str) -> bool:
        """Handle interactive prompts that need user selection"""
        # Try to extract numbered options from the message
        options = self._extract_options_from_message(message)

        if options:
            keyboard = {
                "inline_keyboard": [
                    [{"text": f"{i}. {opt[:30]}...", "callback_data": str(i)}]
                    for i, opt in enumerate(options, 1)
                ]
            }
        else:
            # Default yes/no keyboard
            keyboard = {
                "inline_keyboard": [
                    [
                        {"text": "✅ Yes", "callback_data": "y"},
                        {"text": "⛔️ No", "callback_data": "n"},
                    ]
                ]
            }

        notification_text = (
            f"🤖 **Claude Code Prompt**\n\n{message}\n\nPlease select an option:"
        )
        return _send_telegram_menu(
            notification_text, keyboard, self.raw_input_data, timeout=180
        )

    def _extract_options_from_message(self, message: str) -> list[str]:
        """Extract numbered options from a message"""
        import re

        options = []

        # Look for patterns like "1) Option text" or "1. Option text"
        patterns = [
            r"(\d+)\)\s*(.+?)(?=\n\d+\)|\n\n|\Z)",
            r"(\d+)\.\s*(.+?)(?=\n\d+\.|\n\n|\Z)",
        ]

        for pattern in patterns:
            matches = re.findall(pattern, message, re.MULTILINE | re.DOTALL)
            if matches:
                options = [text.strip().replace("\n", " ") for _, text in matches]
                break

        return options

    def _handle_tool_permission(self, message: str) -> bool:
        """Handle tool permission requests"""
        keyboard = {
            "inline_keyboard": [
                [
                    {"text": "✅ Allow", "callback_data": "allow"},
                    {"text": "⛔️ Deny", "callback_data": "deny"},
                ]
            ]
        }

        notification_text = (
            f"🔒 Tool Permission Request:\n\n{message}\n\nAllow Claude to use this tool?"
        )
        return _send_telegram_menu(notification_text, keyboard)

    def _handle_idle_notification(self, message: str) -> bool:
        """Handle idle timeout notifications with text input support"""
        keyboard = {
            "inline_keyboard": [
                [
                    {"text": "💬 Send Message", "callback_data": "send_message"},
                    {"text": "🛑 Stop", "callback_data": "stop"},
                ]
            ]
        }

        notification_text = (
            f"⏱️ Claude Code Idle:\n\n{message}\n\n"
            f"Click 'Send Message' to type your next request, or 'Stop' to end the session."
        )
        return _send_telegram_menu(
            notification_text, keyboard, self.raw_input_data, timeout=300
        )

    def _is_plan_approval_menu(self, message: str) -> bool:
        """Check if message is a plan approval menu"""
        message_lower = message.lower()
        return any(
            phrase in message_lower
            for phrase in [
                "would you like to proceed",
                "ready to code",
                "auto-accept edits",
                "manually approve edits",
                "keep planning",
                "here is claude's plan",
                "proceed with the plan",
            ]
        )

    def _handle_plan_approval_menu(self, message: str) -> bool:
        """Handle plan approval menu with complete options matching Claude Code exactly"""
        # Format the message without truncation for plan approval
        # Plan approval is critical - user needs to see the complete plan
        clean_message = message.strip()

        # Only truncate if extremely long (over 3000 chars) to respect Telegram limits
        if len(clean_message) > 3000:
            # Find a good break point (end of sentence or paragraph)
            truncate_at = 2800
            for i in range(2800, 2500, -1):
                if clean_message[i] in ".!?\n":
                    truncate_at = i + 1
                    break
            clean_message = (
                clean_message[:truncate_at]
                + "\n\n📋 **[Plan content truncated - see terminal for complete details]**"
            )

        notification_text = (
            f"📝 **CLAUDE CODE PLAN APPROVAL**\n\n"
            f"{clean_message}\n\n"
            f"⚠️ **This will begin making actual changes to your codebase!**\n\n"
            f"**Choose your approval option:**\n"
            f"• **Option 1**: Auto-accept all edits (fastest)\n"
            f"• **Option 2**: Manually approve each edit (safest)\n"
            f"• **Option 3**: Return to planning mode\n\n"
            f"🖥️ **First response wins** - you can also respond in terminal"
        )

        # Create keyboard with exact Claude Code options
        keyboard = {
            "inline_keyboard": [
                [{"text": "1️⃣ Yes, and auto-accept edits", "callback_data": "1"}],
                [{"text": "2️⃣ Yes, and manually approve edits", "callback_data": "2"}],
                [{"text": "3️⃣ No, keep planning", "callback_data": "3"}],
            ]
        }

        # Use the existing _send_telegram_menu function which handles responses properly
        return _send_telegram_menu(
            notification_text, keyboard, self.raw_input_data, timeout=300
        )

    def _handle_generic_notification(
        self, notification_type: str, message: str
    ) -> bool:
        """Handle other notification types"""
        try:
            # Improve message formatting
            if not message or message.strip() == "":
                formatted_message = f"🔔 Claude Code Notification\n\n**Type:** {notification_type}\n**Message:** *(empty)*"
            else:
                formatted_message = f"🔔 Claude Code Notification\n\n**Type:** {notification_type}\n**Message:** {message}"

            # Append raw JSON if we captured any
            if self.raw_input_data:
                json_text = json.dumps(self.raw_input_data, indent=2)
                if len(json_text) <= 3800:
                    formatted_message += f"\n\n```json\n{json_text}\n```"
                else:
                    tmp_path = "/tmp/notification_payload.json"
                    with open(tmp_path, "w") as _tmp:
                        _tmp.write(json_text)
                    _telegram(
                        "sendDocument",
                        chat_id=TG_CHAT,
                        document=open(tmp_path, "rb"),
                        caption="notification_payload.json",
                    )

            _telegram(
                "sendMessage",
                chat_id=TG_CHAT,
                text=formatted_message,
                parse_mode="Markdown",
            )
            return True
        except Exception as e:
            print(f"Failed to send generic notification: {e}", file=sys.stderr)
            return False

    def _write_notification_state(self, notification_type: str, message: str) -> None:
        """Write notification state to file"""
        state = {
            "event": "notification",
            "type": notification_type,
            "message": message,
            "timestamp": str(Path().cwd() / ".remocode_notification.json"),
        }

        try:
            with open(STATE_DIR / "notification.json", "w") as f:
                json.dump(state, f, indent=2)
        except Exception as e:
            print(f"Error writing notification state: {e}", file=sys.stderr)


def main() -> None:
    """Main entry point for the notification hook"""
    # Early exit if remocode is not enabled - make hook transparent
    if not REMOCODE_ENABLED:
        print("Notification hook disabled - RemoCode not active")
        return

    try:
        # According to Claude Code documentation, Notification hooks receive JSON via stdin
        # The command line arguments are just the environment variable expansions

        # Try to read JSON from stdin first (proper Claude Code hook format)
        try:
            input_data = json.load(sys.stdin)
            hook_event_name = input_data.get("hook_event_name", "")
            message = input_data.get("message", "")
            session_id = input_data.get("session_id", "")

            # Debug logging
            with open(LOG_PATH, "a") as f:
                f.write(
                    f"[notification_handler] JSON input: {json.dumps(input_data, indent=2)}\n"
                )

            # Validate this is a Notification hook
            if hook_event_name != "Notification":
                print(f"Not a Notification hook: {hook_event_name}")
                return

        except (json.JSONDecodeError, EOFError):
            # Fallback to command line arguments (legacy mode)
            with open(LOG_PATH, "a") as f:
                f.write(f"[notification_handler] Fallback to args: {sys.argv}\n")

            if len(sys.argv) < 2:
                print("No JSON input and insufficient arguments", file=sys.stderr)
                return

            # Use command line arguments as fallback
            notification_type = sys.argv[1] if len(sys.argv) > 1 else "unknown"
            message = sys.argv[2] if len(sys.argv) > 2 else ""
            session_id = ""

        # Skip empty messages
        if not message or message.strip() == "":
            print("Empty notification message - skipping")
            return

        # Handle the notification
        handler = NotificationHandler()
        success = handler.handle_notification(
            "notification", message, input_data if "input_data" in locals() else None
        )

        if success:
            print("Notification handled successfully")
        else:
            print("Failed to handle notification", file=sys.stderr)

    except Exception as e:
        with open(LOG_PATH, "a") as f:
            f.write(f"[notification_handler] ERROR: {e}\n")
        print(f"Notification handler error: {e}", file=sys.stderr)


if __name__ == "__main__":
    main()
