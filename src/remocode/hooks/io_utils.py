#!/usr/bin/env python3
"""
io_utils.py – small helpers shared by RemoCode v2 hooks.
Provides non-blocking read from the controlling TTY so a background hook can
race terminal input with Telegram callbacks.
"""

from __future__ import annotations

import os
import select
from typing import Optional


def read_terminal_line(timeout: float = 0.1) -> Optional[str]:
    """Return a trimmed line typed in the *Claude Code* terminal.

    If no real TTY is attached or nothing has been typed within *timeout*
    seconds, returns ``None``.  Works on Unix/macOS.  Windows support can be
    added with ``msvcrt`` if needed.
    """
    try:
        fd = os.open("/dev/tty", os.O_RDONLY | os.O_NONBLOCK)
    except OSError:
        # No controlling terminal (e.g. CI run).
        return None

    ready, _, _ = select.select([fd], [], [], timeout)
    if ready:
        data = os.read(fd, 4096).decode(errors="ignore").strip()
        return data if data else None
    return None
