#!/usr/bin/env python3
"""
pretool_guard.py – RemoCode v2 hook

Called by <PERSON> *before* each tool invocation.
Decides automatically or asks user via Telegram.
"""

from __future__ import annotations

import json
import os
import re
import sys
import time
import urllib.parse
import urllib.request
from pathlib import Path
from typing import Any, Dict, List, Optional


# Check if RemoCode is active by looking for the process marker.
# This provides automatic activation when 'uv run remocode transparent' is used
# and zero interference when 'claude' is run directly.
def _is_remocode_active() -> bool:
    """Check if RemoCode is currently active by looking for the process marker"""
    try:
        marker_file = Path.home() / ".remocode" / "active.marker"
        if not marker_file.exists():
            return False

        # Check if the marker is from a currently running process
        with open(marker_file) as f:
            marker_data = json.load(f)

        pid = marker_data.get("pid")
        if pid:
            # Check if process is still running
            try:
                os.kill(pid, 0)  # Signal 0 checks if process exists
                return True
            except (OSError, ProcessLookupError):
                # Process no longer exists, clean up stale marker
                marker_file.unlink()
                return False

    except Exception:
        # If anything goes wrong, assume RemoCode is not active
        pass

    return False


REMOCODE_ENABLED = _is_remocode_active()

TG_TOKEN = os.getenv("TG_TOKEN")
TG_CHAT = os.getenv("TG_CHAT")

# Configuration for live message aggregation
STATE_DIR = Path(__file__).parent.parent / "state"
STATE_DIR.mkdir(exist_ok=True)
LIVE_MODE_ENABLED = os.getenv("REMOCODE_LIVE_MODE", "false").lower() == "true"
LIVE_MESSAGE_FILE = str(STATE_DIR / "live_message.json")

# Configurable log path
LOG_PATH = os.getenv(
    "REMOCODE_LOG_PATH",
    str(Path(__file__).parent.parent.parent.parent / "logs" / "remocode.log"),
)

# ---------------------------------------------------------------------------
# Lightweight non-blocking notifications for routine tool calls
# ---------------------------------------------------------------------------


def _notify_only(tool: str, tool_input: dict, human_msg: str | None = None) -> None:
    """Fire-and-forget notification to Telegram describing an auto-approved
    tool call.  Never blocks execution.
    """
    if not TG_TOKEN or not TG_CHAT:
        return

    try:
        summary_parts = [f"🔧 **{tool}** auto-approved"]
        if human_msg:
            summary_parts.append(human_msg)

        if tool_input:
            import json

            payload = json.dumps(tool_input, indent=2)
            if len(payload) > 3500:
                # Too large – ship as document
                tmp_path = "/tmp/pretool_payload.json"
                with open(tmp_path, "w") as _f:
                    _f.write(payload)
                _telegram(
                    "sendDocument",
                    chat_id=TG_CHAT,
                    document=open(tmp_path, "rb"),
                    caption=f"{tool}.json",
                )
            else:
                summary_parts.append(f"```json\n{payload}\n```")

        _telegram(
            "sendMessage",
            chat_id=TG_CHAT,
            text="\n\n".join(summary_parts),
            parse_mode="Markdown",
        )
    except Exception:
        # Never break tool execution
        pass


# Short ping for each auto-approved tool -----------------------------------


def _quick_ping(tool: str, summary: str = "") -> None:
    """Send a minimal one-liner to Telegram and return immediately."""
    if not TG_TOKEN or not TG_CHAT:
        return
    try:
        text = f"🔧 {tool} OK"
        if summary:
            text += f" — {summary}"
        _telegram("sendMessage", chat_id=TG_CHAT, text=text)
    except Exception:
        pass


# ---------------------------------------------------------------------------
# Heuristics
# ---------------------------------------------------------------------------

# Auto-approve these tools - they're safe and commonly used
# The philosophy: Most tools should be auto-approved, only ask for genuinely dangerous operations
AUTONOMOUS_TOOLS: set[str] = {
    # File operations - generally safe, Claude Code has built-in protections
    "Read",
    "Write",
    "Edit",
    "MultiEdit",
    "NotebookRead",
    "NotebookEdit",
    # Search and navigation - always safe
    "grep_search",
    "codebase_search",
    "file_search",
    "list_dir",
    "Glob",
    "glob",
    "LS",
    "ls",
    "Search",
    "Grep",
    # Web and API - usually safe for read operations
    "WebFetch",
    "WebSearch",
    # Development tools - Bash is handled specially with pattern matching
    "Bash",
    # Task management - generally safe, Claude Code manages subagents
    "Task",
    # Other common tools
    "TodoWrite",
}

# Auto-approve these bash commands - safe and commonly used
SAFE_BASH_PATTERNS = [
    # NPM/Node operations
    r"^npm (run|install|ci|test|build|start|dev).*",
    r"^yarn (install|build|test|start|dev).*",
    r"^pnpm (install|build|test|start|dev).*",
    r"^node .*",
    r"^npx (?!create-).*",  # Allow npx except create commands
    # Git operations (read-only)
    r"^git (status|diff|log|show|branch|checkout|pull|fetch|add|commit|stash).*",
    # File operations
    r"^ls.*",
    r"^cat.*",
    r"^head.*",
    r"^tail.*",
    r"^find.*",
    r"^grep.*",
    r"^mkdir.*",
    r"^cp.*",
    r"^mv.*",
    r"^touch.*",
    r"^chmod.*",
    # Development tools
    r"^python.*",
    r"^pip.*",
    r"^pytest.*",
    r"^black.*",
    r"^ruff.*",
    r"^cargo.*",
    r"^rustc.*",
    r"^go (run|build|test).*",
    r"^make.*",
    r"^cmake.*",
    # System info (safe)
    r"^ps.*",
    r"^top.*",
    r"^htop.*",
    r"^df.*",
    r"^du.*",
    r"^free.*",
    r"^uname.*",
    r"^whoami.*",
    r"^pwd.*",
    r"^which.*",
    r"^where.*",
    # Text processing
    r"^awk.*",
    r"^sed.*",
    r"^sort.*",
    r"^uniq.*",
    r"^wc.*",
    # Docker (read-only operations)
    r"^docker (ps|images|inspect|logs).*",
    r"^docker-compose (ps|logs).*",
]

# Only ask for permission on these - genuinely dangerous operations
DANGEROUS_BASH_PATTERNS = [
    # Destructive operations
    r"^rm -rf.*",
    r"^rm -f.*",
    r"^rmdir.*",
    # System operations
    r"^sudo .*",
    r"^su .*",
    # Network/deployment
    r"^git push.*",
    r"^vercel --prod.*",
    r"^netlify deploy --prod.*",
    r"^aws .*",
    r"^gcloud .*",
    r"^kubectl.*",
    # Package management (potentially dangerous)
    r"^npm (publish|unpublish).*",
    r"^pip install -e.*",  # Editable installs
    # Docker operations that change state
    r"^docker (run|exec|build|push|pull|stop|kill|rm).*",
    r"^docker-compose (up|down|build).*",
    # Process control
    r"^kill.*",
    r"^killall.*",
    r"^pkill.*",
    # File system changes
    r"^mount.*",
    r"^umount.*",
    r"^fdisk.*",
]


def _matches(cmd: str, patterns: List[str]) -> bool:
    return any(re.match(p, cmd) for p in patterns)


def _send_notification(
    message: str,
    tool_name: str,
    tool_input: dict,
    session_id: str = "",
    raw_payload: Optional[Dict[str, Any]] = None,
) -> None:
    """Enhanced notification system with improved progress visibility and transparency"""
    if not TG_TOKEN or not TG_CHAT:
        return

    try:
        if LIVE_MODE_ENABLED:
            # Use live message aggregation mode for better UX
            live_manager = LiveMessageManager()
            live_manager.add_activity(message, tool_name, session_id)
        else:
            # Enhanced individual notification mode with better formatting
            import datetime

            timestamp = datetime.datetime.now().strftime("%H:%M:%S")

            # Add session context if available
            session_info = f" (Session: {session_id[:8]}...)" if session_id else ""

            # Enhanced notification with progress indicators
            progress_emoji = {
                "Bash": "⚡",
                "Read": "📖",
                "Write": "✏️",
                "Edit": "✏️",
                "MultiEdit": "📝",
                "Glob": "🔍",
                "Grep": "🔍",
                "WebFetch": "🌐",
                "WebSearch": "🔍",
                "Task": "🎯",
            }.get(tool_name, "🔧")

            # Create enhanced notification message
            notification_text = (
                f"{progress_emoji} **Claude Code Progress** {session_info}\n\n"
                f"**Action**: {message}\n"
                f"**Tool**: {tool_name}\n"
                f"**Time**: {timestamp}\n"
                f"**Status**: ✅ Auto-approved"
            )

            # Add concise tool summary
            summary = _format_tool_summary(tool_name, tool_input)
            if summary and summary != "No parameters provided":
                notification_text += f"\n**Details**: {summary}"

            # Add debug info if requested (but keep it concise)
            if raw_payload and len(str(raw_payload)) < 500:
                json_str = json.dumps(raw_payload, indent=2)
                notification_text += f"\n\n🔧 **Debug**:\n```json\n{json_str}\n```"

            # Send notification without blocking
            _telegram(
                "sendMessage",
                chat_id=TG_CHAT,
                text=notification_text,
                parse_mode="Markdown",
                disable_notification=True,  # Don't spam with notifications
            )

        # Enhanced logging with more context
        with open(LOG_PATH, "a") as f:
            f.write(
                f"[pretool_guard] {timestamp} - {tool_name}: {_format_tool_summary(tool_name, tool_input)} (Session: {session_id[:8] if session_id else 'unknown'})\n"
            )

    except Exception as e:
        # Don't fail the hook if Telegram is unavailable
        with open(LOG_PATH, "a") as f:
            f.write(f"[pretool_guard] Telegram notification failed: {e}\n")


def _format_tool_summary(tool_name: str, tool_input: dict) -> str:
    """Format a detailed tool summary for notifications"""
    if tool_name == "Bash":
        cmd = tool_input.get("command", "unknown command")
        description = tool_input.get("description", "")
        if description:
            return f"Running: `{cmd}` - {description}"
        return f"Running: `{cmd}`"

    elif tool_name == "Glob":
        pattern = tool_input.get("pattern", tool_input.get("glob_pattern", ""))
        include_hidden = tool_input.get("include_hidden", False)
        if pattern:
            hidden_text = " (including hidden)" if include_hidden else ""
            return f"Finding files matching: `{pattern}`{hidden_text}"
        return "File pattern search (pattern not specified)"

    elif tool_name == "Read":
        file_path = tool_input.get("file_path", tool_input.get("path", "unknown file"))
        view_range = tool_input.get("view_range", [])
        if view_range and len(view_range) == 2:
            return f"Reading: `{file_path}` (lines {view_range[0]}-{view_range[1]})"
        return f"Reading: `{file_path}`"

    elif tool_name in ["Write", "Edit"]:
        file_path = tool_input.get("file_path", tool_input.get("path", "unknown file"))
        content = tool_input.get("content", "")
        if content:
            lines = len(content.split("\n"))
            return f"{'Writing' if tool_name == 'Write' else 'Editing'}: `{file_path}` ({lines} lines)"
        return f"{'Writing' if tool_name == 'Write' else 'Editing'}: `{file_path}`"

    elif tool_name == "MultiEdit":
        edits = tool_input.get("edits", [])
        if edits:
            files = [edit.get("file_path", "unknown") for edit in edits]
            return f"Editing {len(edits)} files: {', '.join(f'`{f}`' for f in files[:3])}{'...' if len(files) > 3 else ''}"
        return "Multi-file edit (no files specified)"

    elif tool_name == "WebFetch":
        url = tool_input.get("url", "unknown URL")
        return f"Fetching: `{url}`"

    elif tool_name == "WebSearch":
        query = tool_input.get("query", "unknown query")
        num_results = tool_input.get("num_results", 5)
        return f"Searching: `{query}` ({num_results} results)"

    elif tool_name == "Grep":
        pattern = tool_input.get("pattern", tool_input.get("search_term", ""))
        path = tool_input.get("path", tool_input.get("file_path", ""))
        if pattern and path:
            return f"Searching for `{pattern}` in `{path}`"
        elif pattern:
            return f"Searching for: `{pattern}`"
        return "Text search (pattern not specified)"

    elif tool_name == "Task":
        instruction = tool_input.get("instruction", "")
        if instruction:
            preview = instruction[:80] + "..." if len(instruction) > 80 else instruction
            return f"Subtask: {preview}"
        return "Running subtask"

    elif tool_name in ["NotebookRead", "NotebookEdit"]:
        file_path = tool_input.get("file_path", "unknown notebook")
        return (
            f"{'Reading' if 'Read' in tool_name else 'Editing'} notebook: `{file_path}`"
        )

    elif tool_name == "TodoWrite":
        content = tool_input.get("content", "")
        if content:
            preview = content[:60] + "..." if len(content) > 60 else content
            return f"Updating todo: {preview}"
        return "Updating todo list"

    elif tool_name.startswith("mcp__"):
        # Handle MCP tools
        parts = tool_name.split("__")
        if len(parts) >= 3:
            server = parts[1]
            tool = parts[2]
            return f"MCP {server}: {tool}"
        return f"MCP tool: {tool_name}"

    else:
        # Enhanced generic summary with better field detection
        priority_fields = [
            ("file_path", "path", "filename"),
            ("command", "cmd"),
            ("query", "search", "term"),
            ("url", "uri"),
            ("pattern", "glob", "regex"),
            ("content", "text", "data"),
            ("instruction", "task", "action"),
        ]

        for field_group in priority_fields:
            for field in field_group:
                if field in tool_input and tool_input[field]:
                    value = str(tool_input[field])
                    if len(value) > 60:
                        value = value[:60] + "..."
                    return f"{field}: `{value}`"

        # If no priority fields found, show available fields
        if tool_input:
            fields = list(tool_input.keys())[:3]  # Show first 3 fields
            return f"Parameters: {', '.join(fields)}"

        return "No parameters provided"


def _format_tool_description(tool_name: str, tool_input: dict) -> str:
    """Format tool description with meaningful details"""
    # Handle None or empty tool names
    if not tool_name or tool_name == "None":
        tool_name = "Unknown Tool"

    # Common tool descriptions
    tool_descriptions = {
        "Task": "🎯 Execute a sub-task or agent",
        "ExitPlanMode": "📋 Exit planning mode and proceed",
        "TodoWrite": "📝 Update todo list",
        "WebFetch": "🌐 Fetch content from web",
        "WebSearch": "🔍 Search the web",
        "NotebookRead": "📓 Read Jupyter notebook",
        "NotebookEdit": "📓 Edit Jupyter notebook",
        "Read": "📖 Read file content",
        "Write": "✏️ Write file content",
        "Edit": "✏️ Edit file content",
        "MultiEdit": "✏️ Edit multiple files",
        "Glob": "🔍 Find files by pattern",
        "Grep": "🔍 Search text in files",
        "LS": "📁 List directory contents",
        "Bash": "⚡ Run terminal command",
    }

    # Get tool description
    tool_desc = tool_descriptions.get(tool_name, f"🔧 Use {tool_name} tool")

    # Format tool input details
    if not tool_input or tool_input == {} or tool_input is None:
        input_details = "*(no parameters provided)*"
    else:
        # Show key parameters in a readable way
        key_params = []
        for key, value in tool_input.items():
            if value is None:
                key_params.append(f"**{key}**: `None`")
            elif isinstance(value, str) and len(value) > 100:
                # Truncate long strings
                key_params.append(f"**{key}**: `{value[:100]}...`")
            elif isinstance(value, (list, dict)) and len(str(value)) > 200:
                # Summarize complex objects
                key_params.append(
                    f"**{key}**: `{type(value).__name__}({len(value)} items)`"
                )
            else:
                key_params.append(f"**{key}**: `{value}`")

        if key_params:
            input_details = "\n".join(key_params)
        else:
            input_details = "*(empty parameters)*"

    return f"{tool_desc}\n\n{input_details}\n\nAllow this tool to run?"


# ---------------------------------------------------------------------------
# Live Message Management
# ---------------------------------------------------------------------------


class LiveMessageManager:
    """Manages live message aggregation for Telegram notifications"""

    def __init__(self) -> None:
        self.message_file = LIVE_MESSAGE_FILE
        self.max_activities = 10  # Maximum activities to show in one message

    def _load_live_state(self) -> Dict[str, Any]:
        """Load current live message state"""
        try:
            if os.path.exists(self.message_file):
                with open(self.message_file, "r") as f:
                    return json.load(f)
        except Exception:
            pass
        return {
            "message_id": None,
            "activities": [],
            "session_id": "",
            "last_update": 0,
        }

    def _save_live_state(self, state: Dict[str, Any]) -> None:
        """Save live message state"""
        try:
            with open(self.message_file, "w") as f:
                json.dump(state, f, indent=2)
        except Exception as e:
            with open(LOG_PATH, "a") as f:
                f.write(f"[live_message] Failed to save state: {e}\n")

    def add_activity(self, activity: str, tool_name: str, session_id: str) -> None:
        """Add a new activity to the live message"""
        if not TG_TOKEN or not TG_CHAT:
            return

        import datetime

        timestamp = datetime.datetime.now().strftime("%H:%M:%S")

        state = self._load_live_state()

        # Check if this is a new session
        if state["session_id"] != session_id:
            # New session - create new message
            state = {
                "message_id": None,
                "activities": [],
                "session_id": session_id,
                "last_update": 0,
            }

        # Add new activity
        activity_entry = {"time": timestamp, "activity": activity, "tool": tool_name}

        state["activities"].append(activity_entry)

        # Keep only recent activities
        if len(state["activities"]) > self.max_activities:
            state["activities"] = state["activities"][-self.max_activities :]

        # Update the message
        self._update_live_message(state, session_id)

    def _update_live_message(self, state: Dict[str, Any], session_id: str) -> None:
        """Update or create the live Telegram message"""
        try:
            # Build message content
            session_info = f" (Session: {session_id[:8]}...)" if session_id else ""
            message_text = f"🔄 **Claude Code Live Activity** {session_info}\n\n"

            # Add activities
            for activity in state["activities"]:
                message_text += f"⏰ {activity['time']} - {activity['activity']}\n"

            message_text += f"\n📊 Total activities: {len(state['activities'])}"

            if state["message_id"]:
                # Update existing message
                try:
                    _telegram(
                        "editMessageText",
                        chat_id=TG_CHAT,
                        message_id=state["message_id"],
                        text=message_text,
                        parse_mode="Markdown",
                    )
                except Exception:
                    # If edit fails, create new message
                    state["message_id"] = None
                    self._update_live_message(state, session_id)
                    return
            else:
                # Create new message
                response = _telegram(
                    "sendMessage",
                    chat_id=TG_CHAT,
                    text=message_text,
                    parse_mode="Markdown",
                )
                if response and "result" in response:
                    state["message_id"] = response["result"]["message_id"]

            # Save updated state
            state["last_update"] = time.time()
            self._save_live_state(state)

        except Exception as e:
            with open(LOG_PATH, "a") as f:
                f.write(f"[live_message] Failed to update message: {e}\n")

    def clear_live_message(self) -> None:
        """Clear the live message (called when permission is needed)"""
        try:
            if os.path.exists(self.message_file):
                os.remove(self.message_file)
        except Exception:
            pass


# ---------------------------------------------------------------------------
# Telegram helpers
# ---------------------------------------------------------------------------


def _telegram(method: str, **params: Any) -> Dict[str, Any]:
    """Lightweight POST using stdlib to avoid external deps."""
    url = f"https://api.telegram.org/bot{TG_TOKEN}/{method}"
    data = json.dumps(params).encode()
    req = urllib.request.Request(
        url, data=data, headers={"Content-Type": "application/json"}
    )
    with urllib.request.urlopen(req, timeout=10) as resp:
        return json.loads(resp.read().decode())


def _ask_plan_approval(plan_content: str, session_id: str = "") -> str:
    """
    Specialized function for plan approval with proper Claude Code option mapping.

    Returns:
        "1" for auto-accept, "2" for manual approve, "3" for keep planning, or "" for timeout/error
    """
    if not TG_TOKEN or not TG_CHAT:
        with open(LOG_PATH, "a") as f:
            f.write(
                f"[pretool_guard] Telegram not configured, falling back to terminal for plan approval (Session: {session_id[:8] if session_id else 'unknown'})\n"
            )
        return ""

    # Clear live message when user interaction is needed
    if LIVE_MODE_ENABLED:
        live_manager = LiveMessageManager()
        live_manager.clear_live_message()

    # Create the complete plan approval message (no truncation)
    message = (
        f"📝 **PLAN EXECUTION CHECKPOINT**\n\n"
        f"Claude wants to exit plan mode and execute this plan:\n\n"
        f"```\n{plan_content}\n```\n\n"
        f"⚠️ **This will begin making actual changes to your codebase!**\n\n"
        f"**Choose your approval option:**"
    )

    # Create keyboard with exact Claude Code options
    keyboard = {
        "inline_keyboard": [
            [{"text": "1️⃣ Yes, and auto-accept edits", "callback_data": "1"}],
            [{"text": "2️⃣ Yes, and manually approve edits", "callback_data": "2"}],
            [{"text": "3️⃣ No, keep planning", "callback_data": "3"}],
        ]
    }

    try:
        sent = _telegram(
            "sendMessage",
            chat_id=TG_CHAT,
            text=f"🤖 *Claude needs your approval*\n\n{message}",
            parse_mode="Markdown",
            reply_markup=keyboard,
        )
        msg_id = sent["result"]["message_id"]
    except Exception as e:
        with open(LOG_PATH, "a") as f:
            f.write(f"[pretool_guard] Failed to send plan approval to Telegram: {e}\n")
        return ""

    last_update_id: int | None = None

    # Wait for user response with timeout
    import time

    start_time = time.time()
    timeout = 300  # 5 minutes

    while time.time() - start_time < timeout:
        try:
            updates = _telegram("getUpdates", offset=last_update_id, timeout=1)

            for update in updates.get("result", []):
                last_update_id = update["update_id"] + 1

                if "callback_query" in update:
                    callback = update["callback_query"]
                    if callback["message"]["message_id"] == msg_id:
                        choice = callback["data"]

                        # Acknowledge the callback
                        _telegram(
                            "answerCallbackQuery", callback_query_id=callback["id"]
                        )

                        # Update the message to show the choice
                        choice_text = {
                            "1": "1️⃣ Yes, and auto-accept edits",
                            "2": "2️⃣ Yes, and manually approve edits",
                            "3": "3️⃣ No, keep planning",
                        }.get(choice, f"Option {choice}")

                        _telegram(
                            "editMessageText",
                            chat_id=TG_CHAT,
                            message_id=msg_id,
                            text=f"✅ **Plan Approval Received**\n\nYou selected: {choice_text}\n\nClaude Code will proceed accordingly.",
                            parse_mode="Markdown",
                        )

                        with open(LOG_PATH, "a") as f:
                            f.write(
                                f"[pretool_guard] Plan approval received via Telegram: {choice} (Session: {session_id[:8] if session_id else 'unknown'})\n"
                            )

                        return choice

        except Exception as e:
            with open(LOG_PATH, "a") as f:
                f.write(
                    f"[pretool_guard] Error polling Telegram for plan approval: {e}\n"
                )
            time.sleep(1)
            continue

        time.sleep(0.5)

    # Timeout - clean up message
    try:
        _telegram(
            "editMessageText",
            chat_id=TG_CHAT,
            message_id=msg_id,
            text=f"⏰ **Plan Approval Timeout**\n\nNo response received within {timeout//60} minutes.\nPlease respond in the terminal.",
            parse_mode="Markdown",
        )
    except:
        pass

    with open(LOG_PATH, "a") as f:
        f.write(
            f"[pretool_guard] Plan approval timeout after {timeout}s (Session: {session_id[:8] if session_id else 'unknown'})\n"
        )

    return ""


def _ask_user(message: str, tool: str = "", cmd: str = "") -> bool:
    """Send inline-keyboard prompt to Telegram and wait **indefinitely** for a
    response.  No automatic approvals – the decision must come from the user
    (either via Telegram or by terminating the process).  If Telegram isn’t
    configured we deny by default so Claude continues to prompt in the
    terminal as usual."""

    # If Telegram integration is missing we DENY, so Claude’s normal terminal
    # approval flow will still appear.  No silent auto-approval.
    if not TG_TOKEN or not TG_CHAT:
        # Log the fallback for debugging
        with open(LOG_PATH, "a") as f:
            f.write(
                f"[pretool_guard] Telegram not configured, falling back to terminal approval for {tool}: {cmd}\n"
            )
        return False

    # Clear live message when user interaction is needed
    if LIVE_MODE_ENABLED:
        live_manager = LiveMessageManager()
        live_manager.clear_live_message()

    # --------------------------------------------------------------
    # Attempt to detect explicit option patterns in the *original*
    # message so we can mirror *all* choices Claude shows in the
    # terminal (e.g. numbered lists, (y/n) prompts, etc.).
    # --------------------------------------------------------------

    import re

    # 1) Detect (y/n) style prompts
    yes_no_match = re.search(r"\(y\s*/\s*n\)", message.lower())
    numbered_matches = re.findall(r"^\s*(\d+)[\)\.].+", message, re.MULTILINE)

    if yes_no_match:
        keyboard = {
            "inline_keyboard": [
                [
                    {"text": "✅ Yes (y)", "callback_data": "y"},
                    {"text": "⛔️ No (n)", "callback_data": "n"},
                ]
            ]
        }

    elif numbered_matches:
        # Build button per distinct number (keep order)
        buttons = []
        added = set()
        for num in numbered_matches:
            if num in added:
                continue
            added.add(num)
            buttons.append([{"text": num, "callback_data": num}])

        keyboard = {"inline_keyboard": buttons}

    # Fallback to context-specific keyboards
    elif "git push" in cmd:
        keyboard = {
            "inline_keyboard": [
                [
                    {"text": "✅ Push now", "callback_data": "yes"},
                    {"text": "📝 Review first", "callback_data": "no"},
                ],
                [
                    {
                        "text": "🔒 Always allow git push",
                        "callback_data": "always_allow",
                    },
                    {"text": "⛔️ Cancel", "callback_data": "no"},
                ],
            ]
        }
    elif "rm -rf" in cmd or "sudo" in cmd:
        keyboard = {
            "inline_keyboard": [
                [
                    {"text": "⚠️ Yes, I understand the risk", "callback_data": "yes"},
                    {"text": "⛔️ No, cancel", "callback_data": "no"},
                ],
                [
                    {
                        "text": "📋 Show me the full command first",
                        "callback_data": "show_details",
                    },
                ],
            ]
        }
    elif "docker" in cmd:
        keyboard = {
            "inline_keyboard": [
                [
                    {"text": "🐳 Allow Docker command", "callback_data": "yes"},
                    {"text": "⛔️ Deny", "callback_data": "no"},
                ],
                [
                    {"text": "🔒 Always allow Docker", "callback_data": "always_allow"},
                ],
            ]
        }
    else:
        # Default keyboard for other cases
        keyboard = {
            "inline_keyboard": [
                [
                    {"text": "✅ Allow", "callback_data": "yes"},
                    {"text": "⛔️ Deny", "callback_data": "no"},
                ],
                [
                    {
                        "text": "🔒 Always allow this tool",
                        "callback_data": "always_allow",
                    },
                ],
            ]
        }

    sent = _telegram(
        "sendMessage",
        chat_id=TG_CHAT,
        text=f"🤖 *Claude needs permission*\n{message}",
        parse_mode="Markdown",
        reply_markup=keyboard,
    )
    msg_id = sent["result"]["message_id"]

    last_update_id: int | None = None

    # ------------------------------------------------------------------
    # Indefinite wait: poll Telegram until the user presses a button or the
    # process is interrupted.  We intentionally *do not* auto-approve.
    # ------------------------------------------------------------------
    while True:
        updates = _telegram(
            "getUpdates",
            offset=(last_update_id or 0) + 1,
            timeout=60,
        )["result"]
        for upd in updates:
            last_update_id = upd["update_id"]
            if (
                "callback_query" in upd
                and upd["callback_query"]["message"]["message_id"] == msg_id
            ):
                choice = upd["callback_query"]["data"]
                _telegram(
                    "answerCallbackQuery", callback_query_id=upd["callback_query"]["id"]
                )
                approved = choice in ["yes", "always_allow"]
                _telegram(
                    "editMessageText",
                    chat_id=TG_CHAT,
                    message_id=msg_id,
                    text=f"{message}\n\n*Choice:* {'✅ Allowed' if approved else '⛔️ Denied'}",
                    parse_mode="Markdown",
                )
                return approved
        # Keep-looping if nothing arrived – prevents tight busy-waiting by
        # relying on Telegram long-polling (timeout=60 above).


# ---------------------------------------------------------------------------
# Main
# ---------------------------------------------------------------------------


def main() -> None:
    # Early exit if remocode is not enabled - make hook transparent
    # This ensures Claude Code's default behavior is preserved when RemoCode is off
    if not REMOCODE_ENABLED:
        print(
            json.dumps(
                {
                    "decision": "approve",
                    "reason": "RemoCode disabled - transparent mode",
                }
            )
        )
        return

    try:
        # Read JSON input from stdin as per Claude Code hooks documentation
        input_data = json.loads(sys.stdin.read())

        # Extract fields according to Claude Code PreToolUse hook schema
        tool_name = input_data.get("tool_name", "Unknown")
        tool_input: dict = input_data.get("tool_input", {})
        session_id = input_data.get("session_id", "")
        hook_event_name = input_data.get("hook_event_name", "")

        # Debug logging to file for troubleshooting
        debug_info = f"Hook Event: {hook_event_name}, Tool: {tool_name}, Input: {tool_input}, Session: {session_id}\n"
        debug_info += f"Raw payload: {json.dumps(input_data, indent=2)}\n"
        with open(LOG_PATH, "a") as f:
            f.write(f"[pretool_guard] {debug_info}")

        # Validate that this is actually a PreToolUse hook
        if hook_event_name != "PreToolUse":
            print(
                json.dumps(
                    {
                        "decision": "approve",
                        "reason": f"Not a PreToolUse hook: {hook_event_name}",
                    }
                )
            )
            return

    except json.JSONDecodeError as e:
        error_msg = f"Failed to parse JSON input: {e}"
        with open(LOG_PATH, "a") as f:
            f.write(f"[pretool_guard] ERROR: {error_msg}\n")
        print(json.dumps({"decision": "block", "reason": "Invalid JSON input to hook"}))
        return
    except Exception as e:
        error_msg = f"Unexpected error: {e}"
        with open(LOG_PATH, "a") as f:
            f.write(f"[pretool_guard] ERROR: {error_msg}\n")
        print(json.dumps({"decision": "block", "reason": "Hook execution error"}))
        return

    # Enhanced safety approach: Transparent notifications with strict safety controls
    # Philosophy: Never auto-approve without explicit user consent, maintain Claude Code's safety

    if tool_name == "Bash":
        cmd = tool_input.get("command", "")

        # Check for genuinely dangerous patterns that require explicit permission
        if _matches(cmd, DANGEROUS_BASH_PATTERNS):
            # CRITICAL: Always require explicit user approval for dangerous commands
            # No auto-approval, no timeouts - user must decide
            description = f"⚠️ **DANGEROUS COMMAND DETECTED**\n\n**Command**: `{cmd}`\n\n🚨 This command could:\n• Delete files permanently\n• Modify system settings\n• Access sensitive data\n• Deploy to production\n\n**Only approve if you understand the risks!**"
            approved = _ask_user(description, tool_name, cmd)
            if approved:
                print(
                    json.dumps(
                        {
                            "decision": "approve",
                            "reason": "User explicitly approved dangerous command",
                        }
                    )
                )
            else:
                print(
                    json.dumps(
                        {
                            "decision": "block",
                            "reason": "User denied dangerous command - safety preserved",
                        }
                    )
                )
            return
        else:
            # For safe bash commands, send notification but auto-approve
            # This maintains transparency while allowing normal development workflow
            _quick_ping(
                f"🔧 **Bash**: `{cmd}`",
                _format_tool_summary(tool_name, tool_input),
            )
            print(
                json.dumps(
                    {"decision": "approve", "reason": "Safe command auto-approved"}
                )
            )
            return

    # CRITICAL: Plan execution checkpoint - provide dual interface
    if tool_name == "ExitPlanMode":
        plan_content = tool_input.get("plan", "Plan content not available")

        # Send plan notification and get user approval via Telegram
        if TG_TOKEN and TG_CHAT:
            try:
                # Send initial notification
                notification_message = (
                    f"📝 **PLAN READY FOR EXECUTION**\n\n"
                    f"Claude has created a plan and is requesting approval:\n\n"
                    f"```\n{plan_content}\n```\n\n"
                    f"⚠️ **Choose your approval option below:**"
                )

                _telegram(
                    "sendMessage",
                    chat_id=TG_CHAT,
                    text=notification_message,
                    parse_mode="Markdown",
                )

                with open(LOG_PATH, "a") as f:
                    f.write(
                        f"[pretool_guard] Plan notification sent to Telegram (Session: {session_id[:8] if session_id else 'unknown'})\n"
                    )

                # Ask for plan approval with interactive buttons
                user_choice = _ask_plan_approval(plan_content, session_id)

                # Process user choice
                if user_choice in ["1", "2"]:
                    # User approved (either auto-accept or manual approve)
                    decision = "approve"
                    reason = f"User approved via Telegram (option {user_choice})"
                    with open(LOG_PATH, "a") as f:
                        f.write(
                            f"[pretool_guard] ExitPlanMode approved - user chose option {user_choice} (Session: {session_id[:8] if session_id else 'unknown'})\n"
                        )
                else:
                    # User denied or timeout
                    decision = "block"
                    reason = "User denied or timeout - staying in planning mode"
                    with open(LOG_PATH, "a") as f:
                        f.write(
                            f"[pretool_guard] ExitPlanMode blocked - user chose to keep planning (Session: {session_id[:8] if session_id else 'unknown'})\n"
                        )

            except Exception as e:
                # Fallback to blocking if Telegram fails
                decision = "block"
                reason = f"Telegram error: {e} - falling back to terminal"
                with open(LOG_PATH, "a") as f:
                    f.write(
                        f"[pretool_guard] ExitPlanMode Telegram error: {e}, falling back to block (Session: {session_id[:8] if session_id else 'unknown'})\n"
                    )
        else:
            # No Telegram configured - block to let Claude Code show terminal menu
            decision = "block"
            reason = "No Telegram configured - falling back to terminal approval"
            with open(LOG_PATH, "a") as f:
                f.write(
                    f"[pretool_guard] ExitPlanMode no Telegram - falling back to terminal (Session: {session_id[:8] if session_id else 'unknown'})\n"
                )

        # Send decision to Claude Code
        response = json.dumps({"decision": decision, "reason": reason})
        print(response)
        sys.stdout.flush()
        return

    elif tool_name in AUTONOMOUS_TOOLS:
        # Auto-approve common safe tools but maintain transparency
        # These tools are generally safe and commonly used in development
        _quick_ping(
            f"🔧 **{tool_name}**: {_format_tool_summary(tool_name, tool_input)}",
            _format_tool_summary(tool_name, tool_input),
        )
        print(
            json.dumps(
                {
                    "decision": "approve",
                    "reason": f"Safe tool {tool_name} auto-approved",
                }
            )
        )
        return

    else:
        # Unknown tools - send detailed notification and auto-approve with caution
        # This maintains development flow while providing transparency
        # If a tool proves dangerous, it should be added to explicit approval lists
        _send_notification(
            f"❓ **Unknown Tool**: {tool_name} - {_format_tool_summary(tool_name, tool_input)}",
            tool_name,
            tool_input,
            session_id,
            input_data,
        )
        print(
            json.dumps(
                {
                    "decision": "approve",
                    "reason": f"Unknown tool {tool_name} auto-approved with monitoring",
                }
            )
        )
        return


if __name__ == "__main__":
    try:
        main()
    except Exception as exc:  # pragma: no cover
        print(json.dumps({"approved": False, "error": str(exc)}))
