"""
Production Telegram bot for RemoCode.

Handles Telegram interactions, menu buttons, commands,
and real-time output streaming with rate limiting.
"""

import asyncio
import logging
import re
import time
from collections import deque
from typing import Callable, List, Optional

try:
    from telegram import Bot, InlineKeyboardButton, InlineKeyboardMarkup, Update
    from telegram.error import RetryAfter, TelegramError
    from telegram.ext import (
        Application,
        CallbackQueryHandler,
        CommandHandler,
        ContextTypes,
        MessageHandler,
        filters,
    )

    TELEGRAM_AVAILABLE = True
except ImportError:
    Bot = InlineKeyboardButton = InlineKeyboardMarkup = Update = None
    Application = CallbackQueryHandler = MessageHandler = None
    CommandHandler = filters = RetryAfter = TelegramError = None

    # Create a dummy ContextTypes for type hints
    class ContextTypes:
        DEFAULT_TYPE = None

    TELEGRAM_AVAILABLE = False

from remocode.config import EnvironmentConfig
from remocode.handy_commands import HandyCommandExecutor
from remocode.menu_detector_v1 import DetectedMenu, MenuType

logger = logging.getLogger(__name__)


class TelegramBot:
    """Production Telegram bot for RemoCode integration."""

    def __init__(
        self,
        token: str,
        chat_id: str,
        on_menu_choice: Optional[Callable[[str, str], None]] = None,
        on_command: Optional[Callable[[str, List[str]], None]] = None,
        get_batch_interval: Optional[Callable[[], float]] = None,
        get_claude_state: Optional[Callable[[], str]] = None,
        on_user_input: Optional[Callable[[str], None]] = None,
    ):
        """
        Initialize Telegram bot.

        Args:
            token: Telegram bot token
            chat_id: Target chat ID for communication
            on_menu_choice: Callback for menu choice submissions
            on_command: Callback for command handling
        """
        if not TELEGRAM_AVAILABLE:
            raise RuntimeError("python-telegram-bot is not installed")

        self.token = token
        self.chat_id = chat_id
        self.on_menu_choice = on_menu_choice
        self.on_command = on_command
        self.get_batch_interval = get_batch_interval
        self.get_claude_state = get_claude_state
        self.on_user_input = on_user_input

        # Bot and application
        self.bot: Optional[Bot] = None
        self.application: Optional[Application] = None

        # Message management
        self.current_menu_message_id: Optional[int] = None
        self.live_view_message_id: Optional[int] = None
        self.live_view_content: str = ""
        self.message_buffer: deque = deque(maxlen=50)
        self.last_batch_time = 0
        self.batch_interval = 1.0  # 1 second batching

        # Live view state
        self.should_create_new_message = True  # Start with new message
        self.last_edit_time = 0
        self.edit_throttle_interval = 1.0  # Edit live view every 1 second max

        # Rate limiting
        self.last_message_time = 0
        self.message_count_window = deque(maxlen=30)  # Track last 30 messages

        # State
        self.running = False
        self.update_task: Optional[asyncio.Task] = None
        self._polling_task: Optional[asyncio.Task] = None

        # Status indicators
        self.last_status_message_id: Optional[int] = None
        self.last_claude_state: Optional[str] = None
        self.status_update_interval = 15.0  # Send status updates every 15 seconds

        # Command execution
        self.handy_executor = HandyCommandExecutor(
            on_output=self._handle_command_output
        )

    async def start(self) -> bool:
        """
        Start the Telegram bot.

        Returns:
            True if started successfully, False otherwise
        """
        if self.running:
            logger.warning("Telegram bot already running")
            return False

        try:
            self.bot = Bot(token=self.token)

            # Test bot connection
            me = await self.bot.get_me()
            logger.info(f"Telegram bot connected: @{me.username}")

            # Create application
            self.application = Application.builder().bot(self.bot).build()

            # Add handlers
            self._setup_handlers()

            # Initialize and start the Telegram application
            await self.application.initialize()
            await self.application.start()

            # Start polling in a background task using the Updater API that is safe
            # to call from an already-running event loop (avoids “loop already running”).
            self._polling_task = asyncio.create_task(self._poll_updates())

            self.running = True

            # NEW: Send a visible startup message so the user knows the bot is live
            try:
                await self._send_message_with_retry(
                    text=(
                        "🚀 *RemoCode Started*\n"
                        "Mirroring Claude CLI output will appear below."
                    ),
                    parse_mode="Markdown",
                    disable_notification=False,  # notify the user explicitly
                )
            except Exception as start_msg_err:
                logger.debug(f"Failed to send startup message: {start_msg_err}")

            # Start live view updater task
            self.update_task = asyncio.create_task(self._live_view_updater())

            # Start status indicator task
            asyncio.create_task(self._send_status_updates())

            logger.info("Telegram bot started successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to start Telegram bot: {e}")
            await self.stop()
            return False

    async def stop(self) -> None:
        """Stop the Telegram bot."""
        if not self.running:
            return

        logger.info("Stopping Telegram bot...")
        self.running = False

        # Cancel update task
        if self.update_task and not self.update_task.done():
            self.update_task.cancel()
            try:
                await self.update_task
            except asyncio.CancelledError:
                pass

        # Cancel polling task
        if self._polling_task and not self._polling_task.done():
            self._polling_task.cancel()
            try:
                await self._polling_task
            except asyncio.CancelledError:
                pass

        # Stop application
        if self.application:
            try:
                await self.application.stop()
                await self.application.shutdown()
            except Exception as e:
                logger.error(f"Error stopping Telegram application: {e}")

        self.bot = None
        self.application = None
        logger.info("Telegram bot stopped")

    async def _poll_updates(self) -> None:
        """Background task that continuously polls Telegram for updates."""
        if not self.application:
            return

        try:
            # Start polling without attempting to run a new event loop
            await self.application.updater.start_polling(drop_pending_updates=True)

            # Idle until the bot manager requests shutdown
            while self.running:
                await asyncio.sleep(1)

            # Stop polling gracefully when requested
            await self.application.updater.stop()

        except Exception as e:
            logger.error(f"Error in Telegram polling: {e}")

    def _setup_handlers(self) -> None:
        """Setup Telegram message and callback handlers."""
        if not self.application:
            return

        # Command handlers
        self.application.add_handler(
            CommandHandler("handy", self._handle_handy_command)
        )
        self.application.add_handler(CommandHandler("cost", self._handle_cost_command))
        self.application.add_handler(
            CommandHandler("status", self._handle_status_command)
        )

        # Callback query handler for inline buttons
        self.application.add_handler(CallbackQueryHandler(self._handle_callback_query))

        # Message handler for text messages
        self.application.add_handler(
            MessageHandler(filters.TEXT & ~filters.COMMAND, self._handle_text_message)
        )

    async def send_output(self, text: str, silent: bool = True) -> None:
        """
        Send Claude CLI output to Telegram using live view updates.

        Args:
            text: Output text from Claude CLI
            silent: Whether to send silently (default True)
        """
        if not self.running or not self.bot:
            return

        # Clean ANSI sequences
        clean_text = self._strip_ansi(text).strip()
        if not clean_text:
            return

        # Update live view content
        self.live_view_content += clean_text + "\n"

        # Keep live view content to last 50 lines max
        lines = self.live_view_content.split("\n")
        if len(lines) > 50:
            self.live_view_content = "\n".join(lines[-50:])

        logger.debug(f"[send_output] Updated live view content: {len(lines)} lines")

        # If no live-view exists yet and we have enough content, create it right away
        if self.should_create_new_message and self.live_view_content.strip():
            try:
                await self.create_new_live_message()
            except Exception as e:
                logger.debug(f"Failed to create initial live view in send_output: {e}")

    async def create_new_live_message(self, initial_content: str = "") -> None:
        """Create a new live view message based on Claude state."""
        if not self.running or not self.bot:
            return

        try:
            current_state = (
                self.get_claude_state() if self.get_claude_state else "active"
            )

            state_headers = {
                "thinking": "🤔 **Claude is thinking...**",
                "tool_use": "🛠️ **Claude is using tools...**",
                "streaming": "💭 **Claude is responding...**",
                "waiting": "⏸️ **Claude is waiting for input**",
            }

            header = state_headers.get(current_state, "⚡ **Claude is active**")
            content = (
                f"{header}\n\n```\n{initial_content or self.live_view_content}\n```"
            )

            message = await self._send_message_with_retry(
                text=content, parse_mode="Markdown", disable_notification=True
            )

            if message:
                self.live_view_message_id = message.message_id
                self.should_create_new_message = False
                self.live_view_content = initial_content or self.live_view_content
                logger.info(
                    f"Created new live view message {self.live_view_message_id}"
                )

        except Exception as e:
            logger.error(f"Error creating new live message: {e}")

    async def update_live_view(self) -> None:
        """Update the existing live view message with current content."""
        if not self.running or not self.bot or not self.live_view_message_id:
            return

        import time

        current_time = time.time()

        # Throttle edits to avoid rate limiting
        if current_time - self.last_edit_time < self.edit_throttle_interval:
            return

        try:
            current_state = (
                self.get_claude_state() if self.get_claude_state else "active"
            )

            state_headers = {
                "thinking": "🤔 **Claude is thinking...**",
                "tool_use": "🛠️ **Claude is using tools...**",
                "streaming": "💭 **Claude is responding...**",
                "waiting": "⏸️ **Claude is waiting for input**",
            }

            header = state_headers.get(current_state, "⚡ **Claude is active**")

            # Limit content length for Telegram
            content_lines = self.live_view_content.split("\n")
            if len(content_lines) > 30:
                content_lines = content_lines[-30:]  # Keep last 30 lines

            display_content = "\n".join(content_lines)
            full_content = f"{header}\n\n```\n{display_content}\n```"

            await self.bot.edit_message_text(
                chat_id=self.chat_id,
                message_id=self.live_view_message_id,
                text=full_content,
                parse_mode="Markdown",
            )

            self.last_edit_time = current_time
            logger.debug(f"Updated live view message {self.live_view_message_id}")

        except Exception as e:
            if "message is not modified" not in str(e).lower():
                logger.debug(f"Error updating live view: {e}")
                # If message editing fails, create a new one
                self.should_create_new_message = True
                self.live_view_message_id = None

    async def show_menu(self, menu: DetectedMenu) -> Optional[int]:
        """
        Show a menu with inline buttons.

        Args:
            menu: Detected menu to display

        Returns:
            Message ID of the sent menu message
        """
        if not self.running or not self.bot:
            return None

        # Clear any existing menu
        await self._clear_current_menu()

        # ------------------------------------------------------------------
        # Build message text (strip TUI frame & ANSI codes)
        # ------------------------------------------------------------------
        def _strip_frame(line: str) -> str:
            """Remove leading box-drawing characters and surrounding whitespace."""
            return re.sub(r"^[\s│╭╰─]+", "", line).rstrip()

        # Use the entire raw output so the user sees full plan.
        clean_lines: list[str] = []
        border_only_re = re.compile(r"^[\s╭╰│─]+$")

        for raw_ln in menu.raw_output.splitlines():
            ln = self._strip_ansi(raw_ln)
            ln = _strip_frame(ln)

            # Skip pure border / frame lines that add visual noise in Telegram
            if not ln.strip() or border_only_re.match(ln):
                continue

            clean_lines.append(ln)

        context = "\n".join(clean_lines)

        if menu.menu_type == MenuType.PLAN_APPROVAL:
            menu_text = (
                "🚀 **Claude's Plan Ready**\n\n" + context + "\n\nChoose your response:"
            )
        else:
            menu_text = "🔤 **Menu Options**\n\n" + context + "\n\nSelect an option:"

        # Build inline keyboard
        keyboard = []
        for option in menu.options:
            button_text = f"{option.number}. {option.text}"
            callback_data = f"menu_{option.number}"
            keyboard.append(
                [InlineKeyboardButton(button_text, callback_data=callback_data)]
            )

        # Add additional options for interactive prompts
        if menu.menu_type == MenuType.PLAN_APPROVAL:
            keyboard.append(
                [
                    InlineKeyboardButton(
                        "💬 Give instruction", callback_data="menu_instruction"
                    )
                ]
            )

        reply_markup = InlineKeyboardMarkup(keyboard)

        try:
            message = await self._send_message_with_retry(
                text=menu_text, reply_markup=reply_markup, parse_mode="Markdown"
            )

            if message:
                self.current_menu_message_id = message.message_id
                logger.info(f"Sent menu with {len(menu.options)} options")
                return message.message_id

        except Exception as e:
            logger.error(f"Failed to send menu: {e}")

        return None

    async def _clear_current_menu(self) -> None:
        """Clear the current menu by editing it."""
        if not self.current_menu_message_id or not self.bot:
            return

        try:
            await self.bot.edit_message_text(
                chat_id=self.chat_id,
                message_id=self.current_menu_message_id,
                text="✅ _Menu completed_",
                parse_mode="Markdown",
            )
        except Exception as e:
            logger.debug(f"Could not clear menu: {e}")
        finally:
            self.current_menu_message_id = None

    def _strip_ansi(self, text: str) -> str:
        """Remove ANSI escape sequences so that plain text is safe for Telegram."""
        import re

        ansi_re = re.compile(r"\x1b\[[0-9;]*[A-Za-z]")
        return ansi_re.sub("", text)

    async def _live_view_updater(self) -> None:
        """Update live view message with current Claude output."""
        while self.running:
            try:
                # Use adaptive update interval
                current_interval = (
                    self.get_batch_interval()
                    if self.get_batch_interval
                    else self.batch_interval
                )
                await asyncio.sleep(current_interval)

                # Check if we need to create a new message
                if self.should_create_new_message and self.live_view_content.strip():
                    await self.create_new_live_message()

                # Update existing live view if we have content and a message to update
                elif self.live_view_content.strip() and self.live_view_message_id:
                    await self.update_live_view()

            except Exception as e:
                if self.running:
                    logger.error(f"Error in live view updates: {e}", exc_info=True)

    def _should_rate_limit(self) -> tuple[bool, float]:
        """Check if we should rate limit based on message window."""
        current_time = time.time()
        recent_messages = [
            t for t in self.message_count_window if current_time - t < 1.0
        ]

        if len(recent_messages) >= 29:  # Leave room for 1 more message per second
            wait_time = 1.0 - (current_time - min(recent_messages))
            return True, max(wait_time, 0.0)  # Prevent negative sleep
        return False, 0.0

    async def _send_message_with_retry(
        self, text: str, reply_markup=None, parse_mode=None, disable_notification=False
    ):
        """Send message with retry logic for rate limiting."""
        if not self.bot:
            return None

        # Check our own rate limiting
        should_limit, wait_time = self._should_rate_limit()
        if should_limit:
            logger.debug(f"Self rate limiting, waiting {wait_time:.2f}s")
            await asyncio.sleep(wait_time)

        for attempt in range(3):
            try:
                message = await self.bot.send_message(
                    chat_id=self.chat_id,
                    text=text[:4096],  # Telegram limit
                    reply_markup=reply_markup,
                    parse_mode=parse_mode,
                    disable_notification=disable_notification,
                )
                self._track_message_sent()
                return message

            except RetryAfter as e:
                wait_time = e.retry_after
                logger.warning(
                    f"Rate limited by Telegram, waiting {wait_time}s (attempt {attempt + 1})"
                )
                await asyncio.sleep(wait_time)
            except TelegramError as e:
                logger.error(f"Telegram error (attempt {attempt + 1}): {e}")

                # Common failure: Markdown entity parse error due to unescaped characters.
                # Retry **once** without any parse_mode so user still receives the text.
                if parse_mode is not None:
                    try:
                        logger.debug(
                            "Retrying message without parse_mode after Telegram parse error"
                        )
                        message = await self.bot.send_message(
                            chat_id=self.chat_id,
                            text=text[:4096],
                            reply_markup=reply_markup,
                            parse_mode=None,  # plain text
                            disable_notification=disable_notification,
                        )
                        self._track_message_sent()
                        return message
                    except Exception as fallback_err:
                        logger.error(
                            f"Fallback send (plain text) failed: {fallback_err}"
                        )

                if attempt == 2:
                    break
                await asyncio.sleep(1)
            except Exception as e:
                logger.error(f"Unexpected error sending message: {e}")
                break

        return None

    async def _edit_message_with_retry(
        self, message_id: int, text: str, reply_markup=None, parse_mode=None
    ):
        """Edit message with retry logic."""
        if not self.bot:
            return False

        try:
            await self.bot.edit_message_text(
                chat_id=self.chat_id,
                message_id=message_id,
                text=text[:4096],
                reply_markup=reply_markup,
                parse_mode=parse_mode,
            )
            return True
        except TelegramError as e:
            logger.debug(f"Could not edit message with parse_mode '{parse_mode}': {e}")

            # Retry a plain-text edit if markup parsing failed
            if parse_mode is not None:
                try:
                    await self.bot.edit_message_text(
                        chat_id=self.chat_id,
                        message_id=message_id,
                        text=text[:4096],
                        reply_markup=reply_markup,
                        parse_mode=None,
                    )
                    return True
                except Exception as fallback_err:
                    logger.debug(
                        f"Fallback edit (plain text) also failed: {fallback_err}"
                    )
            return False
        except Exception as e:
            logger.debug(f"Could not edit message: {e}")
            return False

    def _track_message_sent(self) -> None:
        """Track message sending for rate limiting."""
        current_time = time.time()
        self.last_message_time = current_time
        self.message_count_window.append(current_time)

    async def _handle_callback_query(
        self, update: Update, context: ContextTypes.DEFAULT_TYPE
    ):
        """Handle inline button callbacks."""
        query = update.callback_query
        if not query or not query.data:
            return

        await query.answer()  # Acknowledge the callback

        callback_data = query.data

        if callback_data.startswith("menu_"):
            choice = callback_data[5:]  # Remove "menu_" prefix

            if choice == "instruction":
                # Request instruction from user
                await query.edit_message_text(
                    "💬 **Please send your instruction:**", parse_mode="Markdown"
                )
                self.current_menu_message_id = None
            else:
                # Regular menu choice
                await self._clear_current_menu()

                if self.on_menu_choice:
                    try:
                        self.on_menu_choice(choice, "telegram")
                    except Exception as e:
                        logger.error(f"Error in menu choice callback: {e}")

        elif callback_data.startswith("handy_"):
            command_id = callback_data[6:]  # Remove "handy_" prefix

            if command_id == "cancel":
                await query.edit_message_text("❌ Cancelled")
                return

            # Execute handy command
            command = self.handy_executor.get_command(command_id)
            if command:
                await query.edit_message_text(
                    f"🚀 **Executing: {command.name}**\n\n"
                    f"Command: `{command.command}`\n\n"
                    "Check terminal or wait for output...",
                    parse_mode="Markdown",
                )

                # Execute command asynchronously
                asyncio.create_task(self.handy_executor.execute_command(command_id))

    async def _handle_text_message(
        self, update: Update, context: ContextTypes.DEFAULT_TYPE
    ):
        """Handle text messages (instructions, inputs)."""
        if not update.message or not update.message.text:
            return

        text = update.message.text.strip()

        # If we're waiting for an instruction after clicking "Give instruction"
        if not self.current_menu_message_id:
            # No active menu – treat this as normal user input to Claude.
            if self.on_user_input:
                try:
                    self.on_user_input(text)
                except Exception as e:
                    logger.error(f"Error in on_user_input callback: {e}")
            return

        if self.on_menu_choice:
            try:
                self.on_menu_choice(text, "telegram")
            except Exception as e:
                logger.error(f"Error in menu choice callback: {e}")

    async def _handle_handy_command(
        self, update: Update, context: ContextTypes.DEFAULT_TYPE
    ):
        """Handle /handy command for common tasks."""
        if not update.message:
            return

        # Get available commands from executor
        commands = self.handy_executor.get_available_commands()

        # Build handy command palette
        keyboard = []
        for cmd in commands:
            button_text = f"{cmd.icon} {cmd.name}"
            callback_data = f"handy_{cmd.id}"
            keyboard.append(
                [InlineKeyboardButton(button_text, callback_data=callback_data)]
            )

        # Add cancel option
        keyboard.append(
            [InlineKeyboardButton("❌ Cancel", callback_data="handy_cancel")]
        )

        reply_markup = InlineKeyboardMarkup(keyboard)

        await update.message.reply_text(
            "🛠️ **Handy Commands**\n\nSelect a command to run:",
            reply_markup=reply_markup,
            parse_mode="Markdown",
        )

    async def _handle_cost_command(
        self, update: Update, context: ContextTypes.DEFAULT_TYPE
    ):
        """Handle /cost command for token usage."""
        if not update.message:
            return

        # Simple cost tracking - would need to be enhanced with actual usage data
        cost_text = (
            "💰 **Token Usage Tracking**\n\n"
            "🔍 This feature tracks Claude's API usage from the terminal output.\n\n"
            "📊 **Current Session:**\n"
            "• Input tokens: Not tracked yet\n"
            "• Output tokens: Not tracked yet\n"
            "• Estimated cost: $0.00\n\n"
            "💡 **How to use:**\n"
            "Check your terminal for Claude's token usage reports or enable detailed logging."
        )

        await update.message.reply_text(cost_text, parse_mode="Markdown")

    async def _handle_status_command(
        self, update: Update, context: ContextTypes.DEFAULT_TYPE
    ):
        """Handle /status command."""
        if not update.message:
            return

        status_text = (
            "📊 **RemoCode Status**\n\n"
            f"🤖 Bot: Running\n"
            f"💬 Chat: {self.chat_id}\n"
            f"📊 Messages: {len(self.message_count_window)} in window\n"
            f"🔄 Buffer: {len(self.message_buffer)} queued"
        )

        await update.message.reply_text(status_text, parse_mode="Markdown")

    def _handle_command_output(self, line: str) -> None:
        """Handle output from handy commands."""
        # Send to output queue for batching
        asyncio.create_task(self.send_output(f"[CMD] {line}", silent=True))

    async def _send_status_updates(self) -> None:
        """Send periodic status updates showing Claude's current state."""
        while self.running:
            try:
                await asyncio.sleep(self.status_update_interval)

                if self.get_claude_state:
                    current_state = self.get_claude_state()

                    # Only send update if state changed
                    if (
                        current_state != self.last_claude_state
                        and current_state != "waiting"
                    ):
                        state_emojis = {
                            "waiting": "⏸️",
                            "thinking": "🤔",
                            "streaming": "💭",
                            "tool_use": "🛠️",
                        }

                        state_descriptions = {
                            "waiting": "Waiting for input",
                            "thinking": "Thinking...",
                            "streaming": "Generating response",
                            "tool_use": "Using tools",
                        }

                        emoji = state_emojis.get(current_state, "❓")
                        description = state_descriptions.get(
                            current_state, current_state
                        )

                        status_text = f"{emoji} Claude: {description}"

                        try:
                            # Send status update as a non-intrusive message
                            await self._send_message_with_retry(
                                text=status_text,
                                disable_notification=True,  # Silent update
                            )

                            self.last_claude_state = current_state
                            logger.debug(f"Sent status update: {current_state}")

                        except Exception as e:
                            logger.debug(f"Failed to send status update: {e}")

            except Exception as e:
                if self.running:
                    logger.error(f"Error in status update loop: {e}")

        logger.debug("Status update loop ended")

    def handle_claude_state_change(self, old_state: str, new_state: str) -> None:
        """Handle Claude state changes to trigger new live messages."""
        logger.info(f"Claude state changed: {old_state} -> {new_state}")

        # Only create a new live view message when a fresh task begins.
        # That corresponds to the moment we leave the idle "waiting" state and
        # Claude starts working.  All subsequent sub-state transitions (thinking →
        # tool_use → streaming, etc.) keep updating the same live message.  When
        # the cycle eventually returns to "waiting", we keep the same message so
        # the user can continue typing in the same context.
        significant_transitions = {
            ("waiting", "thinking"),  # New task – Claude begins reasoning
            ("waiting", "tool_use"),  # Rare case: immediate tool use
        }

        if (old_state, new_state) in significant_transitions:
            self.should_create_new_message = True
            logger.info(
                f"Triggering new message for state transition: {old_state} -> {new_state}"
            )
        else:
            logger.debug(
                f"Not creating new message for transition: {old_state} -> {new_state}"
            )

    async def __aenter__(self):
        """Async context manager entry."""
        await self.start()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.stop()


class TelegramBotManager:
    """Manages Telegram bot lifecycle and integration."""

    def __init__(self, env_config: EnvironmentConfig):
        """Initialize bot manager with environment configuration."""
        self.env_config = env_config
        self.bot: Optional[TelegramBot] = None
        self.enabled = env_config.has_telegram

    async def start(
        self,
        on_menu_choice: Optional[Callable[[str, str], None]] = None,
        on_command: Optional[Callable[[str, List[str]], None]] = None,
        get_batch_interval: Optional[Callable[[], float]] = None,
        get_claude_state: Optional[Callable[[], str]] = None,
        on_user_input: Optional[Callable[[str], None]] = None,
    ) -> bool:
        """
        Start Telegram bot if configuration is available.

        Args:
            on_menu_choice: Callback for menu choices
            on_command: Callback for commands

        Returns:
            True if bot started or not needed, False on error
        """
        if not self.enabled:
            logger.info("Telegram not configured, running in terminal-only mode")
            return True

        try:
            self.bot = TelegramBot(
                token=self.env_config.tg_token,
                chat_id=self.env_config.tg_chat,
                on_menu_choice=on_menu_choice,
                on_command=on_command,
                get_batch_interval=get_batch_interval,
                get_claude_state=get_claude_state,
                on_user_input=on_user_input,
            )

            return await self.bot.start()

        except Exception as e:
            logger.error(f"Failed to start Telegram bot manager: {e}")
            self.bot = None
            return False

    async def stop(self) -> None:
        """Stop Telegram bot if running."""
        if self.bot:
            await self.bot.stop()
            self.bot = None

    async def send_output(self, text: str, silent: bool = True) -> None:
        """Send output to Telegram if bot is running."""
        if self.bot:
            await self.bot.send_output(text, silent)

    async def show_menu(self, menu: DetectedMenu) -> bool:
        """Show menu in Telegram if bot is running."""
        if self.bot:
            message_id = await self.bot.show_menu(menu)
            return message_id is not None
        return False

    async def clear_menu(self) -> bool:
        """Clear current menu in Telegram if bot is running."""
        if self.bot:
            await self.bot._clear_current_menu()
            return True
        return False

    def handle_claude_state_change(self, old_state: str, new_state: str) -> None:
        """Handle Claude state changes if bot is running."""
        if self.bot:
            self.bot.handle_claude_state_change(old_state, new_state)
