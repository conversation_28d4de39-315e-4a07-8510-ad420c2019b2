# Force Plan Mode for Claude Code

## The Auto-Approval Issue

Claude Code is auto-approving plans instead of showing approval menus. This happens when:

1. <PERSON> is not in plan mode
2. The `--permission-mode plan` flag isn't working
3. Environment variables are overriding the setting

## Solutions to Try

### 1. Manual Plan Mode
Start Claude Code manually with plan mode:
```bash
npx @anthropic-ai/claude-code --permission-mode plan
```

### 2. Environment Variable
Set the permission mode via environment:
```bash
export CLAUDE_PERMISSION_MODE=plan
npx @anthropic-ai/claude-code
```

### 3. Config File
Create a `.claude/settings.local.json` file:
```json
{
  "permissionMode": "plan"
}
```

### 4. Interactive Mode
Some versions use `--interactive` instead:
```bash
npx @anthropic-ai/claude-code --interactive
```

## Current RemoCode v4 Approach

RemoCode v4 tries to add `--permission-mode plan` automatically, but if <PERSON> is still auto-approving, try:

1. **Stop RemoCode v4** (Ctrl+C)
2. **Start manually with plan mode**:
   ```bash
   cd /Users/<USER>/Desktop/github/remocode
   TG_TOKEN=7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo TG_CHAT=101011847 uv run remocode v4 --claude-cmd "npx @anthropic-ai/claude-code --permission-mode plan"
   ```

3. **Or try with environment variable**:
   ```bash
   cd /Users/<USER>/Desktop/github/remocode
   export CLAUDE_PERMISSION_MODE=plan
   TG_TOKEN=7820749555:AAGzeloh9C6i_CL3ULKQxeJgrPwZXk412Jo TG_CHAT=101011847 uv run remocode v4
   ```

## Verification

When plan mode is working, you should see:
- Plan approval menus with options like "1. Yes, and auto-accept edits"
- Claude asking "Would you like me to proceed with this plan?"
- No automatic execution of plans

If you still see auto-approval, the Claude Code version might not support plan mode or there's a configuration override.
