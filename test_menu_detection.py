#!/usr/bin/env python3

from remocode.v3.core.menu_detector import MenuDetector


def test_menu_detection():
    detector = MenuDetector()

    # Test with the actual menu text we saw
    test_text = """Do you want to make this edit to hello_world.py?
❯ 1. Yes
  2. Yes, and don't ask again this session (shift+tab)
  3. No, and tell <PERSON> what to do differently (esc)"""

    print("Testing menu detection...")
    print(f"Input text:\n{test_text}")
    print("-" * 50)

    result = detector.detect_menu_prompt(test_text)
    if result:
        print("✅ MENU DETECTED!")
        print(f"Prompt: {result.prompt}")
        print(f"Options: {len(result.options)}")
        for opt in result.options:
            print(f"  {opt.key}: {opt.label}")
    else:
        print("❌ No menu detected")


if __name__ == "__main__":
    test_menu_detection()
