repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-toml
      - id: check-json
      - id: check-merge-conflict
      - id: check-added-large-files

  - repo: https://github.com/psf/black
    rev: 23.12.1
    hooks:
      - id: black
        exclude: ^(reference/|archive/)

  - repo: https://github.com/pycqa/isort
    rev: 5.13.2
    hooks:
      - id: isort
        exclude: ^(reference/|archive/)

  # - repo: https://github.com/astral-sh/ruff-pre-commit
  #   rev: v0.1.8
  #   hooks:
  #     - id: ruff
  #       exclude: ^(reference/|archive/)
  #       args: [--fix]

  # Temporarily disabled mypy until type issues are resolved
  # - repo: https://github.com/pre-commit/mirrors-mypy
  #   rev: v1.8.0
  #   hooks:
  #     - id: mypy
  #       exclude: ^(reference/|archive/)
  #       additional_dependencies: [pydantic, python-telegram-bot, typer, rich]
