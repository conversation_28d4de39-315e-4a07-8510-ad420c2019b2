#!/usr/bin/env python3
"""
Test script to verify RemoCode v2 dual interface functionality
"""

import os
import sys
import time
import json
import subprocess
from pathlib import Path

def test_hook_activation():
    """Test that hooks are properly activated when RemoCode marker is present"""
    print("🧪 Testing Hook Activation...")

    # Check if RemoCode marker exists
    marker_file = Path.home() / ".remocode" / "active.marker"
    print(f"  Marker file exists: {marker_file.exists()}")

    if marker_file.exists():
        try:
            with open(marker_file) as f:
                marker_data = json.load(f)
            print(f"  Marker PID: {marker_data.get('pid')}")
            print(f"  Marker command: {marker_data.get('command', 'unknown')}")

            # Check if process is still running
            pid = marker_data.get('pid')
            if pid:
                try:
                    os.kill(pid, 0)  # Signal 0 checks if process exists
                    print(f"  ✅ Process {pid} is running")
                except (OSError, ProcessLookupError):
                    print(f"  ❌ Process {pid} is not running")
        except Exception as e:
            print(f"  ❌ Error reading marker: {e}")

    print()

def test_hook_configuration():
    """Test that Claude Code hooks are properly configured"""
    print("🧪 Testing Hook Configuration...")

    settings_file = Path(".claude/settings.local.json")
    if not settings_file.exists():
        print("  ❌ No .claude/settings.local.json found")
        return

    try:
        with open(settings_file) as f:
            settings = json.load(f)

        hooks = settings.get("hooks", {})

        # Check for required hooks
        required_hooks = ["PreToolUse", "Notification", "UserPromptSubmit"]
        for hook_name in required_hooks:
            if hook_name in hooks:
                hook_configs = hooks[hook_name]
                print(f"  ✅ {hook_name} hook configured ({len(hook_configs)} matchers)")

                # Check if hook scripts exist
                for matcher in hook_configs:
                    for hook in matcher.get("hooks", []):
                        command = hook.get("command", "")
                        if "python3" in command:
                            # Extract script path
                            parts = command.split()
                            if len(parts) >= 2:
                                script_path = parts[1]
                                if Path(script_path).exists():
                                    print(f"    ✅ Script exists: {Path(script_path).name}")
                                else:
                                    print(f"    ❌ Script missing: {script_path}")
            else:
                print(f"  ❌ {hook_name} hook not configured")

    except Exception as e:
        print(f"  ❌ Error reading settings: {e}")

    print()

def test_telegram_configuration():
    """Test Telegram configuration"""
    print("🧪 Testing Telegram Configuration...")

    tg_token = os.getenv("TG_TOKEN")
    tg_chat = os.getenv("TG_CHAT")

    if tg_token and tg_chat:
        print(f"  ✅ TG_TOKEN configured (length: {len(tg_token)})")
        print(f"  ✅ TG_CHAT configured: {tg_chat}")

        # Test basic Telegram connectivity
        try:
            import urllib.request
            import urllib.parse

            url = f"https://api.telegram.org/bot{tg_token}/getMe"
            req = urllib.request.Request(url)
            with urllib.request.urlopen(req, timeout=5) as resp:
                data = json.loads(resp.read().decode())
                if data.get("ok"):
                    bot_info = data.get("result", {})
                    print(f"  ✅ Telegram bot connected: {bot_info.get('username', 'unknown')}")
                else:
                    print(f"  ❌ Telegram API error: {data}")
        except Exception as e:
            print(f"  ⚠️  Telegram connectivity test failed: {e}")
    else:
        print("  ⚠️  Telegram not configured (TG_TOKEN and/or TG_CHAT missing)")
        print("  💡 Set environment variables to enable Telegram integration")

    print()

def test_state_directory():
    """Test that state directory is properly set up"""
    print("🧪 Testing State Directory...")

    state_dir = Path("src/remocode/state")
    print(f"  State directory exists: {state_dir.exists()}")

    if state_dir.exists():
        files = list(state_dir.glob("*"))
        print(f"  State files: {len(files)}")
        for file in files:
            print(f"    - {file.name}")
    else:
        print("  Creating state directory...")
        state_dir.mkdir(parents=True, exist_ok=True)
        print("  ✅ State directory created")

    print()

def main():
    """Run all tests"""
    print("🚀 RemoCode v2 Dual Interface Test Suite")
    print("=" * 50)

    test_hook_activation()
    test_hook_configuration()
    test_telegram_configuration()
    test_state_directory()

    print("🎯 Test Summary:")
    print("  The RemoCode v2 system appears to be properly configured.")
    print("  To test the complete dual interface:")
    print("  1. Set TG_TOKEN and TG_CHAT environment variables")
    print("  2. Run: uv run remocode transparent")
    print("  3. Use Claude Code normally in terminal")
    print("  4. Monitor and control via Telegram")
    print()
    print("💡 Key Features:")
    print("  - Hook-based interception of Claude Code events")
    print("  - First-response-wins coordination between terminal and Telegram")
    print("  - Bidirectional synchronization (Telegram inputs visible in terminal)")
    print("  - Claude Code remains completely native and unmodified")

if __name__ == "__main__":
    main()