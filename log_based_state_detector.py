#!/usr/bin/env python3
"""
Proof of Concept: Log-Based State Detection for RemoCode
Demonstrates real-time log monitoring with intelligent state detection
"""

import asyncio
import re
import time
from dataclasses import dataclass
from enum import Enum
from pathlib import Path
from typing import Callable, Dict, List, Optional


class ClaudeState(Enum):
    WORKING = "working"  # ⏺ <PERSON> is actively processing
    IDLE = "idle"  # > <PERSON> waiting for input
    MENU = "menu"  # > <PERSON> presenting menu options


@dataclass
class StateEvent:
    state: ClaudeState
    content: str
    timestamp: float
    session_id: Optional[str] = None


class LogStateDetector:
    """Real-time log file monitor with intelligent state detection"""

    def __init__(self, log_path: str = "logs/remocode.log"):
        self.log_path = Path(log_path)
        self.current_state = ClaudeState.IDLE
        self.state_callbacks: Dict[ClaudeState, List[Callable]] = {
            ClaudeState.WORKING: [],
            ClaudeState.IDLE: [],
            ClaudeState.MENU: [],
        }
        self.working_buffer: List[str] = []
        self.last_flush_time = time.time()

        # Log patterns for state detection
        self.patterns = {
            "working": [
                r"\[pretool_guard\] Hook Event: PreToolUse",
                r"\[pretool_guard\] \d+:\d+:\d+ - ExitPlanMode",
            ],
            "idle": [
                r'\[notification_handler\].*"Claude is waiting for your input"',
            ],
            "menu": [
                r'\[notification_handler\].*"Would you like to proceed.*1\. Yes.*2\. Yes.*3\. No"',
                r'\[notification_handler\].*"Ready to code.*1\. Yes.*2\. Yes.*3\. No"',
            ],
        }

    def register_callback(self, state: ClaudeState, callback: Callable):
        """Register callback for state changes"""
        self.state_callbacks[state].append(callback)

    async def start_monitoring(self):
        """Start real-time log monitoring"""
        print(f"🔍 Starting log monitoring: {self.log_path}")

        if not self.log_path.exists():
            print(f"❌ Log file not found: {self.log_path}")
            return

        # Start from end of file for real-time monitoring
        with open(self.log_path, "r") as f:
            f.seek(0, 2)  # Seek to end

            while True:
                line = f.readline()
                if line:
                    await self._process_log_line(line.strip())
                else:
                    # Check for buffered working events to flush
                    await self._check_working_buffer_flush()
                    await asyncio.sleep(0.1)  # Small delay to prevent CPU spinning

    async def _process_log_line(self, line: str):
        """Process a single log line and detect state changes"""
        detected_state = self._detect_state_from_line(line)

        if detected_state:
            await self._handle_state_change(detected_state, line)

    def _detect_state_from_line(self, line: str) -> Optional[ClaudeState]:
        """Detect Claude state from log line content"""
        # Check for working state patterns
        for pattern in self.patterns["working"]:
            if re.search(pattern, line):
                return ClaudeState.WORKING

        # Check for menu state patterns
        for pattern in self.patterns["menu"]:
            if re.search(pattern, line):
                return ClaudeState.MENU

        # Check for idle state patterns
        for pattern in self.patterns["idle"]:
            if re.search(pattern, line):
                return ClaudeState.IDLE

        return None

    async def _handle_state_change(self, new_state: ClaudeState, line: str):
        """Handle state transitions with intelligent batching"""
        if new_state == ClaudeState.WORKING:
            # Buffer working events for batching
            self.working_buffer.append(line)
            self.last_flush_time = time.time()

        elif new_state in [ClaudeState.IDLE, ClaudeState.MENU]:
            # Flush any buffered working events first
            if self.working_buffer:
                await self._flush_working_buffer()

            # Handle immediate state change
            await self._trigger_state_callbacks(new_state, line)

        self.current_state = new_state

    async def _check_working_buffer_flush(self):
        """Check if working buffer should be flushed (timeout-based)"""
        if self.working_buffer and (time.time() - self.last_flush_time) > 2.0:
            await self._flush_working_buffer()

    async def _flush_working_buffer(self):
        """Flush buffered working events as a single update"""
        if not self.working_buffer:
            return

        # Create aggregated working event
        aggregated_content = f"📊 Claude processed {len(self.working_buffer)} operations"
        event = StateEvent(
            state=ClaudeState.WORKING, content=aggregated_content, timestamp=time.time()
        )

        # Trigger callbacks with aggregated event
        for callback in self.state_callbacks[ClaudeState.WORKING]:
            try:
                await callback(event)
            except Exception as e:
                print(f"❌ Callback error: {e}")

        self.working_buffer.clear()
        print(f"🔄 Flushed {len(self.working_buffer)} working events")

    async def _trigger_state_callbacks(self, state: ClaudeState, content: str):
        """Trigger callbacks for state change"""
        event = StateEvent(state=state, content=content, timestamp=time.time())

        for callback in self.state_callbacks[state]:
            try:
                await callback(event)
            except Exception as e:
                print(f"❌ Callback error: {e}")


# Example usage and testing
async def demo_telegram_working_handler(event: StateEvent):
    """Demo handler for working state - would send batched updates to Telegram"""
    print(f"📱 [Telegram] Working: {event.content}")


async def demo_telegram_idle_handler(event: StateEvent):
    """Demo handler for idle state - would show text input interface"""
    print("📱 [Telegram] Idle detected - showing text input interface")


async def demo_telegram_menu_handler(event: StateEvent):
    """Demo handler for menu state - would extract options and show buttons"""
    print("📱 [Telegram] Menu detected - extracting options for inline keyboard")
    # Extract menu options from content
    if "1. Yes" in event.content and "2. Yes" in event.content:
        print(
            "   Buttons: [Yes, auto-accept] [Yes, manually approve] [No, keep planning]"
        )


async def main():
    """Demo the log-based state detection system"""
    print("🚀 Log-Based State Detection Demo")
    print("=" * 50)

    detector = LogStateDetector()

    # Register demo handlers
    detector.register_callback(ClaudeState.WORKING, demo_telegram_working_handler)
    detector.register_callback(ClaudeState.IDLE, demo_telegram_idle_handler)
    detector.register_callback(ClaudeState.MENU, demo_telegram_menu_handler)

    # Start monitoring
    try:
        await detector.start_monitoring()
    except KeyboardInterrupt:
        print("\n🛑 Monitoring stopped")


if __name__ == "__main__":
    asyncio.run(main())
