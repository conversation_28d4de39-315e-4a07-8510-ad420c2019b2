#!/usr/bin/env python3
"""
Remote Claude-Code Controller via Telegram
A 150-line Python wrapper that enables remote control of Claude-Code CLI via Telegram.
"""

import os
import sys
import re
import signal
import asyncio
import logging
from typing import Optional, List, Dict, Any
from collections import deque
import pexpect
import time

try:
    from telegram import Bot, InlineKeyboardButton, InlineKeyboardMarkup, Update
    from telegram.ext import Application, CallbackQueryHandler, MessageHandler, filters
    from telegram.error import RetryAfter
except ModuleNotFoundError:
    Bot = InlineKeyboardButton = InlineKeyboardMarkup = Update = None  # type: ignore
    Application = CallbackQueryHandler = MessageHandler = None  # type: ignore
    filters = None  # type: ignore
    logging.warning("python-telegram-bot is not installed; running in local-only mode")
from dotenv import load_dotenv
from asyncio import Queue
import contextlib

# Load environment variables
load_dotenv()

# Configuration
TG_TOKEN = os.getenv("TG_TOKEN")
TG_CHAT = os.getenv("TG_CHAT")
CLAUDE_CMD = os.getenv("CLAUDE_CMD", "npx @anthropic-ai/claude-code")

# Global state
claude_process: Optional[pexpect.spawn] = None
bot: Optional[Bot] = None
log_buffer = deque(maxlen=500)
active_menu_message_id: Optional[int] = None
choice_made = False
confirmation_pending = False  # track yes/no prompt

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[logging.FileHandler("remocode.log"), logging.StreamHandler()],
)
logger = logging.getLogger(__name__)


def log_and_buffer(message: str):
    """Log message and add to buffer for Telegram streaming"""
    logger.info(message)
    log_buffer.append(message)


async def send_telegram_message(text: str, reply_markup=None) -> Optional[int]:
    """Send message to Telegram and return message ID with retry-after support"""
    if not bot or not TG_CHAT:
        return None
    attempts = 0
    while attempts < 3:
        try:
            message = await bot.send_message(
                chat_id=TG_CHAT,
                text=text[:4096],  # Telegram limit
                reply_markup=reply_markup,
                parse_mode="Markdown",
            )
            return message.message_id
        except RetryAfter as e:
            wait_for = getattr(e, "retry_after", 5)
            logger.warning(f"Telegram rate limit hit, sleeping {wait_for}s")
            await asyncio.sleep(wait_for)
            attempts += 1
        except Exception as e:
            logger.error(f"Failed to send Telegram message: {e}")
            return None
    return None


async def edit_telegram_message(message_id: int, text: str, reply_markup=None):
    """Edit existing Telegram message"""
    if not bot or not TG_CHAT:
        return
    try:
        await bot.edit_message_text(
            chat_id=TG_CHAT,
            message_id=message_id,
            text=text[:4096],
            reply_markup=reply_markup,
            parse_mode="Markdown",
        )
    except Exception as e:
        logger.error(f"Failed to edit Telegram message: {e}")


class OutputTee:
    """File-like object passed to pexpect.logfile_read that pushes each line to an asyncio.Queue
    while letting interact() keep raw TUI output to stdout.
    """

    def __init__(self, loop: asyncio.AbstractEventLoop, q: Queue):
        self.loop = loop
        self.q: Queue = q
        self._buf: str = ""

    def write(self, data: str | bytes):
        # Ensure str
        if isinstance(data, bytes):
            try:
                data = data.decode("utf-8", errors="ignore")
            except Exception:
                data = str(data)
        # Replace carriage returns with newlines so spinner rewrites are treated as separate frames
        data = data.replace("\r", "\n")
        self._buf += data
        while "\n" in self._buf:
            line, self._buf = self._buf.split("\n", 1)
            asyncio.run_coroutine_threadsafe(self.q.put(line + "\n"), self.loop)

    def flush(self):
        pass  # no-op


def detect_menu(text: str) -> List[Dict[str, Any]]:
    """Detect numbered menu options in text"""
    menu_pattern = r"^\s*(\d+)\)\s*(.+?)$"
    matches = re.findall(menu_pattern, text, re.MULTILINE)

    if len(matches) < 2:  # Need at least 2 options to be a menu
        return []

    return [
        {"number": int(num), "text": desc.strip()}
        for num, desc in matches
        if desc.strip()
    ]


async def create_telegram_keyboard(
    menu_items: List[Dict[str, Any]]
) -> InlineKeyboardMarkup:
    """Create Telegram inline keyboard from menu items"""
    keyboard = []
    for item in menu_items:
        button = InlineKeyboardButton(
            text=f"#{item['number']}: {item['text'][:30]}...",
            callback_data=str(item["number"]),
        )
        keyboard.append([button])

    return InlineKeyboardMarkup(keyboard)


async def detect_confirmation(text: str) -> bool:
    """Return True if text appears to ask for yes/no confirmation."""
    confirm_patterns = [
        r"\(y/n\)\s*$",
        r"\[y/n\]\s*$",
        r"yes/no\s*\?$",
        r"\(yes/no\)\s*$",
    ]
    lowered = text.lower()
    for pat in confirm_patterns:
        if re.search(pat, lowered, re.MULTILINE):
            return True
    return False


async def create_yes_no_keyboard() -> InlineKeyboardMarkup:
    """Return inline keyboard with Yes/No buttons."""
    keyboard = [
        [
            InlineKeyboardButton(text="✅ Yes", callback_data="y"),
            InlineKeyboardButton(text="❌ No", callback_data="n"),
        ]
    ]
    return InlineKeyboardMarkup(keyboard)


async def handle_callback_query(update: Update, context):
    """Handle Telegram button presses"""
    global choice_made, active_menu_message_id, confirmation_pending

    query = update.callback_query
    await query.answer()

    if choice_made:
        return

    choice = query.data
    choice_made = True

    # Update message to show selection
    await edit_telegram_message(
        active_menu_message_id, f"✅ Selected: {choice}", reply_markup=None
    )

    # Send choice to Claude process
    if claude_process and claude_process.isalive():
        claude_process.sendline(choice)
        log_and_buffer(f"Sent choice: {choice}")

    confirmation_pending = False


async def handle_telegram_message(update: Update, context):
    """Handle regular Telegram messages"""
    if claude_process and claude_process.isalive():
        message = update.message.text
        claude_process.sendline(message)
        log_and_buffer(f"Sent message: {message}")


def setup_telegram_bot():
    """Setup Telegram bot with handlers, or return None if unavailable"""
    global bot

    # Skip Telegram integration if library is missing or credentials absent
    if Application is None or not TG_TOKEN or TG_CHAT is None:
        logger.info("Telegram bot disabled (missing deps or credentials)")
        return None

    application = Application.builder().token(TG_TOKEN).build()
    bot = application.bot

    # Add handlers
    application.add_handler(CallbackQueryHandler(handle_callback_query))
    application.add_handler(
        MessageHandler(filters.TEXT & ~filters.COMMAND, handle_telegram_message)
    )

    return application


async def process_output_queue(output_q: Queue):
    """Listen for stdout frames and expose only actionable prompts to Telegram."""
    global choice_made, active_menu_message_id, confirmation_pending

    SPINNER_RE = re.compile(r"^[\s]*[✻✢✽✳✶·].*")

    paragraph = ""

    while True:
        try:
            line = await output_q.get()
        except asyncio.CancelledError:
            break

        # Skip spinner/status lines
        if SPINNER_RE.match(line):
            continue

        # Accumulate for prompt detection
        paragraph += line

        if line.strip() == "":
            # Blank line indicates paragraph boundary – inspect content
            if paragraph.strip():
                menu_items = detect_menu(paragraph)
                if menu_items and not choice_made:
                    keyboard = await create_telegram_keyboard(menu_items)
                    active_menu_message_id = await send_telegram_message(
                        "🔢 Choose an option:", reply_markup=keyboard
                    )
                    choice_made = False
                elif await detect_confirmation(paragraph) and not confirmation_pending:
                    keyboard = await create_yes_no_keyboard()
                    active_menu_message_id = await send_telegram_message(
                        "❓ Confirm action:", reply_markup=keyboard
                    )
                    confirmation_pending = True
            # Reset paragraph accumulator
            paragraph = ""


async def read_local_input():
    """Forward lines typed in the local terminal to the Claude process asynchronously."""
    loop = asyncio.get_running_loop()
    while claude_process and claude_process.isalive():
        line = await loop.run_in_executor(None, sys.stdin.readline)
        if not line:
            continue
        line = line.rstrip("\n")
        claude_process.sendline(line)
        log_and_buffer(f"Sent local input: {line}")


async def main():
    """Main function"""
    global claude_process

    # Get command line arguments FIRST (needed for telegram decision)
    spec_file = sys.argv[1] if len(sys.argv) > 1 else None

    # Decide if we should enable Telegram (require token + spec file present)
    telegram_app = None
    if spec_file is not None:
        telegram_app = setup_telegram_bot()

    # Setup signal handlers
    def signal_handler(signum, frame):
        logger.info("Received interrupt signal")
        if claude_process:
            claude_process.terminate()
        sys.exit(0)

    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    # Setup Telegram bot
    # The setup_telegram_bot function now handles its own availability check
    # if telegram_app is None:
    #     logger.info("Running in local-only mode (no Telegram bot configured)")

    # Get command line arguments
    claude_args = [CLAUDE_CMD]
    if spec_file:
        # Pass path directly (interactive), no -p flag
        claude_args.append(spec_file)

    try:
        # Start Claude process
        logger.info(f"Starting Claude process: {' '.join(claude_args)}")
        loop = asyncio.get_running_loop()
        output_q: Queue = Queue()
        claude_process = pexpect.spawn(" ".join(claude_args), encoding="utf-8")
        claude_process.logfile_read = OutputTee(loop, output_q)

        if telegram_app:
            await send_telegram_message("🚀 Claude-Code remote session started")

        # Start Telegram bot if configured
        if telegram_app:
            await telegram_app.initialize()
            await telegram_app.start()

        # Run interact() in a thread so it owns the TTY
        interact_task = asyncio.to_thread(claude_process.interact)

        # Create queue consumer task
        queue_task = asyncio.create_task(process_output_queue(output_q))

        await interact_task  # Wait until user exits Claude

        # Once Claude exits, cancel queue consumer
        queue_task.cancel()
        with contextlib.suppress(asyncio.CancelledError):
            await queue_task

        if telegram_app:
            await telegram_app.stop()
    except Exception as e:
        logger.error(f"Error: {e}")
        if telegram_app:
            await send_telegram_message(f"❌ Error: {e}")
    finally:
        if claude_process:
            claude_process.terminate()
        logger.info("Session ended")


if __name__ == "__main__":
    asyncio.run(main())
