# Remote Claude-Code Controller - Usage Guide 📖

## Quick Start Checklist ✅

### 1. Installation

```bash
# Clone or download the files
# Make sure you have: remocode.py, requirements.txt, setup.sh, .env.example

# Run setup
chmod +x setup.sh
./setup.sh
```

### 2. Configuration

```bash
# Copy environment template
cp .env.example .env

# Edit with your credentials
nano .env  # or your preferred editor
```

### 3. Telegram Bot Setup (Optional)

1. Message [@BotFather](https://t.me/botfather) on Telegram
2. Send `/newbot` and follow instructions
3. Copy the bot token
4. Get your chat ID:
   - Send a message to your bot
   - Visit: `https://api.telegram.org/bot<YOUR_TOKEN>/getUpdates`
   - Find your chat ID in the response

### 4. Run

```bash
source .venv/bin/activate
./remocode.py
```

## Usage Modes 🎯

### Local-Only Mode

```bash
# Don't set TG_TOKEN in .env, or:
TG_TOKEN="" ./remocode.py
```

- Works exactly like normal Claude-Code CLI
- No Telegram integration
- Perfect for testing or when mobile access isn't needed

### Remote Mode

```bash
# With TG_TOKEN and TG_CHAT configured
./remocode.py
```

- Full dual-interface functionality
- Local terminal + Telegram control
- Real-time streaming to both interfaces

### With Specification File

```bash
./remocode.py path/to/spec.md
```

- Passes spec file to Claude-Code CLI
- Works in both local-only and remote modes

## Interface Behavior 🖥️📱

### Local Terminal

- **Always active**: Shows all Claude output in real-time
- **Direct input**: Type responses directly, press Enter
- **Menu handling**: Type option number when menu appears
- **Interruption**: Ctrl+C to exit cleanly

### Telegram Interface

- **Silent streaming**: Receives all output (no notifications)
- **Interactive menus**: Numbered options become buttons
- **Button selection**: Tap to choose, buttons auto-clear
- **Status updates**: Shows confirmations and selections

### Race Conditions

- **First wins**: Local keyboard OR Telegram button, whichever responds first
- **Confirmation**: Other interface shows what was selected
- **No conflicts**: System prevents double-selection

## Menu Detection 🔍

The system automatically detects menus using this pattern:

```
1) First option
2) Second option
3) Third option
```

**Requirements for detection:**

- At least 2 numbered options
- Format: `number) description`
- Each option on separate line

**Examples that work:**

```
Choose an option:
1) Create new file
2) Edit existing file
3) Delete file

What would you like to do?
  1) Start new conversation
  2) Continue previous conversation
  3) View history
```

**Examples that don't work:**

```
1. Using dots instead of parentheses
a) Using letters instead of numbers
1) Only one option
```

## Troubleshooting 🔧

### Common Issues

#### "ModuleNotFoundError: No module named 'pexpect'"

```bash
# Activate virtual environment first
source .venv/bin/activate
uv pip install -r requirements.txt
```

#### "Claude process failed to start"

```bash
# Check if Claude-Code CLI is available
npx @anthropic-ai/claude-code --help

# Check your Claude subscription
npx @anthropic-ai/claude-code config
```

#### "Telegram messages not sending"

```bash
# Test your bot token
curl -X GET "https://api.telegram.org/bot<YOUR_TOKEN>/getMe"

# Check chat ID format
# Should be a number like: 123456789
# Or for forum topics: -100123456789/456
```

#### "Menu not detected"

- Check `remocode.log` for debug info
- Verify menu format matches pattern
- Ensure at least 2 options present

#### "Permission denied"

```bash
chmod +x remocode.py setup.sh
```

### Debug Mode

```bash
# Enable verbose logging
export PYTHONPATH=.
python3 -c "
import logging
logging.basicConfig(level=logging.DEBUG)
exec(open('remocode.py').read())
"
```

### Log Analysis

```bash
# View recent logs
tail -f remocode.log

# Search for errors
grep -i error remocode.log

# Check menu detection
grep -i "menu" remocode.log
```

## Advanced Configuration ⚙️

### Environment Variables

```bash
# Custom Claude command
CLAUDE_CMD="claude --model sonnet --verbose" ./remocode.py

# Different chat for different projects
TG_CHAT=123456789 ./remocode.py project1.md
TG_CHAT=987654321 ./remocode.py project2.md

# Disable Telegram temporarily
TG_TOKEN="" ./remocode.py
```

### Multiple Instances

```bash
# Terminal 1 - Project A
TG_CHAT=chat_id_1 ./remocode.py specs/project_a.md

# Terminal 2 - Project B
TG_CHAT=chat_id_2 ./remocode.py specs/project_b.md
```

### Integration Examples

#### With GitHub Actions

```yaml
name: Remote Claude Development
on: [workflow_dispatch]
jobs:
  develop:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.9'
      - name: Install dependencies
        run: |
          pip install pexpect python-telegram-bot python-dotenv
      - name: Run Remote Claude
        run: ./remocode.py specification.md
        env:
          TG_TOKEN: ${{ secrets.TG_TOKEN }}
          TG_CHAT: ${{ secrets.TG_CHAT }}
```

#### With Docker

```dockerfile
FROM python:3.9-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
CMD ["./remocode.py"]
```

## Best Practices 💡

### Security

- Keep TG_TOKEN secret (use environment variables)
- Don't commit .env file to version control
- Use forum topics for team collaboration
- Consider bot permissions carefully

### Performance

- Use local-only mode for simple tasks
- Monitor log file size (auto-rotates at 500 lines)
- Close unused sessions to free resources

### Workflow

- Start with local-only mode for testing
- Use specification files for complex projects
- Keep Telegram chat organized with clear naming
- Use multiple chat IDs for different projects

## FAQ ❓

**Q: Can I use this with other CLI tools?**
A: The script is designed for Claude-Code CLI specifically, but the pattern could be adapted for other interactive CLI tools.

**Q: Does this work on Windows?**
A: The script uses Unix-specific features (pexpect, select). Windows support would require modifications.

**Q: Can multiple people control the same session?**
A: Currently no, but you could modify TG_CHAT to accept a group chat ID for collaborative control.

**Q: What happens if my internet connection drops?**
A: Local terminal continues working. Telegram reconnects automatically when connection is restored.

**Q: Can I save/resume sessions?**
A: This depends on Claude-Code CLI's session management. The wrapper preserves all functionality of the underlying CLI.

---

**Need help?** Check the logs, try the demo script, or review the troubleshooting section above.
