# Pexpect-based Remote Claude-Code Controller 🤖📱

A lightweight Python wrapper that enables remote control of Claude-Code CLI via Telegram using pseudo-terminal interception.

## Quick Start 🚀

```bash
# 1. Setup
cd .remocode/pexpect-wrapper
./setup.sh

# 2. Configure Telegram (optional)
cp .env.example .env
# Edit .env with your TG_TOKEN and TG_CHAT

# 3. Run
source .venv/bin/activate
./remocode.py
```

## Features ✨

- **Dual Interface**: Control from both local terminal AND Telegram
- **Menu Detection**: Converts numbered CLI menus into Telegram buttons
- **Real-time Streaming**: All output appears in both interfaces
- **Race Condition Handling**: First response (local OR remote) wins
- **Local Fallback**: Works without Telegram configuration

## Files

- `remocode.py` - Main 150-line wrapper script
- `setup.sh` - Automated installation
- `demo_remocode.py` - Interactive demo (no Telegram needed)
- `verify_installation.py` - System verification
- `test_menu_detection.py` - Unit tests

## Usage

### Local-only mode
```bash
TG_TOKEN="" ./remocode.py
```

### With specification file
```bash
./remocode.py path/to/spec.md
```

### Multiple instances
```bash
TG_CHAT=chat1 ./remocode.py project1.md &
TG_CHAT=chat2 ./remocode.py project2.md &
```

## How It Works

Uses `pexpect.spawn` to create a pseudo-terminal that intercepts Claude-Code CLI interactions, enabling dual control without modifying the CLI itself.

For detailed documentation, see:
- `REMOCODE_README.md` - Complete documentation
- `USAGE_GUIDE.md` - Detailed usage instructions
- `PROJECT_SUMMARY.md` - Technical implementation details
