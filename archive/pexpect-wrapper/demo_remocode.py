#!/usr/bin/env python3
"""
Demo script showing Remote Claude-Code Controller functionality
This simulates the interaction without requiring actual Telegram setup
"""

import asyncio
import sys
from typing import List, Dict, Any


def detect_menu(text: str) -> List[Dict[str, Any]]:
    """Detect numbered menu options in text"""
    import re

    menu_pattern = r"^\s*(\d+)\)\s*(.+?)$"
    matches = re.findall(menu_pattern, text, re.MULTILINE)

    if len(matches) < 2:
        return []

    return [
        {"number": int(num), "text": desc.strip()}
        for num, desc in matches
        if desc.strip()
    ]


async def simulate_telegram_keyboard(menu_items: List[Dict[str, Any]]):
    """Simulate Telegram inline keyboard"""
    print("\n📱 Telegram Interface:")
    print("🔢 Choose an option:")
    for item in menu_items:
        print(f"  [#{item['number']}] {item['text']}")
    print("  (Tap any button to select)")


async def demo_interaction():
    """Demonstrate the dual-interface interaction"""

    # Simulate Claude CLI output with menu
    claude_output = """
Welcome to Claude Code! What would you like to do?

1) Start a new coding project
2) Continue existing project
3) Review code changes
4) Run tests
5) Exit

Please choose an option (1-5):
"""

    print("🖥️  Local Terminal Output:")
    print(claude_output)

    # Detect menu
    menu_items = detect_menu(claude_output)

    if menu_items:
        print(f"✅ Menu detected with {len(menu_items)} options")

        # Show Telegram interface
        await simulate_telegram_keyboard(menu_items)

        print("\n⚡ Dual Input Mode Active:")
        print("  • Type choice in terminal OR")
        print("  • Tap button in Telegram")
        print("  • First response wins!")

        # Simulate user choice
        print("\n🎯 Simulating user selection...")
        await asyncio.sleep(1)

        choice = "2"  # Simulate choosing option 2
        print(f"✅ Choice made: #{choice}")
        print("📱 Telegram: Buttons cleared, showing '✅ Selected option #2'")
        print(f"🖥️  Terminal: Sending '{choice}' to Claude CLI")

        print(f"\n📤 Sent to Claude: {choice}")
        print("🔄 Claude continues with selected option...")

    else:
        print("ℹ️  No menu detected, streaming output normally")


async def demo_streaming():
    """Demonstrate real-time streaming"""
    print("\n" + "=" * 60)
    print("🌊 STREAMING DEMO")
    print("=" * 60)

    # Simulate Claude generating code
    code_chunks = [
        "Analyzing your request...\n",
        "Creating new Python file...\n",
        "```python\n",
        "def hello_world():\n",
        "    print('Hello, World!')\n",
        "```\n",
        "File created successfully!\n",
    ]

    print("🖥️  Local Terminal | 📱 Telegram")
    print("-" * 30 + "|" + "-" * 30)

    for chunk in code_chunks:
        print(f"{chunk.strip():<29} | {chunk.strip()}")
        await asyncio.sleep(0.5)

    print("\n✅ Both interfaces receive identical real-time output")


async def main():
    """Main demo function"""
    print("🚀 Remote Claude-Code Controller Demo")
    print("=" * 50)

    print("\n📋 This demo shows how the system works:")
    print("  1. Dual interface (local terminal + Telegram)")
    print("  2. Menu detection and button generation")
    print("  3. Real-time output streaming")
    print("  4. Race condition handling")

    input("\nPress Enter to start demo...")

    # Demo 1: Menu interaction
    await demo_interaction()

    input("\nPress Enter for streaming demo...")

    # Demo 2: Streaming
    await demo_streaming()

    print("\n" + "=" * 60)
    print("🎉 Demo Complete!")
    print("=" * 60)
    print("\nTo use the real system:")
    print("1. Run: ./setup.sh")
    print("2. Configure .env with Telegram credentials")
    print("3. Run: ./remocode.py")
    print("\nFor local-only mode, just run without TG_TOKEN")


if __name__ == "__main__":
    asyncio.run(main())
