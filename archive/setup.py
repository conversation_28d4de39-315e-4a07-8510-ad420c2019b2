#!/usr/bin/env python3
"""
RemoCode setup script for easy installation.
"""

import subprocess
import sys
import os
from pathlib import Path


def run_command(cmd: str, description: str) -> bool:
    """Run a command and return success status."""
    print(f"🔧 {description}...")
    try:
        result = subprocess.run(
            cmd, shell=True, check=True, capture_output=True, text=True
        )
        print(f"✅ {description} completed")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e.stderr}")
        return False


def main():
    """Main setup function."""
    print("🚀 RemoCode v2.0 Setup")
    print("=" * 40)

    # Check if we're in the right directory
    if not Path("src").exists() or not Path("pyproject.toml").exists():
        print("❌ Please run this script from the remocode directory")
        sys.exit(1)

    # Check for uv
    try:
        subprocess.run(["uv", "--version"], check=True, capture_output=True)
        print("✅ UV package manager found")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ UV not found. Please install UV first:")
        print("   curl -LsSf https://astral.sh/uv/install.sh | sh")
        sys.exit(1)

    # Setup virtual environment
    if not Path(".venv").exists():
        if not run_command("uv venv .venv", "Creating virtual environment"):
            sys.exit(1)
    else:
        print("✅ Virtual environment already exists")

    # Install dependencies
    if not run_command("uv pip install -r requirements.txt", "Installing dependencies"):
        sys.exit(1)

    # Create default config if it doesn't exist
    config_path = Path("config.json")
    if not config_path.exists():
        try:
            # Import and create default config
            sys.path.insert(0, str(Path("src")))
            from config import ConfigManager

            config_manager = ConfigManager(config_path)
            config_manager.save_default_config()
            print("✅ Created default config.json")
        except Exception as e:
            print(f"⚠️ Could not create default config: {e}")
    else:
        print("✅ Config file already exists")

    # Check environment variables
    print("\n📋 Environment Check:")
    if os.getenv("TG_TOKEN"):
        print("✅ TG_TOKEN is set")
    else:
        print("⚠️ TG_TOKEN not set (Telegram features will be disabled)")

    if os.getenv("TG_CHAT"):
        print("✅ TG_CHAT is set")
    else:
        print("⚠️ TG_CHAT not set (Telegram features will be disabled)")

    # Final instructions
    print("\n🎉 Setup Complete!")
    print("\nNext steps:")
    print("1. Activate virtual environment: source .venv/bin/activate")
    print("2. Set Telegram environment variables (optional):")
    print("   export TG_TOKEN=your_bot_token")
    print("   export TG_CHAT=your_chat_id")
    print("3. Test the installation: ./remocode status")
    print("4. Run RemoCode: ./remocode run")
    print("\nSee README.md for detailed usage instructions.")


if __name__ == "__main__":
    main()
