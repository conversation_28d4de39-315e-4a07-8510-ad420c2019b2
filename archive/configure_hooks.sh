#!/bin/bash
# RemoCode Hook Configuration Script
# Enables or disables Claude Code hooks for RemoCode integration

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

show_usage() {
    echo "Usage: $0 [enable|disable|status]"
    echo ""
    echo "Commands:"
    echo "  enable   - Enable RemoCode hooks (requires TG_TOKEN and TG_CHAT)"
    echo "  disable  - Disable RemoCode hooks (transparent mode)"
    echo "  status   - Show current hook status"
    echo ""
    echo "Environment Variables:"
    echo "  REMOCODE_ENABLED     - Set to 'true' to enable hooks"
    echo "  TG_TOKEN            - Telegram bot token (required when enabled)"
    echo "  TG_CHAT             - Telegram chat ID (required when enabled)"
    echo "  REMOCODE_LOG_PATH   - Log file path (default: /tmp/remocode.log)"
    echo "  REMOCODE_LIVE_MODE  - Enable live message aggregation (default: false)"
}

check_telegram_config() {
    if [[ -z "$TG_TOKEN" || -z "$TG_CHAT" ]]; then
        echo "Error: TG_TOKEN and TG_CHAT environment variables must be set to enable hooks"
        echo "Please set them in your shell profile or .env file:"
        echo "  export TG_TOKEN=your_bot_token"
        echo "  export TG_CHAT=your_chat_id"
        exit 1
    fi
}

show_status() {
    echo "RemoCode Hook Status:"
    echo "===================="

    if [[ "${REMOCODE_ENABLED,,}" == "true" ]]; then
        echo "Status: ENABLED ✅"
        echo "Hooks will intercept Claude Code operations and relay to Telegram"

        if [[ -n "$TG_TOKEN" && -n "$TG_CHAT" ]]; then
            echo "Telegram: CONFIGURED ✅"
        else
            echo "Telegram: NOT CONFIGURED ❌"
            echo "Set TG_TOKEN and TG_CHAT environment variables"
        fi
    else
        echo "Status: DISABLED ❌"
        echo "Hooks will be transparent (no interception)"
    fi

    echo ""
    echo "Environment Variables:"
    echo "  REMOCODE_ENABLED=${REMOCODE_ENABLED:-false}"
    echo "  TG_TOKEN=${TG_TOKEN:+[SET]}${TG_TOKEN:-[NOT SET]}"
    echo "  TG_CHAT=${TG_CHAT:+[SET]}${TG_CHAT:-[NOT SET]}"
    echo "  REMOCODE_LOG_PATH=${REMOCODE_LOG_PATH:-/tmp/remocode.log}"
    echo "  REMOCODE_LIVE_MODE=${REMOCODE_LIVE_MODE:-false}"

    echo ""
    echo "Hook Files:"
    for hook in pretool_guard.py notification_handler.py menu_detector.py; do
        if [[ -f "$SCRIPT_DIR/src/remocode/hooks/$hook" ]]; then
            echo "  $hook: EXISTS ✅"
        else
            echo "  $hook: MISSING ❌"
        fi
    done
}

enable_hooks() {
    echo "Enabling RemoCode hooks..."

    # Check if Telegram is configured
    check_telegram_config

    # Set environment variable for current session
    export REMOCODE_ENABLED=true

    echo "✅ RemoCode hooks enabled for this session"
    echo ""
    echo "To make this permanent, add to your shell profile (.bashrc, .zshrc, etc.):"
    echo "  export REMOCODE_ENABLED=true"
    echo "  export TG_TOKEN=$TG_TOKEN"
    echo "  export TG_CHAT=$TG_CHAT"
    echo ""
    echo "Or create a .env file in your project root with these variables."
}

disable_hooks() {
    echo "Disabling RemoCode hooks..."

    # Unset environment variable for current session
    export REMOCODE_ENABLED=false

    echo "✅ RemoCode hooks disabled for this session"
    echo "Hooks will now be transparent and not intercept Claude Code operations"
    echo ""
    echo "To make this permanent, remove or set to false in your shell profile:"
    echo "  export REMOCODE_ENABLED=false"
}

case "${1:-}" in
    enable)
        enable_hooks
        ;;
    disable)
        disable_hooks
        ;;
    status)
        show_status
        ;;
    -h|--help|help)
        show_usage
        ;;
    "")
        echo "Error: No command specified"
        echo ""
        show_usage
        exit 1
        ;;
    *)
        echo "Error: Unknown command '$1'"
        echo ""
        show_usage
        exit 1
        ;;
esac
