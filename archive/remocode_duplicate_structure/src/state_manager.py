"""
State management for RemoCode sessions.

Handles persistence of session state, log buffers, and recovery
capabilities for seamless restarts.
"""

import json
import time
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from collections import deque


logger = logging.getLogger(__name__)


@dataclass
class SessionState:
    """Represents the state of a RemoCode session."""

    session_id: str
    start_time: float
    last_activity: float
    claude_pid: Optional[int] = None
    telegram_chat_id: Optional[str] = None
    current_menu: Optional[Dict[str, Any]] = None
    log_buffer: List[str] = None

    def __post_init__(self):
        """Initialize default values after dataclass creation."""
        if self.log_buffer is None:
            self.log_buffer = []

    @property
    def age_seconds(self) -> float:
        """Get session age in seconds."""
        return time.time() - self.start_time

    @property
    def idle_seconds(self) -> float:
        """Get time since last activity in seconds."""
        return time.time() - self.last_activity

    def update_activity(self) -> None:
        """Update last activity timestamp."""
        self.last_activity = time.time()

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        return asdict(self)

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "SessionState":
        """Create SessionState from dictionary."""
        return cls(**data)


class StateManager:
    """Manages session state persistence and recovery."""

    def __init__(self, state_file: Optional[Path] = None, max_log_lines: int = 500):
        """
        Initialize state manager.

        Args:
            state_file: Path to state file (defaults to ./state.json)
            max_log_lines: Maximum number of log lines to keep in buffer
        """
        self.state_file = state_file or Path("state.json")
        self.max_log_lines = max_log_lines
        self.current_state: Optional[SessionState] = None
        self.log_buffer: deque = deque(maxlen=max_log_lines)
        self._auto_save = True

    def create_new_session(
        self, session_id: str, telegram_chat_id: Optional[str] = None
    ) -> SessionState:
        """
        Create a new session state.

        Args:
            session_id: Unique identifier for the session
            telegram_chat_id: Optional Telegram chat ID

        Returns:
            New SessionState instance
        """
        current_time = time.time()
        self.current_state = SessionState(
            session_id=session_id,
            start_time=current_time,
            last_activity=current_time,
            telegram_chat_id=telegram_chat_id
            # Note: Removed log_buffer duplication - use only deque and serialize on save
        )

        logger.info(f"Created new session: {session_id}")
        if self._auto_save:
            self.save_state()

        return self.current_state

    def load_state(self) -> Optional[SessionState]:
        """
        Load session state from file.

        Returns:
            Loaded SessionState or None if no valid state found
        """
        if not self.state_file.exists():
            logger.info("No existing state file found")
            return None

        try:
            with open(self.state_file, "r") as f:
                data = json.load(f)

            self.current_state = SessionState.from_dict(data)

            # Restore log buffer from loaded state to deque, then clear from state to avoid duplication
            if self.current_state.log_buffer:
                self.log_buffer.extend(
                    self.current_state.log_buffer[-self.max_log_lines :]
                )
                self.current_state.log_buffer = []  # Clear to avoid runtime duplication

            logger.info(f"Loaded session state: {self.current_state.session_id}")
            logger.info(
                f"Session age: {self.current_state.age_seconds:.1f}s, "
                f"idle: {self.current_state.idle_seconds:.1f}s"
            )

            return self.current_state

        except (json.JSONDecodeError, KeyError, ValueError) as e:
            logger.error(f"Failed to load state from {self.state_file}: {e}")
            return None

    def save_state(self) -> bool:
        """
        Save current session state to file.

        Returns:
            True if saved successfully, False otherwise
        """
        if self.current_state is None:
            logger.warning("No current state to save")
            return False

        try:
            # Serialize current deque into state for saving (avoiding duplication during runtime)
            state_dict = self.current_state.to_dict()
            state_dict["log_buffer"] = list(self.log_buffer)

            # Ensure parent directory exists
            self.state_file.parent.mkdir(parents=True, exist_ok=True)

            # Write state to file
            with open(self.state_file, "w") as f:
                json.dump(state_dict, f, indent=2)

            logger.debug(f"Saved state to {self.state_file}")
            return True

        except (OSError, json.JSONEncodeError) as e:
            logger.error(f"Failed to save state to {self.state_file}: {e}")
            return False

    def add_log_line(self, line: str) -> None:
        """
        Add a line to the log buffer.

        Args:
            line: Log line to add
        """
        timestamp = time.time()
        timestamped_line = f"[{timestamp:.3f}] {line}"
        self.log_buffer.append(timestamped_line)

        if self.current_state:
            self.current_state.update_activity()

    def get_recent_logs(self, lines: int = 50) -> List[str]:
        """
        Get recent log lines.

        Args:
            lines: Number of recent lines to return

        Returns:
            List of recent log lines
        """
        return list(self.log_buffer)[-lines:]

    def set_claude_pid(self, pid: int) -> None:
        """Set the Claude process PID."""
        if self.current_state:
            self.current_state.claude_pid = pid
            self.current_state.update_activity()
            if self._auto_save:
                self.save_state()

    def set_current_menu(self, menu_data: Optional[Dict[str, Any]]) -> None:
        """Set current menu state."""
        if self.current_state:
            self.current_state.current_menu = menu_data
            self.current_state.update_activity()
            if self._auto_save:
                self.save_state()

    def clear_current_menu(self) -> None:
        """Clear current menu state."""
        self.set_current_menu(None)

    def cleanup_old_sessions(self, max_age_hours: int = 24) -> None:
        """
        Clean up old session files.

        Args:
            max_age_hours: Maximum age for session files in hours
        """
        if not self.state_file.exists():
            return

        try:
            file_age = time.time() - self.state_file.stat().st_mtime
            if file_age > (max_age_hours * 3600):
                logger.info(f"Cleaning up old state file (age: {file_age/3600:.1f}h)")
                self.state_file.unlink()
        except OSError as e:
            logger.error(f"Failed to cleanup old state file: {e}")

    def set_auto_save(self, enabled: bool) -> None:
        """Enable/disable automatic state saving."""
        self._auto_save = enabled
        logger.debug(f"Auto-save {'enabled' if enabled else 'disabled'}")

    def force_save(self) -> bool:
        """Force save current state regardless of auto-save setting."""
        return self.save_state()

    def get_summary(self) -> Dict[str, Any]:
        """Get session summary information."""
        if not self.current_state:
            return {"status": "no_session"}

        return {
            "status": "active",
            "session_id": self.current_state.session_id,
            "age_seconds": self.current_state.age_seconds,
            "idle_seconds": self.current_state.idle_seconds,
            "log_lines": len(self.log_buffer),
            "has_telegram": bool(self.current_state.telegram_chat_id),
            "has_menu": bool(self.current_state.current_menu),
            "claude_pid": self.current_state.claude_pid,
        }


class LogRotator:
    """Handles log file rotation and cleanup."""

    def __init__(self, log_file: Path, max_lines: int = 500):
        """Initialize log rotator."""
        self.log_file = log_file
        self.max_lines = max_lines

    def rotate_if_needed(self) -> bool:
        """
        Rotate log file if it exceeds maximum lines.

        Returns:
            True if rotation occurred, False otherwise
        """
        if not self.log_file.exists():
            return False

        try:
            with open(self.log_file, "r") as f:
                lines = f.readlines()

            if len(lines) <= self.max_lines:
                return False

            # Keep only the most recent lines
            recent_lines = lines[-self.max_lines :]

            with open(self.log_file, "w") as f:
                f.writelines(recent_lines)

            logger.info(f"Rotated {self.log_file}, kept {len(recent_lines)} lines")
            return True

        except OSError as e:
            logger.error(f"Failed to rotate log file {self.log_file}: {e}")
            return False
