#!/usr/bin/env python3
"""
RemoCode v2 launcher script.

This script launches the hook-based RemoCode v2 implementation
with proper UV environment and package imports.
"""

import sys
import os
from pathlib import Path

# Ensure the parent directory is on the path for package imports
script_dir = Path(__file__).parent
sys.path.insert(0, str(script_dir))

# Import and run main application from the new package structure
try:
    from src.remocode.main import app
    app()
except ImportError as e:
    print(f"❌ Failed to import RemoCode v2 modules: {e}")
    print("💡 Make sure you've installed dependencies: uv sync")
    print("💡 Run with: uv run remocode")
    sys.exit(1)
except Exception as e:
    print(f"❌ Fatal error: {e}")
    sys.exit(1)
