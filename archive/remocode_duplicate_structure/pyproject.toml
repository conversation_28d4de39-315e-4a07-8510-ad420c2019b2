[project]
name = "remocode"
version = "2.0.0"
description = "Remote Claude Code Control via Telegram - Clean asyncio implementation"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "python-telegram-bot>=21.0.1",
    "python-dotenv>=1.0.0",
    "asyncio-subprocess>=0.1.0",
    "pydantic>=2.0.0",
    "rich>=13.0.0",
    "typer>=0.12.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=8.0.0",
    "pytest-asyncio>=0.23.0",
    "black>=24.0.0",
    "isort>=5.13.0",
    "mypy>=1.8.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.black]
line-length = 88
target-version = ['py311']

[tool.isort]
profile = "black"
line_length = 88

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
