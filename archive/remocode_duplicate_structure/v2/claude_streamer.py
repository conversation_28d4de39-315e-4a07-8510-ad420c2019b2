"""
claude_streamer.py – minimal, stateless wrapper around the Claude Code CLI that relies solely on Anthropic hooks.
"""

from __future__ import annotations

import asyncio
import logging
import os
import pty
import re
import shlex
import signal
import sys
from typing import Awaitable, Callable, List, Optional

logger = logging.getLogger(__name__)


class ClaudeStreamer:
    """Lightweight replacement for the v1 ClaudeWrapper."""

    def __init__(
        self,
        claude_cmd: str = "npx @anthropic-ai/claude-code",
        hook_path: str | os.PathLike | None = None,
        on_output: Optional[Callable[[str], Awaitable[None]]] = None,
        env: Optional[dict[str, str]] = None,
    ) -> None:
        self.claude_cmd = claude_cmd
        self.hook_path = str(hook_path) if hook_path else None
        self.on_output = on_output or (lambda _line: asyncio.sleep(0))
        self._env = env or os.environ.copy()

        self._proc: Optional[asyncio.subprocess.Process] = None
        self._master_fd: Optional[int] = None
        self._reader: Optional[asyncio.StreamReader] = None
        self._tasks: list[asyncio.Task] = []
        self.running: bool = False

        # Terminal state management to prevent excessive refreshing
        self._output_buffer: str = ""
        self._last_cursor_pos: Optional[tuple[int, int]] = None
        self._screen_cleared_recently: bool = False

    # ------------------------------------------------------------------ #
    # Public API
    # ------------------------------------------------------------------ #

    async def start(self, extra_args: Optional[List[str]] = None) -> bool:
        """Launch Claude CLI inside a pseudo-terminal so it stays interactive."""
        if self.running:
            logger.warning("ClaudeStreamer already running.")
            return False

        cmd_parts: list[str] = shlex.split(self.claude_cmd)
        if extra_args:
            cmd_parts += extra_args

        logger.info("Launching Claude CLI: %s", " ".join(cmd_parts))

        # open PTY
        master_fd, slave_fd = pty.openpty()
        self._master_fd = master_fd

        # Ensure colors
        env = self._env.copy()
        env.setdefault("TERM", "xterm-256color")
        env.setdefault("FORCE_COLOR", "1")

        try:
            self._proc = await asyncio.create_subprocess_exec(
                *cmd_parts,
                stdin=slave_fd,
                stdout=slave_fd,
                stderr=slave_fd,
                env=env,
                preexec_fn=os.setsid,
            )
        except FileNotFoundError as exc:
            os.close(master_fd)
            os.close(slave_fd)
            logger.error("Could not start Claude CLI (%s)", exc)
            return False

        # parent closes slave
        os.close(slave_fd)

        loop = asyncio.get_running_loop()
        # Reader from master fd
        self._reader = asyncio.StreamReader()
        protocol = asyncio.StreamReaderProtocol(self._reader)
        await loop.connect_read_pipe(
            lambda: protocol, os.fdopen(master_fd, "rb", buffering=0)
        )

        self.running = True

        self._tasks = [
            asyncio.create_task(self._read_master()),
            # Note: Removed _pipe_stdin() to avoid interfering with Claude Code's input handling
            # Input coordination is now handled by the menu_coordinator in main.py
            asyncio.create_task(self._monitor_exit()),
        ]

        return True

    async def stop(self) -> None:
        """Terminate CLI and cancel background tasks."""
        if not self.running:
            return

        assert self._proc
        if self._proc.returncode is None:
            self._proc.send_signal(signal.SIGINT)
            try:
                await asyncio.wait_for(self._proc.wait(), 3)
            except asyncio.TimeoutError:
                logger.warning("Force-killing Claude CLI.")
                self._proc.kill()

        for task in self._tasks:
            task.cancel()
        self._tasks.clear()
        if self._master_fd is not None:
            try:
                os.close(self._master_fd)
            except OSError:
                pass
            self._master_fd = None
        self.running = False
        logger.info("ClaudeStreamer stopped.")

    async def send_input(self, text: str) -> None:
        """Forward raw text to Claude’s stdin."""
        if self.running and self._proc and self._proc.stdin:
            self._proc.stdin.write(text.encode())
            await self._proc.stdin.drain()

    # ------------------------------------------------------------------ #
    # Internal helpers
    # ------------------------------------------------------------------ #

    async def _read_master(self) -> None:
        """Read from PTY master and forward to on_output with terminal state management."""
        while self.running and not self._reader.at_eof():
            data = await self._reader.read(1024)  # Read in chunks instead of lines
            if not data:
                break
            try:
                # Decode and buffer the data
                text = data.decode(errors="ignore")
                self._output_buffer += text

                # Process buffered output and send stable content
                processed_output = self._process_terminal_output()
                if processed_output:
                    await self.on_output(processed_output)
            except Exception:
                logger.exception("on_output callback error")

    # NOTE: _pipe_stdin removed to avoid conflicts with Claude Code's input handling
    # Input coordination is now handled by menu_coordinator when needed

    async def _monitor_exit(self) -> None:
        """Wait for the subprocess exit and then stop self."""
        if self._proc is None:
            return
        await self._proc.wait()
        logger.info("Claude CLI exited with code %s", self._proc.returncode)
        await self.stop()

    def _process_terminal_output(self) -> str:
        """Process buffered terminal output to prevent excessive refreshing."""
        if not self._output_buffer:
            return ""

        # ANSI escape sequence patterns that cause screen clearing/refreshing
        screen_clear_patterns = [
            r"\x1b\[2J",  # Clear entire screen
            r"\x1b\[3J",  # Clear entire screen including scrollback
            r"\x1b\[H",  # Move cursor to home position
            r"\x1b\[\d*;\d*H",  # Move cursor to specific position
        ]

        # Check if this output contains problematic sequences
        has_clear_sequence = any(
            re.search(pattern, self._output_buffer) for pattern in screen_clear_patterns
        )

        if has_clear_sequence:
            # If we just cleared the screen recently, suppress rapid clearing
            if self._screen_cleared_recently:
                # Filter out excessive clear sequences but keep content
                filtered_output = self._output_buffer
                for pattern in screen_clear_patterns:
                    filtered_output = re.sub(pattern, "", filtered_output)

                self._output_buffer = ""
                return filtered_output.strip()
            else:
                # Allow the first clear but mark it
                self._screen_cleared_recently = True
                # Reset the flag after a short delay (will be handled in next call)
                asyncio.create_task(self._reset_clear_flag())
        else:
            # Reset flag for non-clearing output
            self._screen_cleared_recently = False

        # For normal output, process line by line
        lines = self._output_buffer.split("\n")
        # Keep the last incomplete line in buffer
        if lines and not self._output_buffer.endswith("\n"):
            self._output_buffer = lines[-1]
            lines = lines[:-1]
        else:
            self._output_buffer = ""

        # Return complete lines
        return "\n".join(lines) + ("\n" if lines else "")

    async def _reset_clear_flag(self) -> None:
        """Reset the screen clear flag after a brief delay."""
        await asyncio.sleep(0.5)  # 500ms delay
        self._screen_cleared_recently = False
