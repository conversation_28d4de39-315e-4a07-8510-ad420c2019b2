#!/usr/bin/env python3
"""
Test script for RemoCode v2 improved hooks system
Tests the notification vs permission logic and JSON parsing fixes
"""

import json
import os
import subprocess
import sys
from pathlib import Path
from typing import Dict, Any


def test_pretool_hook(
    tool_name: str, tool_input: Dict[str, Any], expected_decision: str = "approve"
) -> bool:
    """Test the PreToolUse hook with given input"""
    print(f"\n🧪 Testing PreToolUse hook: {tool_name}")

    # Create test payload according to Claude Code hook schema
    test_payload = {
        "session_id": "test-session-123",
        "transcript_path": "/tmp/test-transcript.jsonl",
        "cwd": str(Path.cwd()),
        "hook_event_name": "PreToolUse",
        "tool_name": tool_name,
        "tool_input": tool_input,
    }

    # Run the hook
    hook_path = Path(__file__).parent / "hooks" / "pretool_guard.py"
    try:
        result = subprocess.run(
            ["python3", str(hook_path)],
            input=json.dumps(test_payload),
            text=True,
            capture_output=True,
            timeout=10,
        )

        print(f"   Return code: {result.returncode}")
        print(f"   Stdout: {result.stdout.strip()}")
        if result.stderr:
            print(f"   Stderr: {result.stderr.strip()}")

        # Parse the JSON response
        if result.stdout.strip():
            try:
                response = json.loads(result.stdout.strip())
                decision = response.get("decision", "unknown")
                print(f"   Decision: {decision}")

                if decision == expected_decision:
                    print("   ✅ Test passed")
                    return True
                else:
                    print(
                        f"   ❌ Test failed: expected {expected_decision}, got {decision}"
                    )
                    return False
            except json.JSONDecodeError:
                print(f"   ❌ Invalid JSON response: {result.stdout}")
                return False
        else:
            print("   ❌ No output from hook")
            return False

    except subprocess.TimeoutExpired:
        print("   ❌ Hook timed out")
        return False
    except Exception as e:
        print(f"   ❌ Error running hook: {e}")
        return False


def test_notification_hook(message: str) -> bool:
    """Test the Notification hook with given message"""
    print(f"\n🧪 Testing Notification hook: {message[:50]}...")

    # Create test payload according to Claude Code hook schema
    test_payload = {
        "session_id": "test-session-123",
        "transcript_path": "/tmp/test-transcript.jsonl",
        "cwd": str(Path.cwd()),
        "hook_event_name": "Notification",
        "message": message,
    }

    # Run the hook
    hook_path = Path(__file__).parent / "hooks" / "notification_handler.py"
    try:
        result = subprocess.run(
            ["python3", str(hook_path)],
            input=json.dumps(test_payload),
            text=True,
            capture_output=True,
            timeout=5,  # Shorter timeout for notifications
        )

        print(f"   Return code: {result.returncode}")
        print(f"   Stdout: {result.stdout.strip()}")
        if result.stderr:
            print(f"   Stderr: {result.stderr.strip()}")

        # For notifications, success is just not crashing
        if result.returncode == 0:
            print("   ✅ Test passed")
            return True
        else:
            print("   ❌ Test failed")
            return False

    except subprocess.TimeoutExpired:
        print("   ❌ Hook timed out")
        return False
    except Exception as e:
        print(f"   ❌ Error running hook: {e}")
        return False


def test_stop_hook(stop_reason: str = "completion") -> bool:
    """Test the Stop hook with given stop reason"""
    print(f"\n🧪 Testing Stop hook: {stop_reason}")

    # Create test payload according to Claude Code hook schema
    test_payload = {
        "session_id": "test-session-123",
        "transcript_path": "/tmp/test-transcript.jsonl",
        "cwd": str(Path.cwd()),
        "hook_event_name": "Stop",
        "stop_hook_active": False,
    }

    # Create a mock transcript file for testing
    transcript_content = ""
    if stop_reason == "usage_limit":
        transcript_content = "Claude usage limit reached. Your limit will reset at 1am"
    elif stop_reason == "error":
        transcript_content = "Error: Connection failed"
    else:
        transcript_content = "Task completed successfully"

    try:
        with open("/tmp/test-transcript.jsonl", "w") as f:
            f.write(transcript_content)
    except:
        pass

    # Run the hook
    hook_path = Path(__file__).parent / "hooks" / "stop_handler.py"
    try:
        result = subprocess.run(
            ["python3", str(hook_path)],
            input=json.dumps(test_payload),
            text=True,
            capture_output=True,
            timeout=5,
        )

        print(f"   Return code: {result.returncode}")
        print(f"   Stdout: {result.stdout.strip()}")
        if result.stderr:
            print(f"   Stderr: {result.stderr.strip()}")

        # For stop hooks, success is just not crashing
        if result.returncode == 0:
            print("   ✅ Test passed")
            return True
        else:
            print("   ❌ Test failed")
            return False

    except subprocess.TimeoutExpired:
        print("   ❌ Hook timed out")
        return False
    except Exception as e:
        print(f"   ❌ Error running hook: {e}")
        return False
    finally:
        # Clean up test file
        try:
            os.remove("/tmp/test-transcript.jsonl")
        except:
            pass


def main():
    """Run all tests"""
    print("🚀 RemoCode v2 Enhanced Hook System Tests")
    print("=" * 50)

    tests_passed = 0
    total_tests = 0

    # Test cases for PreToolUse hook (including enhanced tool summaries)
    pretool_tests = [
        # Safe tools should be auto-approved
        ("Read", {"file_path": "/tmp/test.txt"}, "approve"),
        ("Write", {"file_path": "/tmp/test.txt", "content": "test content"}, "approve"),
        ("WebFetch", {"url": "https://example.com"}, "approve"),
        ("Glob", {"pattern": "*.py", "include_hidden": True}, "approve"),
        # Safe bash commands should be auto-approved
        ("Bash", {"command": "ls -la"}, "approve"),
        ("Bash", {"command": "npm run build"}, "approve"),
        ("Bash", {"command": "git status"}, "approve"),
        # Unknown tools should be approved (new philosophy)
        ("UnknownTool", {"param": "value"}, "approve"),
        # MCP tools should be approved
        ("mcp__filesystem__read_file", {"path": "/tmp/test.txt"}, "approve"),
    ]

    for tool_name, tool_input, expected in pretool_tests:
        total_tests += 1
        if test_pretool_hook(tool_name, tool_input, expected):
            tests_passed += 1

    # Test cases for Notification hook
    notification_tests = [
        "Claude needs your permission to use Bash",
        "Claude is waiting for your input",
        "Would you like to proceed with the plan?",
        "Generic notification message",
    ]

    for message in notification_tests:
        total_tests += 1
        if test_notification_hook(message):
            tests_passed += 1

    # Test cases for Stop hook
    stop_tests = ["completion", "usage_limit", "error"]

    for stop_reason in stop_tests:
        total_tests += 1
        if test_stop_hook(stop_reason):
            tests_passed += 1

    # Summary
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {tests_passed}/{total_tests} passed")

    if tests_passed == total_tests:
        print("🎉 All tests passed!")
        return 0
    else:
        print("❌ Some tests failed")
        return 1


if __name__ == "__main__":
    sys.exit(main())
