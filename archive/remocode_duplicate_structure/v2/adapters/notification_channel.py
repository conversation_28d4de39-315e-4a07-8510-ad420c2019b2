#!/usr/bin/env python3
"""notification_channel.py – pluggable outbound-message channel abstraction.

This deliberately lightweight interface lets RemoCode v2 deliver messages
(to ask for permission, send pings, etc.) through **any** chat service
without rewriting hook logic.  The default implementation wraps Telegram's
HTTP Bot API, but future adapters (Slack, WhatsApp, email) can be added by
conforming to the same protocol.

We use composition: hooks depend only on the *capabilities*, not the concrete
class.  No inheritance tree is required; a simple *duck-typed* object is
fine.  For static checking we provide a ``Protocol``.
"""

from __future__ import annotations

import json
import os
import urllib.request
from pathlib import Path
from typing import Any, Dict, List, Optional, Protocol


class NotificationChannel(Protocol):
    """Minimal surface area RemoCode hooks rely on."""

    def send_message(self, text: str, parse_mode: str = "Markdown", **kw) -> int:
        ...

    def edit_message(
        self, message_id: int, text: str, parse_mode: str = "Markdown", **kw
    ) -> None:
        ...

    def delete_message(self, message_id: int) -> None:
        ...

    def send_document(self, file_path: str, caption: str | None = None) -> None:
        ...

    def answer_callback(self, callback_id: str, text: str | None = None) -> None:
        ...

    def get_updates(self, offset: int = 0, timeout: int = 10) -> List[Dict[str, Any]]:
        ...


# ---------------------------------------------------------------------------
# Telegram implementation (HTTP; no external deps) – default
# ---------------------------------------------------------------------------


class TelegramChannel:
    """Thin wrapper around Telegram Bot API using only stdlib."""

    def __init__(self, token: str, chat_id: str):
        self.token = token
        self.chat_id = chat_id
        self._api_root = f"https://api.telegram.org/bot{token}"

    # --- core helper ------------------------------------------------------
    def _call(self, method: str, **params):
        url = f"{self._api_root}/{method}"
        data = json.dumps(params).encode()
        req = urllib.request.Request(
            url, data=data, headers={"Content-Type": "application/json"}
        )
        with urllib.request.urlopen(req, timeout=10) as resp:
            return json.loads(resp.read().decode())

    # --- NotificationChannel interface -----------------------------------
    def send_message(self, text: str, parse_mode: str = "Markdown", **kw) -> int:
        res = self._call(
            "sendMessage", chat_id=self.chat_id, text=text, parse_mode=parse_mode, **kw
        )
        return res["result"]["message_id"]

    def edit_message(
        self, message_id: int, text: str, parse_mode: str = "Markdown", **kw
    ) -> None:
        self._call(
            "editMessageText",
            chat_id=self.chat_id,
            message_id=message_id,
            text=text,
            parse_mode=parse_mode,
            **kw,
        )

    def delete_message(self, message_id: int) -> None:
        try:
            self._call("deleteMessage", chat_id=self.chat_id, message_id=message_id)
        except Exception:
            pass

    def send_document(self, file_path: str, caption: str | None = None) -> None:
        # Use multipart/form-data for files; easiest is to shell out to curl
        import subprocess, shlex, tempfile

        cmd = [
            "curl",
            "-s",
            f"https://api.telegram.org/bot{self.token}/sendDocument",
            "-F",
            f"chat_id={self.chat_id}",
            "-F",
            f"document=@{file_path}",
        ]
        if caption:
            cmd += ["-F", f"caption={caption}"]
        subprocess.run(cmd, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)

    def answer_callback(self, callback_id: str, text: str | None = None) -> None:
        self._call(
            "answerCallbackQuery", callback_query_id=callback_id, text=text or ""
        )

    def get_updates(self, offset: int = 0, timeout: int = 10) -> List[Dict[str, Any]]:
        res = self._call("getUpdates", offset=offset, timeout=timeout)
        return res["result"]


# ---------------------------------------------------------------------------
# Factory helper – chooses channel based on env / config
# ---------------------------------------------------------------------------

_DEFAULT_CHANNEL: NotificationChannel | None = None


def get_notification_channel() -> NotificationChannel:
    global _DEFAULT_CHANNEL
    if _DEFAULT_CHANNEL is not None:
        return _DEFAULT_CHANNEL

    tg_token = os.getenv("TG_TOKEN")
    tg_chat = os.getenv("TG_CHAT")
    if not tg_token or not tg_chat:
        raise RuntimeError(
            "Telegram environment variables TG_TOKEN / TG_CHAT not set; cannot create default NotificationChannel"
        )

    _DEFAULT_CHANNEL = TelegramChannel(tg_token, tg_chat)
    return _DEFAULT_CHANNEL


# ---------------------------------------------------------------------------
# Convenience helpers so legacy hook code can migrate gradually ------------
# ---------------------------------------------------------------------------


def send_message(text: str, **kw) -> int:  # noqa: D401
    """Thin wrapper calling *default* channel’s send_message."""
    return get_notification_channel().send_message(text, **kw)


def edit_message(message_id: int, text: str, **kw) -> None:
    get_notification_channel().edit_message(message_id, text, **kw)


def delete_message(message_id: int) -> None:
    get_notification_channel().delete_message(message_id)


def send_document(path: str, caption: str | None = None) -> None:
    get_notification_channel().send_document(path, caption)


def answer_callback(callback_id: str, text: str | None = None) -> None:
    get_notification_channel().answer_callback(callback_id, text)


def get_updates(offset: int = 0, timeout: int = 10) -> List[Dict[str, Any]]:
    return get_notification_channel().get_updates(offset, timeout)
