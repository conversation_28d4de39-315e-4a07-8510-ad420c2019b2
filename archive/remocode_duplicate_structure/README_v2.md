# RemoCode v2 – Hook-based runner

This edition of RemoCode abandons the brittle screen-scraping logic and
relies exclusively on <PERSON>’s _hook_ API. It co-exists with the
original implementation:

- **v1** – `src/…` folder (unchanged)
- **v2** – `remocode/v2/…`

Run it with:

```bash
python -m remocode.v2.main run -- --model gpt-4o-mini
```

Anything after `--` is passed straight to `claude-code`.
