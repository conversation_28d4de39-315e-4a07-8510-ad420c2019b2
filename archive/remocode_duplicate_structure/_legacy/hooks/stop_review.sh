#!/usr/bin/env bash
# Load secrets from project-local .env so we can post results to Telegram
ENV_PATH="$(dirname "$(dirname "${BASH_SOURCE[0]}")")/../.env"
if [[ -f "$ENV_PATH" ]]; then
  # shellcheck disable=SC2046
  export $(grep -v '^#' "$ENV_PATH" | xargs -d '\n')
fi

TG_API="https://api.telegram.org/bot${TG_TOKEN}"
CHAT_ID="${TG_UID}"

# Run tests & build -----------------------------------------------------------
set -euo pipefail
npm run test --silent || { echo "tests fail" >&2; [[ -n "$TG_TOKEN" ]] && curl -s -X POST "$TG_API/sendMessage" -d chat_id="$CHAT_ID" -d text="🚨 Tests failed"; exit 2; }
npm run build --silent || { echo "build fail" >&2; [[ -n "$TG_TOKEN" ]] && curl -s -X POST "$TG_API/sendMessage" -d chat_id="$CHAT_ID" -d text="🚨 Build failed"; exit 2; }

# Create diff against merge-base --------------------------------------------
DIFF=$(git diff -U0 $(git merge-base main HEAD)..HEAD)

# Ask Gemini to review --------------------------------------------------------
# We inline the diff in the prompt. The 1M-token context window easily handles this.
PROMPT="You are an expert software reviewer. Carefully review the following git diff and write a concise plain-text report listing:\n1. Critical issues\n2. Improvement suggestions\n3. Praise for well-done parts.\nUse bullet points.\n\nGit diff begins:\n$DIFF\n\nEnd of diff."
REVIEW_OUTPUT=$(gemini -p "$PROMPT")
echo "$REVIEW_OUTPUT" > /tmp/gem.out

# Send results to Telegram ----------------------------------------------------
if [[ -n "$TG_TOKEN" ]]; then
  # Send review summary (trim if too long)
  SUMMARY=$(echo "$REVIEW_OUTPUT" | head -c 3500) # Telegram max 4096, keep margin
  curl -s -X POST "$TG_API/sendMessage" \
    -d chat_id="$CHAT_ID" \
    --data-urlencode text="📋 <b>Gemini review</b>%0A<pre>${SUMMARY}</pre>" \
    -d parse_mode=HTML > /dev/null

  # Send diff as a document to avoid length limits
  printf '%s\n' "$DIFF" > /tmp/changes.patch
  curl -s -F document=@/tmp/changes.patch -F chat_id="$CHAT_ID" "$TG_API/sendDocument" > /dev/null
fi

# Block merge if Gemini produced a BLOCK marker -----------------------------
if grep -Eq '\[BLOCK\]|^BLOCK:' /tmp/gem.out; then
  echo "Gemini blocked the change" >&2
  exit 2
fi

exit 0
