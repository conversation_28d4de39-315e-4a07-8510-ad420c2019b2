#!/usr/bin/env python3
"""
Verification script for Remote Claude-Code Controller
Checks all components and dependencies
"""

import os
import sys
import subprocess
import importlib.util


def check_file_exists(filepath, description):
    """Check if a file exists"""
    if os.path.exists(filepath):
        print(f"✅ {description}: {filepath}")
        return True
    else:
        print(f"❌ {description}: {filepath} (missing)")
        return False


def check_python_module(module_name):
    """Check if a Python module can be imported"""
    try:
        spec = importlib.util.find_spec(module_name)
        if spec is not None:
            print(f"✅ Python module: {module_name}")
            return True
        else:
            print(f"❌ Python module: {module_name} (not found)")
            return False
    except ImportError:
        print(f"❌ Python module: {module_name} (import error)")
        return False


def check_command(command, description):
    """Check if a command is available"""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ {description}: Available")
            return True
        else:
            print(f"❌ {description}: Not available or error")
            return False
    except Exception as e:
        print(f"❌ {description}: Error - {e}")
        return False


def check_env_file():
    """Check environment configuration"""
    if os.path.exists(".env"):
        print("✅ Environment file: .env exists")
        with open(".env", "r") as f:
            content = f.read()
            if "TG_TOKEN" in content:
                print("  📱 Telegram token configured")
            else:
                print("  ⚠️  Telegram token not configured (local-only mode)")
            if "TG_CHAT" in content:
                print("  💬 Telegram chat configured")
            else:
                print("  ⚠️  Telegram chat not configured")
        return True
    else:
        print("⚠️  Environment file: .env not found (will use defaults)")
        return False


def main():
    """Main verification function"""
    print("🔍 Remote Claude-Code Controller - Installation Verification")
    print("=" * 60)

    all_good = True

    # Check core files
    print("\n📁 Core Files:")
    files_to_check = [
        ("remocode.py", "Main script"),
        ("requirements.txt", "Python dependencies"),
        ("setup.sh", "Setup script"),
        (".env.example", "Environment template"),
        ("REMOCODE_README.md", "Documentation"),
        ("USAGE_GUIDE.md", "Usage guide"),
    ]

    for filepath, description in files_to_check:
        if not check_file_exists(filepath, description):
            all_good = False

    # Check Python modules (if virtual env is active)
    print("\n🐍 Python Dependencies:")
    modules_to_check = ["pexpect", "telegram", "dotenv"]

    for module in modules_to_check:
        if not check_python_module(module):
            print(
                f"  💡 Run: source .venv/bin/activate && pip install -r requirements.txt"
            )
            all_good = False
            break

    # Check system commands
    print("\n⚙️  System Commands:")
    commands_to_check = [
        ("python3 --version", "Python 3"),
        ("npx --version", "NPX (Node Package Runner)"),
    ]

    for command, description in commands_to_check:
        if not check_command(command, description):
            all_good = False

    # Check Claude-Code CLI
    print("\n🤖 Claude-Code CLI:")
    if check_command("npx @anthropic-ai/claude-code --help", "Claude-Code CLI"):
        print("  🎉 Claude-Code CLI is available!")
    else:
        print("  ⚠️  Claude-Code CLI not available - will be installed on first run")

    # Check environment configuration
    print("\n🔧 Configuration:")
    check_env_file()

    # Check script permissions
    print("\n🔐 Permissions:")
    scripts_to_check = ["remocode.py", "setup.sh"]
    for script in scripts_to_check:
        if os.path.exists(script):
            if os.access(script, os.X_OK):
                print(f"✅ Executable: {script}")
            else:
                print(f"⚠️  Not executable: {script} (run: chmod +x {script})")

    # Final status
    print("\n" + "=" * 60)
    if all_good:
        print("🎉 VERIFICATION PASSED!")
        print("\nNext steps:")
        print("1. Configure .env file if using Telegram")
        print("2. Run: source .venv/bin/activate")
        print("3. Run: ./remocode.py")
    else:
        print("⚠️  VERIFICATION ISSUES FOUND")
        print("\nTo fix issues:")
        print("1. Run: ./setup.sh")
        print("2. Check error messages above")
        print("3. Run this verification script again")

    print("\n📚 For help, see:")
    print("  - REMOCODE_README.md")
    print("  - USAGE_GUIDE.md")
    print("  - Run: python3 demo_remocode.py")


if __name__ == "__main__":
    main()
