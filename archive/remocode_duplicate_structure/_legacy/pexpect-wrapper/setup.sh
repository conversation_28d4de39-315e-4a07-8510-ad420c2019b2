#!/bin/bash
# Setup script for Remote Claude-Code Controller

set -e

echo "🚀 Setting up Remote Claude-Code Controller..."

# Check if uv is installed
if ! command -v uv &> /dev/null; then
    echo "📦 Installing uv..."
    curl -LsSf https://astral.sh/uv/install.sh | sh
    source ~/.bashrc || source ~/.zshrc || true
fi

# Create virtual environment
echo "🐍 Creating Python virtual environment..."
uv venv .venv

# Activate virtual environment
echo "⚡ Activating virtual environment..."
source .venv/bin/activate

# Install dependencies
echo "📚 Installing Python dependencies..."
uv pip install -r requirements.txt

# Make script executable
chmod +x remocode.py

# Copy environment template if .env doesn't exist
if [ ! -f .env ]; then
    echo "📝 Creating .env file from template..."
    cp .env.example .env
    echo "⚠️  Please edit .env file with your Telegram bot token and chat ID"
fi

echo "✅ Setup complete!"
echo ""
echo "Next steps:"
echo "1. Edit .env file with your Telegram bot credentials"
echo "2. Run: source .venv/bin/activate"
echo "3. Run: ./remocode.py [optional_spec_file]"
echo ""
echo "For local-only mode (no Telegram), just run without TG_TOKEN in .env"
