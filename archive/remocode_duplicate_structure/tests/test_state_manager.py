"""Tests for state manager functionality."""

import json
import time
from pathlib import Path

from src.state_manager import StateManager, <PERSON>gR<PERSON><PERSON>


def test_create_and_save_session(tmp_path: Path):
    """Test creating and saving a session."""
    state_file = tmp_path / "state.json"
    sm = StateManager(state_file)

    # Create new session
    session = sm.create_new_session("test_session", "test_chat")
    assert session.session_id == "test_session"
    assert session.telegram_chat_id == "test_chat"

    # Add some log lines
    sm.add_log_line("test log line 1")
    sm.add_log_line("test log line 2")

    # Save state
    assert sm.force_save() is True
    assert state_file.exists()


def test_load_existing_state(tmp_path: Path):
    """Test loading existing state from file."""
    state_file = tmp_path / "state.json"

    # Create and save initial state
    sm1 = StateManager(state_file)
    sm1.create_new_session("test_session", "test_chat")
    sm1.add_log_line("test log line")
    sm1.force_save()

    # Load state with new manager
    sm2 = StateManager(state_file)
    loaded_state = sm2.load_state()

    assert loaded_state is not None
    assert loaded_state.session_id == "test_session"
    assert loaded_state.telegram_chat_id == "test_chat"
    assert any("test log line" in line for line in loaded_state.log_buffer)


def test_log_buffer_max_size():
    """Test that log buffer respects maximum size."""
    sm = StateManager(max_log_lines=5)
    sm.create_new_session("test", None)

    # Add more lines than the buffer can hold
    for i in range(10):
        sm.add_log_line(f"line {i}")

    # Should only keep the last 5 lines
    assert len(sm.log_buffer) == 5
    assert "line 9" in sm.log_buffer[-1]
    assert "line 5" in sm.log_buffer[0]


def test_session_summary():
    """Test session summary information."""
    sm = StateManager()

    # No session initially
    summary = sm.get_summary()
    assert summary["status"] == "no_session"

    # Create session
    sm.create_new_session("test", "chat123")
    sm.set_claude_pid(12345)

    summary = sm.get_summary()
    assert summary["status"] == "active"
    assert summary["session_id"] == "test"
    assert summary["has_telegram"] is True
    assert summary["claude_pid"] == 12345


def test_menu_state_management():
    """Test menu state management."""
    sm = StateManager()
    sm.create_new_session("test", None)

    # Set menu
    menu_data = {
        "type": "numbered_options",
        "options": [{"number": 1, "text": "Option 1"}],
    }
    sm.set_current_menu(menu_data)

    summary = sm.get_summary()
    assert summary["has_menu"] is True

    # Clear menu
    sm.clear_current_menu()

    summary = sm.get_summary()
    assert summary["has_menu"] is False


def test_log_rotator(tmp_path: Path):
    """Test log file rotation."""
    log_file = tmp_path / "test.log"

    # Create log file with many lines
    lines = [f"line {i}\n" for i in range(600)]
    log_file.write_text("".join(lines))

    # Rotate with smaller limit
    rotator = LogRotator(log_file, max_lines=500)
    assert rotator.rotate_if_needed() is True

    # Check that file was rotated
    with open(log_file) as f:
        remaining_lines = f.readlines()

    assert len(remaining_lines) == 500
    # Should keep the last 500 lines
    assert "line 599" in remaining_lines[-1]
    assert "line 100" in remaining_lines[0]


def test_log_rotator_no_rotation_needed(tmp_path: Path):
    """Test that log rotator doesn't rotate small files."""
    log_file = tmp_path / "small.log"

    # Create small log file
    lines = [f"line {i}\n" for i in range(100)]
    log_file.write_text("".join(lines))

    # Try to rotate
    rotator = LogRotator(log_file, max_lines=500)
    assert rotator.rotate_if_needed() is False

    # File should be unchanged
    with open(log_file) as f:
        remaining_lines = f.readlines()

    assert len(remaining_lines) == 100
