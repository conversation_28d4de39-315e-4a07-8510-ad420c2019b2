"""Tests for menu response handler functionality."""

import asyncio
import pytest
from src.menu_detector import MenuResponseHandler


@pytest.mark.asyncio
async def test_response_handler_accepts_choice():
    """Test that response handler accepts and returns choices."""
    handler = MenuResponseHandler()

    async def submit_choice():
        """Submit a choice after a short delay."""
        await asyncio.sleep(0.05)
        assert handler.submit_choice("1", "test")

    # Start the choice submission task
    asyncio.create_task(submit_choice())

    # Wait for choice
    result = await handler.wait_for_choice(timeout=1.0)
    assert result == "1"


@pytest.mark.asyncio
async def test_response_handler_timeout():
    """Test that response handler times out properly."""
    handler = MenuResponseHandler()

    # Wait for choice with short timeout
    result = await handler.wait_for_choice(timeout=0.1)
    assert result is None


@pytest.mark.asyncio
async def test_response_handler_race_condition():
    """Test that first response wins in race condition."""
    handler = <PERSON>uResponse<PERSON>and<PERSON>()

    async def submit_first():
        await asyncio.sleep(0.05)
        return handler.submit_choice("first", "source1")

    async def submit_second():
        await asyncio.sleep(0.1)
        return handler.submit_choice("second", "source2")

    # Start both submission tasks
    task1 = asyncio.create_task(submit_first())
    task2 = asyncio.create_task(submit_second())

    # Wait for choice
    result = await handler.wait_for_choice(timeout=1.0)

    # First should win
    assert result == "first"

    # Verify task results
    first_accepted = await task1
    second_accepted = await task2

    assert first_accepted is True
    assert second_accepted is False  # Second should be rejected


@pytest.mark.asyncio
async def test_response_handler_cancel():
    """Test that response handler can be cancelled."""
    handler = MenuResponseHandler()

    # Start waiting
    wait_task = asyncio.create_task(handler.wait_for_choice(timeout=1.0))

    # Cancel after short delay
    await asyncio.sleep(0.05)
    handler.cancel_waiting()

    # Should return None due to cancellation
    result = await wait_task
    assert result is None
