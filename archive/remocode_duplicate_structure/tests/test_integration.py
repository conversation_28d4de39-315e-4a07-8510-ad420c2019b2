#!/usr/bin/env python3
"""
Integration test to verify both terminal input and Telegram output functionality.
Tests the complete RemoCode pipeline.
"""

import sys
import asyncio
import logging
import os
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent))

from src.claude_wrapper import Claude<PERSON><PERSON><PERSON>
from src.telegram_bot import TelegramBot, TELEGRAM_AVAILABLE
from src.state_manager import StateManager
from src.config import EnvironmentConfig

# Setup debug logging
logging.basicConfig(
    level=logging.DEBUG, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class IntegrationTester:
    """Test both terminal input forwarding and Telegram output pipeline."""

    def __init__(self):
        self.claude_wrapper = None
        self.telegram_bot = None
        self.output_lines = []
        self.env_config = EnvironmentConfig()

    async def test_terminal_input_forwarding(self) -> bool:
        """Test that terminal input reaches Claude CLI properly."""
        print("🧪 Testing terminal input forwarding...")

        # Create state manager
        state_manager = StateManager(Path("test_integration_state.json"))
        state_manager.create_new_session("integration-test", None)

        # Create Claude wrapper with output callback
        self.claude_wrapper = ClaudeWrapper(
            claude_cmd="npx @anthropic-ai/claude-code",
            state_manager=state_manager,
            on_output=self._capture_output,
        )

        try:
            # Start Claude wrapper
            success = await self.claude_wrapper.start()
            if not success:
                print("❌ Failed to start Claude wrapper")
                return False

            print("✅ Claude wrapper started")
            print("💡 Try typing in the terminal - text should appear in Claude CLI")
            print("💡 Press Ctrl+C to continue to Telegram test")

            # Wait for user to test typing
            await asyncio.sleep(20)

            print("✅ Terminal input test completed")
            return True

        except KeyboardInterrupt:
            print("✅ Terminal input test completed (interrupted)")
            return True
        except Exception as e:
            print(f"❌ Terminal input test failed: {e}")
            return False
        finally:
            if self.claude_wrapper:
                await self.claude_wrapper.stop()

    async def test_telegram_output_pipeline(self) -> bool:
        """Test that Telegram bot receives and processes output."""
        print("\\n🧪 Testing Telegram output pipeline...")

        # Check if Telegram is available
        if not TELEGRAM_AVAILABLE:
            print("❌ python-telegram-bot not available, skipping Telegram test")
            return False

        # Check environment variables
        if not self.env_config.has_telegram:
            print("❌ Telegram not configured (missing TG_TOKEN or TG_CHAT)")
            print(f"💡 TG_TOKEN present: {bool(os.getenv('TG_TOKEN'))}")
            print(f"💡 TG_CHAT present: {bool(os.getenv('TG_CHAT'))}")
            return False

        print(
            f"✅ Telegram configured: token={'*' * 10 + self.env_config.tg_token[-4:]}"
        )
        print(f"✅ Chat ID: {self.env_config.tg_chat}")

        try:
            # Create Telegram bot
            self.telegram_bot = TelegramBot(
                token=self.env_config.tg_token,
                chat_id=self.env_config.tg_chat,
                on_menu_choice=lambda choice, source: print(
                    f"Menu choice: {choice} from {source}"
                ),
                on_command=lambda cmd, args: print(f"Command: {cmd} {args}"),
            )

            # Start Telegram bot
            success = await self.telegram_bot.start()
            if not success:
                print("❌ Failed to start Telegram bot")
                return False

            print("✅ Telegram bot started successfully")

            # Test sending messages
            test_messages = [
                "🧪 RemoCode Integration Test",
                "Testing message batching...",
                "Line 1 of Claude output",
                "Line 2 of Claude output",
                "Line 3 of Claude output",
            ]

            print("📤 Sending test messages to Telegram...")
            for msg in test_messages:
                await self.telegram_bot.send_output(msg, silent=False)
                await asyncio.sleep(0.5)

            # Wait for messages to be sent
            print("⏳ Waiting for message batching to process...")
            await asyncio.sleep(5)

            print("✅ Telegram output test completed")
            print("💡 Check your Telegram chat for test messages")
            return True

        except Exception as e:
            print(f"❌ Telegram test failed: {e}")
            logger.exception("Telegram test error")
            return False
        finally:
            if self.telegram_bot:
                await self.telegram_bot.stop()

    def _capture_output(self, line: str) -> None:
        """Capture output from Claude for analysis."""
        self.output_lines.append(line)
        if len(self.output_lines) <= 5:  # Only log first few lines to avoid spam
            print(f"📥 Captured: {line[:50]}...")

    async def run_full_test(self) -> bool:
        """Run both terminal and Telegram tests."""
        print("🚀 RemoCode Integration Testing")
        print("=" * 50)

        # Test 1: Terminal input forwarding
        terminal_success = await self.test_terminal_input_forwarding()

        # Test 2: Telegram output pipeline
        telegram_success = await self.test_telegram_output_pipeline()

        # Results
        print(f"\\n📊 Test Results:")
        print(f"Terminal Input: {'✅ PASS' if terminal_success else '❌ FAIL'}")
        print(f"Telegram Output: {'✅ PASS' if telegram_success else '❌ FAIL'}")

        if terminal_success and telegram_success:
            print("🎉 All tests passed! RemoCode integration is working.")
            return True
        else:
            print("⚠️  Some tests failed. Check the logs above.")
            return False


async def main():
    """Run integration tests."""
    tester = IntegrationTester()
    try:
        success = await tester.run_full_test()
        return 0 if success else 1
    except KeyboardInterrupt:
        print("\\n👋 Test interrupted by user")
        return 0
    except Exception as e:
        print(f"💥 Test crashed: {e}")
        logger.exception("Integration test error")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
