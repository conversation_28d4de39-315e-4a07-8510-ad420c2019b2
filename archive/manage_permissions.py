#!/usr/bin/env python3
"""
Romocode Permission Manager

Utility script to manage persistent permissions for the autonomous romocode system.
Allows viewing, adding, removing, and cleaning up persistent permissions.
"""

import json
import time
from pathlib import Path
from typing import Dict
import argparse
import sys

PERSISTENT_PERMISSIONS_FILE = (
    Path.home() / ".cache" / "claude_persistent_permissions.json"
)
PERSISTENT_TTL_SECS = 30 * 24 * 3600  # 30 days


def load_permissions() -> Dict[str, float]:
    """Load persistent permissions from file."""
    try:
        if PERSISTENT_PERMISSIONS_FILE.exists():
            data = json.loads(PERSISTENT_PERMISSIONS_FILE.read_text())
            return data
        return {}
    except Exception as e:
        print(f"Error loading permissions: {e}")
        return {}


def save_permissions(permissions: Dict[str, float]):
    """Save persistent permissions to file."""
    try:
        PERSISTENT_PERMISSIONS_FILE.parent.mkdir(parents=True, exist_ok=True)
        PERSISTENT_PERMISSIONS_FILE.write_text(json.dumps(permissions, indent=2))
        print(f"Permissions saved to {PERSISTENT_PERMISSIONS_FILE}")
    except Exception as e:
        print(f"Error saving permissions: {e}")


def clean_expired_permissions(permissions: Dict[str, float]) -> Dict[str, float]:
    """Remove expired permissions."""
    current_time = time.time()
    cleaned = {
        k: v for k, v in permissions.items() if (current_time - v) < PERSISTENT_TTL_SECS
    }
    expired_count = len(permissions) - len(cleaned)
    if expired_count > 0:
        print(f"Removed {expired_count} expired permissions")
    return cleaned


def format_time_ago(timestamp: float) -> str:
    """Format timestamp as human-readable time ago."""
    diff = time.time() - timestamp
    if diff < 3600:  # Less than 1 hour
        return f"{int(diff // 60)} minutes ago"
    elif diff < 86400:  # Less than 1 day
        return f"{int(diff // 3600)} hours ago"
    else:
        return f"{int(diff // 86400)} days ago"


def format_expires_in(timestamp: float) -> str:
    """Format when permission expires."""
    expires_at = timestamp + PERSISTENT_TTL_SECS
    diff = expires_at - time.time()
    if diff < 0:
        return "EXPIRED"
    elif diff < 86400:  # Less than 1 day
        return f"expires in {int(diff // 3600)} hours"
    else:
        return f"expires in {int(diff // 86400)} days"


def list_permissions():
    """List all persistent permissions."""
    permissions = load_permissions()
    permissions = clean_expired_permissions(permissions)

    if not permissions:
        print("No persistent permissions found.")
        return

    print(f"\n📋 Persistent Permissions ({len(permissions)} total):")
    print("=" * 60)

    for perm, timestamp in sorted(permissions.items()):
        granted = format_time_ago(timestamp)
        expires = format_expires_in(timestamp)
        print(f"✅ {perm}")
        print(f"   Granted: {granted} | {expires}")
        print()


def add_permission(operation: str, days: int = 30):
    """Add a persistent permission."""
    permissions = load_permissions()
    permissions = clean_expired_permissions(permissions)

    permissions[operation] = time.time()
    save_permissions(permissions)

    print(f"✅ Added persistent permission for: {operation}")
    print(f"   Valid for {days} days")


def remove_permission(operation: str):
    """Remove a persistent permission."""
    permissions = load_permissions()

    if operation in permissions:
        del permissions[operation]
        save_permissions(permissions)
        print(f"❌ Removed persistent permission for: {operation}")
    else:
        print(f"⚠️  Permission not found: {operation}")


def clear_all_permissions():
    """Clear all persistent permissions."""
    try:
        if PERSISTENT_PERMISSIONS_FILE.exists():
            PERSISTENT_PERMISSIONS_FILE.unlink()
            print("🗑️  All persistent permissions cleared")
        else:
            print("No permissions file found")
    except Exception as e:
        print(f"Error clearing permissions: {e}")


def main():
    parser = argparse.ArgumentParser(
        description="Manage Romocode persistent permissions"
    )
    subparsers = parser.add_subparsers(dest="command", help="Available commands")

    # List command
    subparsers.add_parser("list", help="List all persistent permissions")

    # Add command
    add_parser = subparsers.add_parser("add", help="Add a persistent permission")
    add_parser.add_argument(
        "operation",
        help='Operation to grant permission for (e.g., "WebFetch" or "Bash:npm test")',
    )
    add_parser.add_argument(
        "--days", type=int, default=30, help="Number of days (default: 30)"
    )

    # Remove command
    remove_parser = subparsers.add_parser(
        "remove", help="Remove a persistent permission"
    )
    remove_parser.add_argument("operation", help="Operation to remove permission for")

    # Clear command
    subparsers.add_parser("clear", help="Clear all persistent permissions")

    # Clean command
    subparsers.add_parser("clean", help="Remove expired permissions")

    args = parser.parse_args()

    if not args.command:
        parser.print_help()
        return

    if args.command == "list":
        list_permissions()
    elif args.command == "add":
        add_permission(args.operation, args.days)
    elif args.command == "remove":
        remove_permission(args.operation)
    elif args.command == "clear":
        clear_all_permissions()
    elif args.command == "clean":
        permissions = load_permissions()
        cleaned = clean_expired_permissions(permissions)
        if len(cleaned) < len(permissions):
            save_permissions(cleaned)
        else:
            print("No expired permissions found")


if __name__ == "__main__":
    main()
