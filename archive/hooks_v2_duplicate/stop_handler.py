#!/usr/bin/env python3
"""
stop_handler.py – RemoCode v2 Stop hook

Called by Claude <PERSON> when the main agent has finished responding.
Captures detailed stop reasons including usage limits, completion, errors.
"""

import json
import os
import sys
import time
import urllib.request
import urllib.parse
from typing import Dict, Any, Optional
from pathlib import Path

STATE_DIR = Path(__file__).parent.parent / "state"
STATE_DIR.mkdir(exist_ok=True)

# Telegram configuration
TG_TOKEN = os.getenv("TG_TOKEN")
TG_CHAT = os.getenv("TG_CHAT")


def _telegram(method: str, **params) -> Dict[str, Any]:
    """Lightweight POST using stdlib to avoid external deps."""
    url = f"https://api.telegram.org/bot{TG_TOKEN}/{method}"
    data = json.dumps(params).encode()
    req = urllib.request.Request(
        url, data=data, headers={"Content-Type": "application/json"}
    )
    with urllib.request.urlopen(req, timeout=10) as resp:
        return json.loads(resp.read().decode())


def _analyze_stop_reason(transcript_path: str, session_id: str) -> Dict[str, Any]:
    """Analyze the transcript to determine detailed stop reason"""
    stop_info = {
        "reason": "unknown",
        "details": "",
        "usage_limit": False,
        "completion": False,
        "error": False,
        "user_interrupt": False,
    }

    try:
        if not os.path.exists(transcript_path):
            stop_info["reason"] = "no_transcript"
            stop_info["details"] = "Transcript file not found"
            return stop_info

        # Read the last few lines of the transcript to understand why Claude stopped
        with open(transcript_path, "r") as f:
            lines = f.readlines()

        # Look at the last 10 lines for stop indicators
        recent_lines = lines[-10:] if len(lines) >= 10 else lines
        transcript_text = "".join(recent_lines).lower()

        # Check for usage limit indicators
        usage_limit_indicators = [
            "usage limit reached",
            "limit will reset",
            "rate limit",
            "quota exceeded",
            "daily limit",
            "monthly limit",
        ]

        for indicator in usage_limit_indicators:
            if indicator in transcript_text:
                stop_info["usage_limit"] = True
                stop_info["reason"] = "usage_limit"
                # Try to extract the reset time
                if "reset at" in transcript_text:
                    import re

                    reset_match = re.search(r"reset at (\d+[ap]m)", transcript_text)
                    if reset_match:
                        stop_info[
                            "details"
                        ] = f"Usage limit reached. Limit will reset at {reset_match.group(1)}"
                    else:
                        stop_info[
                            "details"
                        ] = "Usage limit reached. Check terminal for reset time."
                else:
                    stop_info["details"] = "Usage limit reached"
                break

        # Check for completion indicators
        if not stop_info["usage_limit"]:
            completion_indicators = [
                "task completed",
                "finished",
                "done",
                "completed successfully",
                "all set",
            ]

            for indicator in completion_indicators:
                if indicator in transcript_text:
                    stop_info["completion"] = True
                    stop_info["reason"] = "completion"
                    stop_info["details"] = "Task completed successfully"
                    break

        # Check for error indicators
        if not stop_info["usage_limit"] and not stop_info["completion"]:
            error_indicators = [
                "error",
                "failed",
                "exception",
                "timeout",
                "connection refused",
            ]

            for indicator in error_indicators:
                if indicator in transcript_text:
                    stop_info["error"] = True
                    stop_info["reason"] = "error"
                    stop_info["details"] = "An error occurred during execution"
                    break

        # If no specific reason found, assume normal completion
        if stop_info["reason"] == "unknown":
            stop_info["completion"] = True
            stop_info["reason"] = "completion"
            stop_info["details"] = "Claude Code session ended normally"

    except Exception as e:
        stop_info["reason"] = "analysis_error"
        stop_info["details"] = f"Failed to analyze stop reason: {e}"

    return stop_info


def _send_stop_notification(
    stop_info: Dict[str, Any], session_id: str, raw_data: Dict[str, Any]
) -> None:
    """Send stop notification to Telegram with detailed context"""
    if not TG_TOKEN or not TG_CHAT:
        return

    try:
        import datetime

        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        session_info = f" (Session: {session_id[:8]}...)" if session_id else ""

        # Choose appropriate emoji and title based on stop reason
        if stop_info["usage_limit"]:
            emoji = "⏳"
            title = "Usage Limit Reached"
            color = "🔴"
        elif stop_info["error"]:
            emoji = "❌"
            title = "Error Occurred"
            color = "🔴"
        elif stop_info["completion"]:
            emoji = "✅"
            title = "Task Completed"
            color = "🟢"
        else:
            emoji = "🛑"
            title = "Session Stopped"
            color = "🟡"

        # Build notification message
        message_text = f"{color} **Claude Code {title}** {session_info}\n\n"
        message_text += (
            f"{emoji} **Reason:** {stop_info['reason'].replace('_', ' ').title()}\n"
        )

        if stop_info["details"]:
            message_text += f"📝 **Details:** {stop_info['details']}\n"

        message_text += f"\n⏰ {timestamp}"

        # Add raw JSON for debugging
        json_str = json.dumps(raw_data, indent=2)
        if len(json_str) > 600:
            json_str = json_str[:600] + "...\n[JSON truncated]"
        message_text += f"\n\n🔧 **Debug Info:**\n```json\n{json_str}\n```"

        # Send notification
        _telegram(
            "sendMessage", chat_id=TG_CHAT, text=message_text, parse_mode="Markdown"
        )

        # Log the stop event
        with open(STATE_DIR / "remocode.log", "a") as f:
            f.write(
                f"[stop_handler] Claude Code stopped: {stop_info['reason']} - {stop_info['details']}\n"
            )

    except Exception as e:
        with open(STATE_DIR / "remocode.log", "a") as f:
            f.write(f"[stop_handler] Failed to send stop notification: {e}\n")


def main():
    """Main entry point for the Stop hook"""
    try:
        # Read JSON input from stdin according to Claude Code hooks documentation
        input_data = json.load(sys.stdin)

        session_id = input_data.get("session_id", "")
        transcript_path = input_data.get("transcript_path", "")
        hook_event_name = input_data.get("hook_event_name", "")
        stop_hook_active = input_data.get("stop_hook_active", False)

        # Debug logging
        with open(STATE_DIR / "remocode.log", "a") as f:
            f.write(
                f"[stop_handler] Hook triggered: {hook_event_name}, Session: {session_id}, Active: {stop_hook_active}\n"
            )

        # Validate this is a Stop hook
        if hook_event_name != "Stop":
            print(f"Not a Stop hook: {hook_event_name}")
            return

        # Analyze the stop reason
        stop_info = _analyze_stop_reason(transcript_path, session_id)

        # Send notification to Telegram
        _send_stop_notification(stop_info, session_id, input_data)

        print(f"Stop notification sent: {stop_info['reason']}")

    except json.JSONDecodeError as e:
        error_msg = f"Failed to parse JSON input: {e}"
        with open(STATE_DIR / "remocode.log", "a") as f:
            f.write(f"[stop_handler] ERROR: {error_msg}\n")
        print(f"Stop handler JSON error: {e}", file=sys.stderr)

    except Exception as e:
        error_msg = f"Unexpected error: {e}"
        with open(STATE_DIR / "remocode.log", "a") as f:
            f.write(f"[stop_handler] ERROR: {error_msg}\n")
        print(f"Stop handler error: {e}", file=sys.stderr)


if __name__ == "__main__":
    main()
