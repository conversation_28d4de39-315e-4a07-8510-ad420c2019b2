#!/usr/bin/env python3
"""
RemoCode launcher script.

This script ensures we're running with the proper Python environment
and launches the main application.
"""

import sys
import os
from pathlib import Path

# Ensure the parent directory of the `src` package is on the path
script_dir = Path(__file__).parent
parent_dir = script_dir  # `src` is a package inside this directory
sys.path.insert(0, str(parent_dir))

# Import and run main application as a proper package module so that
# the internal relative imports (e.g. `from .config import ...`) work.
try:
    from src.main import main as _main  # type: ignore  # noqa: WPS433
    _main()
except ImportError as e:
    print(f"❌ Failed to import RemoCode modules: {e}")
    print("💡 Make sure you've installed dependencies: uv pip install -r requirements.txt")
    sys.exit(1)
except Exception as e:
    print(f"❌ Fatal error: {e}")
    sys.exit(1)
