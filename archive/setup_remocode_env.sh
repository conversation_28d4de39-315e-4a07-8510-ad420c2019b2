#!/usr/bin/env bash
# Purpose : Create an isolated Python environment for the Remocode hook scripts
# Usage   : ./remocode/setup_remocode_env.sh  (run from repo root)
# Requires: uv (https://github.com/astral-sh/uv)

set -euo pipefail

ENV_DIR=".remocode/.venv-remocode"

if [[ -d "$ENV_DIR" ]]; then
  echo "✅ Reusing existing environment at $ENV_DIR"
else
  echo "📦 Creating virtual environment with uv…"
  uv venv --python=$(command -v python3) "$ENV_DIR"
fi

# shellcheck source=/dev/null
source "$ENV_DIR/bin/activate"

# Install hook dependencies
uv pip install --upgrade requests python-dotenv

echo "🎉 Remocode environment ready. Activate with: source $ENV_DIR/bin/activate"
