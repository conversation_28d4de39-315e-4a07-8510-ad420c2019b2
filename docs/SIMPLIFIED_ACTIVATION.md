# RemoCode v2 Simplified Activation System

## Overview

RemoCode v2 now features a **completely simplified activation process** that eliminates the need for manual environment variable setup. The system automatically detects when RemoCode should be active based on which command you run.

## ✨ New Simplified Workflow

### Before (Old System)
```bash
# Required manual setup
export REMOCODE_ENABLED=true
claude  # Would use RemoCode hooks

# Or without RemoCode
unset REMOCODE_ENABLED
claude  # Pure Claude Code
```

### After (New System)
```bash
# RemoCode + Claude Code with dual interface
uv run remocode transparent

# Pure Claude Code with zero interference
claude
```

## 🔧 How It Works

### Process-Based Detection

The new system uses **process markers** instead of environment variables:

1. **Marker Creation**: When `uv run remocode transparent` starts, it creates a process marker file
2. **Hook Detection**: All hooks automatically check for this marker to determine if RemoCode is active
3. **Automatic Cleanup**: Marker is removed when RemoCode exits (gracefully or forcefully)
4. **Zero Interference**: No marker = no RemoCode effects

### Marker File Location

```
~/.remocode/active.marker
```

**Contents:**
```json
{
  "pid": 12345,
  "started_at": 1703123456.789,
  "command": "uv run remocode transparent"
}
```

### Hook Detection Logic

Each hook now includes automatic detection:

```python
def _is_remocode_active() -> bool:
    """Check if RemoCode is currently active by looking for the process marker"""
    try:
        marker_file = Path.home() / ".remocode" / "active.marker"
        if not marker_file.exists():
            return False

        # Check if the marker is from a currently running process
        with open(marker_file) as f:
            marker_data = json.load(f)

        pid = marker_data.get("pid")
        if pid:
            # Check if process is still running
            try:
                os.kill(pid, 0)  # Signal 0 checks if process exists
                return True
            except (OSError, ProcessLookupError):
                # Process no longer exists, clean up stale marker
                marker_file.unlink()
                return False

    except Exception:
        # If anything goes wrong, assume RemoCode is not active
        pass

    return False
```

## 🛡️ Safety Guarantees

### Zero Interference Promise

When you run `claude` directly:

- ✅ **No Hook Execution**: All RemoCode hooks detect no marker and exit immediately
- ✅ **No Performance Impact**: Hooks exit in microseconds with minimal overhead
- ✅ **No Side Effects**: No files created, no network calls, no logging
- ✅ **Pure Claude Code**: Exactly the same experience as if RemoCode didn't exist

### Automatic Cleanup

The system handles cleanup in multiple ways:

1. **Normal Exit**: `atexit` handlers remove the marker
2. **Process Termination**: Stale markers are detected and cleaned up
3. **System Restart**: Markers are automatically invalidated on reboot
4. **Manual Cleanup**: Markers can be safely deleted manually if needed

## 📋 Updated Commands

### RemoCode Commands

```bash
# Main transparent mode (recommended)
uv run remocode transparent

# With plan mode
uv run remocode transparent --plan

# With live message aggregation
uv run remocode transparent --live-mode

# Advanced mode with full control
uv run remocode run
```

### Pure Claude Code

```bash
# Standard Claude Code usage
claude

# With any Claude Code options
claude --help
claude --permission-mode plan
claude "Write a Python script"
```

## 🔄 Migration Guide

### For Existing Users

**No action required!** The old `REMOCODE_ENABLED` environment variable is no longer needed:

1. **Remove Environment Variable**: You can safely remove `export REMOCODE_ENABLED=true` from your shell profile
2. **Update Scripts**: Replace any scripts that set `REMOCODE_ENABLED` with direct command usage
3. **Clean Workflows**: Use the new simplified commands

### Script Updates

**Before:**
```bash
#!/bin/bash
export REMOCODE_ENABLED=true
claude "Analyze this codebase"
```

**After:**
```bash
#!/bin/bash
uv run remocode transparent -- "Analyze this codebase"
```

## 🧪 Testing the System

### Verify Zero Interference

Test that `claude` has no RemoCode effects:

```bash
# This should show no RemoCode activity
claude --help

# Check for marker file (should not exist)
ls ~/.remocode/active.marker  # Should show "No such file"
```

### Verify Automatic Activation

Test that RemoCode activates automatically:

```bash
# Start RemoCode (in background for testing)
uv run remocode transparent &
REMOCODE_PID=$!

# Check marker exists
ls ~/.remocode/active.marker  # Should exist

# Stop RemoCode
kill $REMOCODE_PID

# Check marker is cleaned up
ls ~/.remocode/active.marker  # Should be gone
```

### Hook Detection Test

```bash
# Test hook without RemoCode (should be transparent)
echo '{"hook_event_name": "PreToolUse", "tool_name": "Read", "tool_input": {}}' | \
  python3 src/remocode/hooks/pretool_guard.py
# Output: {"decision": "approve", "reason": "RemoCode disabled - transparent mode"}

# Test hook with RemoCode active
uv run remocode transparent &
echo '{"hook_event_name": "PreToolUse", "tool_name": "Read", "tool_input": {}}' | \
  python3 src/remocode/hooks/pretool_guard.py
# Output: {"decision": "approve", "reason": "Safe tool Read auto-approved"}
```

## 🔧 Troubleshooting

### Common Issues

1. **Stale Markers**: If RemoCode was forcefully terminated, stale markers are automatically cleaned up
2. **Permission Issues**: Ensure `~/.remocode/` directory is writable
3. **Process Detection**: The system gracefully handles process detection failures

### Manual Cleanup

If needed, you can manually clean up:

```bash
# Remove marker file
rm -f ~/.remocode/active.marker

# Remove entire RemoCode directory
rm -rf ~/.remocode/
```

### Debug Mode

Enable debug logging to see marker detection:

```bash
export REMOCODE_LOG_PATH=/tmp/remocode-debug.log
uv run remocode transparent --verbose
```

## 🎯 Benefits

### For Users

- **🚀 Instant Activation**: No setup required, just run the command
- **🔄 Clear Separation**: Obvious distinction between RemoCode and pure Claude Code
- **🛡️ Zero Risk**: No chance of accidentally enabling RemoCode
- **📱 Mobile Ready**: Immediate Telegram integration when desired

### For Developers

- **🧹 Cleaner Code**: No environment variable management
- **🔒 Safer Defaults**: Hooks are disabled by default
- **🎯 Explicit Intent**: Command choice clearly indicates desired behavior
- **🧪 Easier Testing**: Deterministic activation state

## 🚀 Next Steps

1. **Update Your Workflows**: Replace environment variable usage with direct commands
2. **Clean Up Scripts**: Remove `REMOCODE_ENABLED` from your shell configuration
3. **Enjoy Simplicity**: Use `uv run remocode transparent` when you want dual interface
4. **Use Pure Claude**: Use `claude` when you want standard Claude Code

The simplified activation system makes RemoCode v2 more intuitive, safer, and easier to use while maintaining all the powerful dual interface capabilities you need for mobile Claude Code control.
