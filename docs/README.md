# RemoCode v2 - Improved Hook-Based System

RemoCode v2 is a completely redesigned system that allows you to use Claude Code normally while mirroring the interaction to Telegram. This enables you to respond to permission requests from your phone instead of being tied to your computer.

## 🎯 Key Improvements

### Fixed Core Issues

- **✅ JSON Parsing**: Fixed hook system to properly parse Claude Code's JSON input
- **✅ Smart Permission Logic**: Distinguishes between notifications vs genuine permission requests
- **✅ Enhanced Telegram Interface**: Shows both human-readable messages and raw JSON for debugging
- **✅ Live Progress Updates**: Non-blocking notifications for transparency
- **✅ Proper Hook Integration**: Follows Claude Code hooks documentation correctly

### New Features (Latest Update)

- **✅ Enhanced Tool Summaries**: Detailed, meaningful descriptions for all tool types including Glob, MCP tools, and more
- **✅ Live Message Aggregation**: Optional mode that updates a single Telegram message with multiple activities
- **✅ Stop Hook Integration**: Captures Claude Code usage limits, completion, and error states with detailed context
- **✅ Raw JSON Debugging**: Always includes complete payload information for troubleshooting
- **✅ Configuration Modes**: Switch between individual notifications and live aggregation modes

### New Philosophy: Notification vs Permission

**Before**: Every tool use triggered a permission request (overly aggressive)
**After**: Smart filtering that distinguishes:

1. **Notifications** (Non-blocking, info-only):
   - Regular tool usage (Read, Write, WebFetch, etc.)
   - Safe bash commands (ls, git status, npm build)
   - Progress updates for transparency

2. **Permissions** (Blocking, requires user input):
   - Genuinely dangerous operations (rm -rf, sudo commands)
   - Claude Code's built-in permission requests
   - Plan approval menus

## 🏗️ Architecture

### Hook System

- **PreToolUse Hook** (`pretool_guard.py`): Intercepts tool calls, auto-approves safe operations, only blocks dangerous ones
- **Notification Hook** (`notification_handler.py`): Handles Claude Code's notification system for genuine permission requests
- **Stop Hook** (`stop_handler.py`): Captures Claude Code stop events including usage limits and completion states
- **Menu Detection** (`menu_detector.py`): Detects interactive prompts and plan approval menus

### Telegram Integration

- **Enhanced Messages**: Shows both human-readable content and raw JSON for debugging
- **Dynamic Keyboards**: Creates buttons based on actual Claude Code options
- **Multiple Input Methods**: Supports both button presses and text replies
- **Timeout Handling**: Graceful fallback when no response received

## 🚀 Usage

### Quick Start

```bash
# Activate virtual environment
source .venv/bin/activate

# Run in transparent mode (recommended)
python3 main.py transparent

# Or run with full monitoring
python3 main.py run --silent
```

### Configuration

The system automatically configures Claude Code hooks in `.claude/settings.local.json`:

```json
{
  "hooks": {
    "PreToolUse": [
      {
        "hooks": [
          {
            "type": "command",
            "command": "python3 /path/to/pretool_guard.py"
          }
        ]
      }
    ],
    "Notification": [
      {
        "hooks": [
          {
            "type": "command",
            "command": "python3 /path/to/notification_handler.py"
          }
        ]
      }
    ]
  }
}
```

## 📱 Telegram Interface

### Notification Types

1. **Progress Notifications** (Non-blocking):

   ```
   🔄 Claude Code Activity (Session: abc123...)

   🔧 Bash Command: `npm run build`

   ⏰ 14:30:25
   ```

2. **Permission Requests** (Blocking):

   ````
   🔒 Claude Code Permission Request

   ⚠️ Potentially dangerous bash command:
   command: `rm -rf /tmp/old-files`

   This command could modify system state or delete data. Allow execution?

   [✅ Allow] [⛔️ Deny] [🔒 Always allow this]

   🔧 Debug Info (tap to expand):
   ```json
   {
     "session_id": "abc123...",
     "tool_name": "Bash",
     "tool_input": {
       "command": "rm -rf /tmp/old-files"
     }
   }
   ````

   ⏰ 14:30:25

   ```

   ```

3. **Plan Approval Menus**:

   ```
   🤖 Claude Code Plan Approval

   Here is Claude's plan:
   1. Create new component
   2. Update existing files
   3. Add tests

   Choose an option:

   [✅ Yes, auto-accept edits]
   [🔍 Yes, manually approve edits]
   [📝 No, keep planning]
   ```

## 🧪 Testing

Run the test suite to validate the system:

```bash
python3 test_improved_hooks.py
```

Expected output:

```
🚀 RemoCode v2 Improved Hook System Tests
==================================================
🧪 Testing PreToolUse hook: Read
   ✅ Test passed
...
📊 Test Results: 11/11 passed
🎉 All tests passed!
```

## 🔧 Configuration

### Environment Variables

Set these in your `.env` file or environment:

- `TG_TOKEN`: Your Telegram bot token
- `TG_CHAT`: Your Telegram chat ID
- `REMOCODE_LIVE_MODE`: Set to `"true"` to enable live message aggregation mode (default: `"false"`)

### Tool Categories

**Auto-Approved Tools** (send notifications only):

- File operations: Read, Write, Edit, MultiEdit
- Search: Glob, Grep, WebFetch, WebSearch
- Safe bash commands: ls, git status, npm build, etc.

**Permission Required** (block and ask):

- Dangerous bash: rm -rf, sudo commands
- System operations: mount, fdisk
- Deployment: git push --force, production deploys

### Notification Modes

**Individual Mode** (default):

- Each tool use creates a separate Telegram message
- Includes detailed tool summary and raw JSON payload
- Best for detailed debugging and audit trails

**Live Aggregation Mode** (`REMOCODE_LIVE_MODE=true`):

- Updates a single Telegram message with multiple activities
- Shows running list of recent tool uses
- Creates new message only when user interaction is required
- More compact for high-activity sessions

### Enhanced Tool Summaries

The system now provides detailed, meaningful summaries for all tool types:

- **Glob**: `Finding files matching: *.py (including hidden)`
- **Read**: `Reading: config.json (lines 10-50)`
- **Write**: `Writing: output.txt (25 lines)`
- **Bash**: `Running: npm run build`
- **WebFetch**: `Fetching: https://api.example.com`
- **MCP Tools**: `MCP filesystem: read_file`
- **Task**: `Subtask: Create new component for user authentication`

### Stop Event Handling

The Stop hook captures detailed information about why Claude Code stopped:

- **Usage Limits**: `⏳ Usage limit reached. Limit will reset at 1am`
- **Completion**: `✅ Task completed successfully`
- **Errors**: `❌ An error occurred during execution`
- **Analysis**: Examines transcript content for context

## 🔍 Debugging

### Log Files

- Main log: `/path/to/remocode.log`
- Check for JSON parsing issues and hook execution details

### Debug Mode

```bash
# Run Claude Code with debug output
claude --debug

# Check hook execution in transcript mode
# Press Ctrl-R in Claude Code to see hook progress
```

### Common Issues

1. **"Unknown Tool" in logs**: Old issue, fixed in v2
2. **Empty notifications**: Check Telegram token/chat configuration
3. **Hooks not triggering**: Verify `.claude/settings.local.json` configuration

## 📋 Next Steps

1. **Test with Real Claude Code**: Try various scenarios to ensure proper behavior
2. **Customize Tool Categories**: Adjust `AUTONOMOUS_TOOLS` and `DANGEROUS_BASH_PATTERNS` as needed
3. **Monitor Performance**: Check logs for any issues or improvements needed
4. **Extend Functionality**: Add more sophisticated permission logic if needed

## 🔒 Security Notes

- The system now auto-approves most operations for better UX
- Only genuinely dangerous operations require explicit permission
- All activity is logged and can be monitored via Telegram
- Raw JSON is always available for debugging and transparency
