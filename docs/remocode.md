# Design Document: **RemoCode** – Autonomous Multi-Agent Development Framework

> **Status:** Draft v2 – _supersedes previous drafts in `docs/feature_design_md/todo`_. The existing RemoCode prototype (local dual-control) remains functional; this document sets the roadmap to a production-ready, remote, multi-agent system with automated QA.

---

## 0 Background & Motivation

Modern AI coding assistants excel at _single-turn_ completions, but real-world development requires:

1. **Long-running tasks** – building features often exceeds token/time limits of a single chat.
2. **Parallelism** – multiple features in flight, each isolated and independently reviewable.
3. **Verifiable quality** – automated gates must replace ad-hoc human review when you are offline.
4. **Asynchronous control** – you should be able to kick off a task from your laptop, close it, board a flight, and still monitor / intervene from your phone.

**RemoCode** answers this by combining three battle-tested primitives:

- **<PERSON> (TUI)** – best-in-class interactive reasoning inside a Unix terminal.
- **Telegram <PERSON>t** – ubiquitous, low-latency notifications & interactive menus.
- **`tmux` + isolated git workspaces** – hermetic environments and live “jump-in” capability.

The diagram below shows how these pieces fit – every agent is just _Claude_ running inside a PTY; we duplicate its bytes to both the local terminal and Telegram.

```mermaid
graph TD
    subgraph Agent
      CW[ClaudeWrapper (PTY)] --> TG(TelegramBot)
      CW --> ST(StateManager)
    end

    Developer -- attach --> Agent
    Developer -- Telegram --> TG
```

---

## 1 Core Goals

1. **Asynchronous control** – primary UI is Telegram; first reply wins (terminal or bot).
2. **Hermetic execution** – each agent gets its own directory, git branch and `tmux` session.
3. **Verifiable quality** – multi-stage “review gauntlet” converges to `<<<PASS>>>` or `<<<REWORK>>>`.
4. **Human override** – attach to any session (`tmux a -t <name>` or browser via _gotty_).
5. **Incremental rollout** – ship in small, safe phases that never break existing flows.

---

## 2 Phases at a Glance

| Phase | Outcome                                     | Primary Tech       |
| ----- | ------------------------------------------- | ------------------ |
| 0     | Local single-agent dual-control (prototype) | `pty`, `termios`   |
| 1     | Local multi-agent launcher `start-agent.sh` | `tmux`             |
| 2     | Remote singleton with browser “jump-in”     | VPS, `gotty`       |
| 3     | Remote multi-agent team + review gauntlet   | `LangGraph`, Caddy |

Each phase maps to a PR / migration checklist so we can pause safely between milestones.

---

## 3 Phase 0 – Bootstrapping (DONE)

### 3.1 Quick-start

```bash
# setup
cd remocode && uv venv .venv && source .venv/bin/activate
uv pip install -r requirements.txt

# (optional) Telegram creds
export TG_TOKEN=XXX  # from @BotFather
export TG_CHAT=123   # chat id

# run
./remocode run              # REPL passthrough
./remocode run spec.md      # autonomous mode
```

### 3.2 File Layout

```
remocode/
├─ remocode              # launcher (shebang)
├─ requirements.txt
├─ src/
│  ├─ main.py            # orchestrator
│  ├─ claude_wrapper.py  # PTY ↔ Claude CLI
│  ├─ telegram_bot.py    # batching bot
│  ├─ state_manager.py   # JSONL session log
│  ├─ menu_detector.py   # parse numbered menus
│  └─ config.py          # Pydantic settings
└─ tests/ …
```

> **Metric:** < 100 ms TUI latency; Telegram edits < 1 req/sec to avoid rate-limit.

---

## 4 Phase 1 – Local Multi-Agent Launcher (IN PROGRESS)

### 4.1 Script `start-agent.sh`

```bash
./start-agent.sh feature/login docs/specs/login.md
```

1. Creates `~/coding-agents/feature/login`.
2. Clones repo, checks out new branch.
3. Copies `.env` + sets up venv if missing.
4. Launches `tmux new -d -s feature-login ./remocode/remocode run spec.md`.

### 4.2 Why `tmux`?

- Attach without killing the PTY – `tmux a -t feature-login`.
- Durable across SSH reconnects.
- Easy to enumerate sessions for monitoring (`tmux ls`).

> **Exit Criteria:** Can run 3+ agents locally without interfer-ence; each agent pushes to its own branch.

---

## 5 Phase 2 – Remote Singleton (“Fly & Forget”)

### 5.1 Server Checklist

| Step                | Command                                             |
| ------------------- | --------------------------------------------------- |
| Harden SSH          | `ufw allow 22/tcp` + keys only                      |
| Install deps        | `sudo apt install tmux node npm uv gotty caddy`     |
| Copy code           | `scp -r remocode user@vps:/srv/remocode`            |
| Copy Claude session | `scp ~/.config/claude/* user@vps:~/.config/claude/` |

### 5.2 Web Terminal

```bash
gotty --port 8080 --credential dev:pw tmux a -t feature-login
```

Share `https://<vps>:8080` for emergency debugging from any browser.

> **Exit Criteria:** Agent runs 24/7; Telegram still receives output; human can jump-in via browser.

---

## 6 Phase 3 – Remote AI Team & Review Gauntlet (PLANNED)

### 6.1 Live Preview via Caddy

_Each agent_ picks an open port (3001, 3002 …). `start-agent.sh` hits Caddy admin API to map `feature-x.domain.com` → `localhost:3001` (auto-HTTPS).

### 6.2 Automated Review (`run-review.sh`)

```mermaid
graph LR
    gitDiff[git diff] --> P1(Spec)
    gitDiff --> P2(Security)
    gitDiff --> P3(Best Practices)
    P1 & P2 & P3 --> L(Lead)
    L -->|<<<REWORK>>>| Remo(Remocode Rerun)
    L -->|<<<PASS>>>| Gates(Tests → Build → Notify)
```

- Orchestrated by **LangGraph**; max 3 cycles.
- Only on `<<<PASS>>>` do we run `npm test && npm build` and send final screenshot (Playwright) to Telegram.

---

## 7 Configuration Reference

| Env Var      | Default                         | Notes                    |
| ------------ | ------------------------------- | ------------------------ |
| `TG_TOKEN`   | _(empty)_                       | Skip to disable Telegram |
| `TG_CHAT`    | _(empty)_                       | Chat id                  |
| `CLAUDE_CMD` | `npx @anthropic-ai/claude-code` | CLI path                 |
| `LOG_LEVEL`  | `INFO`                          | Python logging           |

JSON-based overrides live in `config.json` (auto-created by `./remocode init`).

---

## 8 FAQ

**Why PTY over pexpect?** Cleaner EOF semantics, lower latency, fewer edge-cases.

**Where is state stored?** `.remocode/state/*.jsonl` per-agent (rolled + gzip).

**What if Telegram is down?** Terminal remains primary; bot fails closed (no crashes).

**Blocked ops?** `config.json.blocked_operations` defaults to `git_push`, `database_write`, `rm -rf /`.

---

## 9 Cut-over Checklist

- [ ] Phase 1 merged & Docs updated
- [ ] Phase 2 VPS deployed & smoke-tested
- [ ] Phase 3 scripts green on CI (`npm test`, `pytest`)

---

## 10 Appendix A – Single-Agent Sequence Diagram

```mermaid
sequenceDiagram
    participant Dev as Developer
    participant Term as Local Terminal
    participant Bot as TelegramBot
    participant Claude as ClaudeWrapper

    Dev->>Term: ./remocode run spec.md
    Term->>Claude: spawn CLI (PTY)
    Claude-->>Term: TUI bytes
    Claude-->>Bot: same bytes (batched)
    Dev-->>Bot: tap button
    Bot-->>Claude: stdin
```

---

_Enjoy building with a tireless AI pair-programmer!_
