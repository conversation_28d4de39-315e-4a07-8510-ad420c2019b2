# RemoCode v2 Usage Examples

This document demonstrates the enhanced features of RemoCode v2 with real-world examples.

## 🔧 Configuration Examples

### Basic Setup
```bash
# Set environment variables
export TG_TOKEN="your_telegram_bot_token"
export TG_CHAT="your_telegram_chat_id"

# Run in individual notification mode (default)
python3 main.py transparent
```

### Live Aggregation Mode
```bash
# Enable live message aggregation
export REMOCODE_LIVE_MODE="true"

# Run with live updates
python3 main.py transparent
```

## 📱 Telegram Interface Examples

### Individual Notification Mode (Default)

Each tool use creates a separate message:

```
🔄 Claude Code Activity (Session: abc123...)

🔧 Glob: Finding files matching: *.py (including hidden)

⏰ 14:30:15

🔧 Debug Payload:
{
  "session_id": "abc123...",
  "tool_name": "Glob",
  "tool_input": {
    "pattern": "*.py",
    "include_hidden": true
  }
}
```

```
🔄 Claude Code Activity (Session: abc123...)

🔧 Read: Reading: config.json (lines 10-50)

⏰ 14:30:18

🔧 Debug Payload:
{
  "session_id": "abc123...",
  "tool_name": "Read",
  "tool_input": {
    "file_path": "config.json",
    "view_range": [10, 50]
  }
}
```

### Live Aggregation Mode

Single message that updates with multiple activities:

```
🔄 Claude Code Live Activity (Session: abc123...)

⏰ 14:30:15 - 🔧 Glob: Finding files matching: *.py (including hidden)
⏰ 14:30:18 - 🔧 Read: Reading: config.json (lines 10-50)
⏰ 14:30:22 - 🔧 Write: Writing: output.txt (25 lines)
⏰ 14:30:25 - 🔧 Bash: Running: npm run build

📊 Total activities: 4
```

### Permission Request (Both Modes)

When genuine permission is needed, live mode clears and shows:

```
🔒 Claude Code Permission Request

⚠️ Potentially dangerous bash command:
command: `rm -rf /tmp/old-files`

This command could modify system state or delete data. Allow execution?

[✅ Allow] [⛔️ Deny] [🔒 Always allow this]

🔧 Debug Info:
{
  "session_id": "abc123...",
  "tool_name": "Bash",
  "tool_input": {
    "command": "rm -rf /tmp/old-files"
  }
}

⏰ 14:30:30
```

### Stop Event Notifications

#### Usage Limit Reached
```
🔴 Claude Code Usage Limit Reached (Session: abc123...)

⏳ Reason: Usage Limit
📝 Details: Usage limit reached. Limit will reset at 1am

⏰ 14:45:00

🔧 Debug Info:
{
  "session_id": "abc123...",
  "hook_event_name": "Stop",
  "stop_hook_active": false
}
```

#### Task Completion
```
🟢 Claude Code Task Completed (Session: abc123...)

✅ Reason: Completion
📝 Details: Task completed successfully

⏰ 15:20:30
```

#### Error Occurred
```
🔴 Claude Code Error Occurred (Session: abc123...)

❌ Reason: Error
📝 Details: An error occurred during execution

⏰ 14:35:15
```

## 🛠️ Enhanced Tool Summaries

### File Operations
- **Read**: `Reading: package.json`
- **Read with range**: `Reading: config.py (lines 25-100)`
- **Write**: `Writing: output.log (150 lines)`
- **Edit**: `Editing: main.py (50 lines)`
- **MultiEdit**: `Editing 3 files: main.py, config.py, utils.py`

### Search Operations
- **Glob**: `Finding files matching: *.js (including hidden)`
- **Grep**: `Searching for "TODO" in src/`
- **WebSearch**: `Searching: "Claude Code hooks" (10 results)`

### Development Tools
- **Bash**: `Running: git commit -m "Add new feature"`
- **Task**: `Subtask: Create authentication middleware for API endpoints`
- **WebFetch**: `Fetching: https://api.github.com/repos/user/repo`

### MCP Tools
- **MCP Filesystem**: `MCP filesystem: read_file`
- **MCP Memory**: `MCP memory: create_entities`
- **MCP GitHub**: `MCP github: search_repositories`

### Notebooks
- **NotebookRead**: `Reading notebook: analysis.ipynb`
- **NotebookEdit**: `Editing notebook: data_processing.ipynb`

## 🔍 Debugging Examples

### Raw JSON Payload
Every notification includes the complete raw payload for debugging:

```json
{
  "session_id": "abc123-def456-ghi789",
  "transcript_path": "/Users/<USER>/.claude/projects/my-project/transcript.jsonl",
  "cwd": "/Users/<USER>/projects/my-app",
  "hook_event_name": "PreToolUse",
  "tool_name": "MultiEdit",
  "tool_input": {
    "edits": [
      {
        "file_path": "src/main.py",
        "content": "# Updated main file\nprint('Hello World')"
      },
      {
        "file_path": "src/utils.py",
        "content": "# Utility functions\ndef helper(): pass"
      }
    ]
  }
}
```

### Log File Output
Check `/path/to/remocode.log` for detailed execution logs:

```
[pretool_guard] Hook Event: PreToolUse, Tool: Glob, Input: {'pattern': '*.py'}, Session: abc123...
[pretool_guard] Sent notification: Glob - Finding files matching: *.py
[stop_handler] Claude Code stopped: usage_limit - Usage limit reached. Limit will reset at 1am
```

## 🚀 Real-World Scenarios

### Scenario 1: Development Workflow
```
# Individual notifications show each step:
🔧 Glob: Finding files matching: *.py
🔧 Read: Reading: main.py
🔧 Edit: Editing: main.py (75 lines)
🔧 Bash: Running: python -m pytest tests/
🔧 Bash: Running: git add .
🔧 Bash: Running: git commit -m "Fix bug in authentication"

# Permission request for potentially dangerous operation:
🔒 Permission needed for: git push --force origin main
[User responds via Telegram]
```

### Scenario 2: Data Analysis Session
```
# Live mode shows aggregated activity:
⏰ 09:15:20 - 🔧 Read: Reading: data.csv
⏰ 09:15:25 - 🔧 NotebookEdit: Editing notebook: analysis.ipynb
⏰ 09:15:30 - 🔧 Bash: Running: python analyze.py
⏰ 09:15:45 - 🔧 Write: Writing: results.json (500 lines)
⏰ 09:16:00 - 🔧 WebFetch: Fetching: https://api.data-source.com

📊 Total activities: 5

# Stop notification when session ends:
🟢 Task Completed: Data analysis workflow finished successfully
```

### Scenario 3: Usage Limit Management
```
# Normal operation continues until limit hit:
🔧 Multiple tool activities...

# Stop notification with detailed context:
🔴 Usage Limit Reached
📝 Details: Claude usage limit reached. Your limit will reset at 1am
⏰ Current time: 23:45:30

# User can see exactly when to resume work
```

## 📋 Best Practices

1. **Use Live Mode for High-Activity Sessions**: Reduces Telegram message spam
2. **Keep Individual Mode for Debugging**: Better for detailed analysis
3. **Monitor Stop Events**: Stay informed about usage limits and errors
4. **Review Raw JSON**: Use debug payloads for troubleshooting hook issues
5. **Configure Permissions Carefully**: Adjust dangerous command patterns as needed

## 🔧 Troubleshooting

### Common Issues

1. **"No details available" in notifications**: Fixed in v2 with enhanced tool summaries
2. **Missing stop notifications**: Ensure Stop hook is configured in settings.local.json
3. **Live mode not updating**: Check REMOCODE_LIVE_MODE environment variable
4. **Permission requests timing out**: Increase timeout in notification_handler.py

### Debug Commands

```bash
# Test individual hooks
python3 hooks/pretool_guard.py < test_payload.json
python3 hooks/stop_handler.py < stop_payload.json

# Run comprehensive tests
python3 test_improved_hooks.py

# Check hook configuration
cat .claude/settings.local.json | jq '.hooks'
```
