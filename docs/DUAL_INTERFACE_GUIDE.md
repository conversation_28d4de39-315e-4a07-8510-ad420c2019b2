# RemoCode v2 Dual Interface System Guide

## Overview

RemoCode v2 implements a sophisticated **dual interface system** that allows you to interact with <PERSON> through both the terminal and Telegram simultaneously. The system ensures that **the first response wins** while maintaining complete transparency and safety.

## Core Architecture

### Transparent Extension Design

RemoCode v2 functions as a **completely non-intrusive extension** to Claude Code:

- ✅ **Zero Impact**: Claude Code functionality remains 100% unchanged
- ✅ **Terminal Priority**: All existing terminal workflows continue to work
- ✅ **Permission Preservation**: <PERSON>'s built-in permission system remains active
- ✅ **Safe Fallback**: System defaults to <PERSON>'s original behavior

### Hook-Based Integration

The system uses Claude <PERSON>'s official hook system:

- **PreToolUse**: Intercepts tool calls for safety evaluation
- **Notification**: Handles Claude Code notifications and idle states
- **Stop**: Manages session completion and cleanup

## Dual Interface Coordination

### First Response Wins Mechanism

```mermaid
graph TD
    A[Claude Code Requests Permission] --> B[Display in Terminal]
    A --> C[Display in Telegram]
    B --> D{User Responds?}
    C --> D
    D -->|Terminal First| E[Terminal Response Wins]
    D -->|Telegram First| F[Telegram Response Wins]
    E --> G[Cleanup Telegram UI]
    F --> H[Continue Terminal Flow]
    G --> I[Claude Code Continues]
    H --> I
```

### Race Condition Handling

The system implements atomic response coordination:

1. **Response Lock**: First interface to respond claims the lock
2. **State Synchronization**: Other interface is immediately notified
3. **UI Cleanup**: Losing interface updates to show the winning response
4. **Graceful Continuation**: Claude Code receives the response and continues

### Interface Synchronization

When one interface responds:

- **Telegram Buttons Disappear**: If terminal responds first
- **Terminal Notification**: If Telegram responds first
- **Status Updates**: Both interfaces show the final decision
- **Clean State**: All coordination files are cleaned up

## Safety Mechanisms

### Strict Safety Controls

RemoCode v2 implements enhanced safety mechanisms:

```python
# Example: Dangerous command detection
DANGEROUS_BASH_PATTERNS = [
    r"^rm -rf.*",      # File deletion
    r"^sudo .*",       # System operations
    r"^git push.*",    # Deployments
    r"^docker run.*",  # Container operations
]
```

### No Auto-Approval Policy

- **🚨 Dangerous Operations**: Always require explicit user approval
- **📋 Plan Execution**: Manual approval required before making code changes
- **⏸️ Idle States**: User must actively choose to continue or stop
- **❓ Unknown Tools**: Monitored and logged, but user retains control

### Safety Checkpoints

1. **Plan Approval**: Before Claude Code exits planning mode
2. **Dangerous Commands**: Before executing risky bash operations
3. **System Operations**: Before sudo, deployment, or destructive commands
4. **Unknown Tools**: Before using unrecognized tools

## Progress Visibility

### Real-Time Updates

Telegram provides comprehensive progress visibility:

- **🔄 Live Activity Feed**: Real-time tool usage updates
- **📊 Progress Indicators**: Visual status of Claude Code operations
- **⏰ Timestamps**: Precise timing of all operations
- **🎯 Tool Details**: Detailed information about each tool call

### Activity Monitoring

```
🔧 Claude Code Progress (Session: abc123...)

Action: Reading configuration file
Tool: Read
Time: 14:32:15
Status: ✅ Auto-approved
Details: Reading: `config.json`
```

### Session Management

- **🚀 Startup Messages**: Notification when RemoCode begins
- **👋 Farewell Messages**: Automatic notification when sessions end
- **📈 Session Statistics**: Summary of operations performed
- **🔒 Security Summary**: Report of approvals and denials

## Usage Examples

### Typical Workflow

1. **Start RemoCode**: `uv run remocode transparent`
2. **Use Claude Code Normally**: All terminal interactions work as before
3. **Receive Mobile Notifications**: Progress updates appear in Telegram
4. **Respond from Either Interface**: Terminal or Telegram - first wins
5. **Automatic Cleanup**: Session ends with farewell message

### Permission Request Flow

```
Terminal: Claude Code asks for permission to run `git push origin main`
Telegram: 🤖 Claude needs permission
          ⚠️ DANGEROUS COMMAND DETECTED
          Command: git push origin main
          [✅ Push now] [📝 Review first] [⛔️ Cancel]

User clicks "Push now" in Telegram
→ Terminal immediately shows: "Permission granted via Telegram"
→ Claude Code continues with git push
→ Telegram buttons disappear, showing "✅ Approved"
```

### Idle State Handling

```
Claude Code: Waiting for user input...
Telegram: ⏸️ Claude Code Idle
          Claude is waiting for your input
          [⏰ Continue] [🛑 Stop]

User types in terminal OR clicks Telegram button
→ First response wins
→ Claude Code resumes with the input
```

## Configuration

### Environment Variables

```bash
# Required for Telegram integration
TG_TOKEN=your_bot_token
TG_CHAT=your_chat_id

# Optional: Enable RemoCode hooks
REMOCODE_ENABLED=true

# Optional: Enable live message aggregation
REMOCODE_LIVE_MODE=true
```

### Hook Configuration

RemoCode automatically configures Claude Code hooks in `.claude/settings.local.json`:

```json
{
  "hooks": {
    "PreToolUse": [
      {
        "hooks": [
          {
            "type": "command",
            "command": "python3 /path/to/pretool_guard.py"
          }
        ]
      }
    ],
    "Notification": [...],
    "Stop": [...]
  }
}
```

## Troubleshooting

### Common Issues

1. **Hooks Not Working**: Ensure `REMOCODE_ENABLED=true` is set
2. **Telegram Not Responding**: Check `TG_TOKEN` and `TG_CHAT` values
3. **Race Conditions**: System handles these automatically with atomic locks
4. **Permission Conflicts**: First response always wins, others are cleaned up

### Debug Mode

Enable detailed logging:

```bash
export REMOCODE_LOG_PATH=/path/to/debug.log
uv run remocode transparent --verbose
```

### State Files

RemoCode uses temporary state files for coordination:

- `state/menu.json`: Active menu state
- `state/notification.json`: Notification state
- `state/response.json`: Response coordination
- `state/response.lock`: Atomic response lock

These are automatically cleaned up after each operation.

## Best Practices

### For Developers

1. **Review Plans Carefully**: Always check Claude's plan before approving execution
2. **Use Terminal for Complex Input**: Long commands are easier in terminal
3. **Monitor Telegram for Progress**: Keep an eye on what Claude is doing
4. **Trust the Safety System**: Dangerous operations will always ask for approval

### For Teams

1. **Shared Telegram Bots**: Each developer should have their own bot
2. **Environment Isolation**: Use separate configurations per environment
3. **Security Policies**: Establish team guidelines for approval workflows
4. **Monitoring**: Review logs regularly for security and usage patterns

## Advanced Features

### Live Message Aggregation

Enable with `REMOCODE_LIVE_MODE=true` for consolidated progress updates:

```
🔄 Claude Code Live Activity (Session: abc123...)

⏰ 14:30:15 - Reading configuration file
⏰ 14:30:16 - Searching for dependencies
⏰ 14:30:18 - Running npm install
⏰ 14:30:25 - Building project

📊 Total activities: 4
```

### Custom Safety Patterns

Extend the safety system by modifying `DANGEROUS_BASH_PATTERNS` in `pretool_guard.py`:

```python
# Add custom dangerous patterns
DANGEROUS_BASH_PATTERNS.extend([
    r"^kubectl delete.*",     # Kubernetes deletions
    r"^terraform destroy.*",  # Infrastructure destruction
    r"^npm publish.*",        # Package publishing
])
```

### Integration with CI/CD

RemoCode can be integrated into CI/CD pipelines for remote approval workflows:

```yaml
# GitHub Actions example
- name: Deploy with RemoCode
  run: |
    export REMOCODE_ENABLED=true
    uv run remocode transparent -- claude "Deploy to production"
```

This enables mobile approval of deployment operations even in automated environments.
