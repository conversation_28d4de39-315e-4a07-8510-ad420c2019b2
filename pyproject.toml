[project]
name = "remocode"
version = "2.0.0"
description = "Remote Claude Code Control via Telegram - Hook-based implementation"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "python-telegram-bot>=21.0.1",
    "python-dotenv>=1.0.0",
    "pydantic>=2.0.0",
    "rich>=13.0.0",
    "typer>=0.12.0",
    "psutil>=7.0.0",
    "httpx>=0.28.1",
    "watchdog>=6.0.0",
    "fastapi>=0.104.0",
    "uvicorn>=0.24.0",
]

[project.scripts]
remocode = "remocode.main:app"  # Only v2, no fallbacks

[project.optional-dependencies]
dev = [
    "pytest>=8.0.0",
    "pytest-asyncio>=0.23.0",
    "black>=24.0.0",
    "isort>=5.13.0",
    "mypy>=1.8.0",
    "pre-commit>=3.6.0",
    "ruff>=0.1.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  reference/
  | archive/
)/
'''

[tool.isort]
profile = "black"
line_length = 88
skip_glob = ["reference/*", "archive/*"]

[tool.ruff]
line-length = 88
target-version = "py311"
extend-exclude = ["reference", "archive"]

[tool.mypy]
python_version = "3.11"
warn_return_any = false
warn_unused_configs = false
disallow_untyped_defs = false
check_untyped_defs = false
exclude = ["reference/", "archive/"]
explicit_package_bases = true
mypy_path = "src"
namespace_packages = true
ignore_missing_imports = true
follow_imports = "silent"
show_error_codes = false
warn_unused_ignores = false
no_implicit_optional = false

[dependency-groups]
dev = [
    "ruff>=0.12.4",
]
