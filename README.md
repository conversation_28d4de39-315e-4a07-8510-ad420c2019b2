# RemoCode v2 - Enhanced Dual Interface for Claude Code

RemoCode v2 is an advanced hook-based system that provides a **transparent dual interface** for Claude Code, enabling seamless interaction via both terminal and Telegram. Experience true "first response wins" coordination between interfaces while maintaining Claude Code's safety mechanisms.

## ✨ Enhanced Features

- **🔄 Dual Interface System**: True "first response wins" between Telegram and terminal
- **🛡️ Transparent Extension**: Zero impact on Claude Code functionality - works exactly as normal
- **⚡ Enhanced Hook Architecture**: Uses Claude Code's official hook system with improved coordination
- **🎯 Smart Safety Logic**: Distinguishes between safe auto-approved tools and dangerous operations
- **📱 Mobile Decision Interface**: Respond to permission requests from your phone
- **📊 Real-time Progress Updates**: Live visibility into Claude Code operations via Telegram
- **🔒 Enhanced Safety Mechanisms**: Strict controls with no unintended auto-approvals
- **👋 Farewell Messages**: Automatic notification when RemoCode sessions end
- **🔧 Pre-commit Integration**: Automated code formatting and linting

## 🚀 Quick Start

### Prerequisites

- Python 3.11+
- UV package manager
- Telegram bot token (from @BotFather)
- Your Telegram chat ID (from @userinfobot)

### Installation

```bash
# Clone and navigate to repository
git clone <repository-url>
cd remocode

# Install dependencies
uv sync

# Set up Claude Code hooks (automatically configures paths)
./setup_hooks.sh

# Copy environment template and configure Telegram credentials
cp .env.example .env
# Edit .env with your Telegram bot token and chat ID

# That's it! No additional setup required.
```

## 🔧 Development

### Formatting and Linting

```bash
# Format code
uv run black src/ tests/
uv run isort src/ tests/
uv run ruff check --fix src/ tests/

# Check formatting
uv run black --check src/ tests/
uv run ruff check src/ tests/
uv run mypy src/

# Run tests
uv run pytest tests/

# Install pre-commit hooks
uv run pre-commit install
```

### Project Structure

```
remocode/                           # Root project directory
├── pyproject.toml                  # Main UV project config
├── .pre-commit-config.yaml         # Pre-commit hooks
├── .env.example                    # Environment template
├── src/remocode/                   # Main package
│   ├── main.py                     # Entry point
│   ├── hooks/                      # Claude Code hooks
│   ├── adapters/                   # External service adapters
│   └── ...                         # Core modules
├── tests/                          # Test suite
├── docs/                           # Documentation
├── reference/v1/                   # V1 code (reference only)
└── archive/                        # Legacy code archive
```

## 📱 Usage

### Transparent Mode (Recommended)

```bash
uv run remocode transparent
```

**How the Dual Interface Works:**

1. **🖥️ Terminal Interface**: Claude Code works exactly as normal - all existing functionality preserved
2. **📱 Telegram Interface**: Parallel mobile interface for approvals and monitoring
3. **🏁 First Response Wins**: Whichever interface responds first takes precedence
4. **🔄 Interface Synchronization**: When one interface responds, the other is automatically updated
5. **⏸️ Idle State Handling**: Users can respond via Telegram to resume Claude Code execution
6. **🛡️ Safety First**: No auto-approvals for dangerous operations - user must explicitly decide

### Simplified Activation

- **🚀 `uv run remocode transparent`**: Activates RemoCode + Claude Code with dual interface
- **💻 `claude`**: Pure Claude Code with zero RemoCode interference
- **🔄 Automatic**: No environment variables or manual setup required

### Key Behaviors

- **Approval Requests**: Displayed simultaneously in both terminal and Telegram
- **Progress Updates**: Real-time Claude Code activity streamed to Telegram
- **Safety Checkpoints**: Critical operations (like plan execution) require explicit approval
- **Zero Interference**: Running `claude` directly has no RemoCode effects
- **Farewell Messages**: Automatic notification when sessions end

### Configuration

The system automatically configures Claude Code hooks in `.claude/settings.local.json`. See `docs/README.md` for detailed usage instructions.

## 🔒 Enhanced Security & Safety

### Core Safety Principles

- **🛡️ No Silent Auto-Approvals**: Dangerous operations always require explicit user consent
- **🔄 Transparent Extension**: Zero modification to Claude Code's core safety mechanisms
- **⚡ Safe-by-Default**: System fails safely by defaulting to Claude Code's original behavior
- **🎯 Selective Intervention**: Only intercepts genuinely dangerous operations

### Safety Mechanisms

- **Dangerous Command Detection**: Automatic detection of risky bash commands (rm -rf, sudo, etc.)
- **Plan Execution Checkpoints**: Explicit approval required before Claude Code exits planning mode
- **First Response Wins**: Race condition handling prevents conflicting approvals
- **Activity Logging**: All operations logged locally and monitored via Telegram
- **Environment Protection**: Secrets never committed to version control
- **Pre-commit Validation**: Automated security and quality checks

### What Requires Approval

- 🚨 **Dangerous bash commands** (file deletion, system modifications, deployments)
- 📋 **Plan execution** (exiting planning mode to make actual code changes)
- 🔧 **Unknown tools** (with monitoring and transparency)

### What's Auto-Approved

- ✅ **Safe development tools** (reading files, searching, common build commands)
- ✅ **Web operations** (fetching documentation, searching)
- ✅ **Navigation commands** (listing directories, finding files)

## 📚 Documentation

- [Usage Examples](docs/USAGE_EXAMPLES.md)
- [V2 Documentation](docs/README.md)

## 🤝 Contributing

1. Install development dependencies: `uv sync`
2. Install pre-commit hooks: `uv run pre-commit install`
3. Make changes and ensure tests pass: `uv run pytest`
4. Format code: `uv run black src/ tests/ && uv run isort src/ tests/`
5. Submit pull request
