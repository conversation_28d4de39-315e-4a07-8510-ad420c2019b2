#!/usr/bin/env python3
"""
Simple test for RemoCode v4 fixes
Tests that <PERSON> interface appears and Ctrl+C works
"""

import asyncio
import sys
from pathlib import Path

# Add src directory to path
sys.path.append(str(Path(__file__).parent / "src"))

from remocode.v4.core.claude_controller import <PERSON><PERSON><PERSON><PERSON><PERSON>


async def test_claude_interface():
    """Test that <PERSON> interface appears in terminal"""
    print("🧪 Testing Claude interface display...")

    # Test with echo command first to verify subprocess works
    controller = <PERSON>Controller("echo 'Testing subprocess...'")

    try:
        print("📋 Starting echo test...")

        # Start controller
        start_task = asyncio.create_task(controller.start())

        # Wait a moment
        await asyncio.sleep(2)

        # Stop controller
        await controller.stop()

        print("✅ Echo test completed - subprocess working")
        return True

    except Exception as e:
        print(f"❌ Echo test failed: {e}")
        return False


async def main():
    """Run simple test"""
    print("🚀 RemoCode v4 Simple Test")
    print("=" * 40)

    success = await test_claude_interface()

    if success:
        print("\n✅ Basic subprocess test passed")
        print("\n🎯 Now try the real command:")
        print("   uv run remocode v4 --no-telegram")
        print("\n📋 You should see:")
        print("   1. RemoCode startup messages")
        print("   2. Claude Code interface appears")
        print("   3. Ctrl+C works to exit")
    else:
        print("\n❌ Basic test failed - check the implementation")

    return success


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
