#!/usr/bin/env python3
"""
Test script to verify plan context detection in RemoCode v3.
"""

import asyncio
import json
from pathlib import Path

from remocode.v3.bridges.telegram_bridge import TelegramBridge
from remocode.v3.core.transcript_tailer import TranscriptTailer
from remocode.v3.utils.session_utils import SessionInfo


class MockArgs:
    """Mock args object for testing."""

    def __init__(self):
        self.minimal_mode = False
        self.show_session_info = True
        self.show_full_json = False


async def test_plan_detection():
    """Test plan context detection with a sample assistant event."""

    # Create a mock session info
    session_info = SessionInfo(
        session_id="test-session-123",
        project_path="/Users/<USER>/Desktop/github/remocode",
        transcript_path=Path("/tmp/test-transcript.jsonl"),
    )

    # Create mock args
    mock_args = MockArgs()

    # Create telegram bridge (won't actually send since we're testing)
    telegram_bridge = TelegramBridge(mock_args)

    # Create transcript tailer
    tailer = TranscriptTailer(session_info, telegram_bridge, None, mock_args)

    # Test event with plan context
    test_event = {
        "type": "assistant",
        "message": {
            "content": [
                {
                    "type": "text",
                    "text": "I'll create a plan for implementing a complete QuickSort algorithm in a quicksort.py file.\n\nHere's my plan:\n1. Create quicksort.py file\n2. Implement main quicksort function\n3. Add test cases\n\nWould you like to proceed with this implementation?",
                }
            ]
        },
    }

    print("Testing plan context detection...")
    print(f"Event: {json.dumps(test_event, indent=2)}")

    # Process the event
    await tailer._check_for_plan_context(test_event)

    print("Plan context detection test completed!")


if __name__ == "__main__":
    asyncio.run(test_plan_detection())
